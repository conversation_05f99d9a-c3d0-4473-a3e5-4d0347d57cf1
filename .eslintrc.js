module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    // "vue/html-indent": ["error", 2, {
    //   "attribute": 1,
    //   "baseIndent": 1,
    //   "closeBracket": 0,
    //   "alignAttributesVertically": true,
    //   "ignores": []
    // }],
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    "no-unused-vars": [0]
  }
}
