stages:
  - build-npm
  - build-image
  - deploy

# Build program
.build-npm:
  stage: build-npm
  image: registry.cn-shenzhen.aliyuncs.com/eddid-dev/node-cicd:10.23.0-alpine3.11-cicd.1
  variables:
    BUILD_ENV: 'dev qa uat sit prd'
  cache:
    key: node_cache
    paths:
      - .npm
  script:
    - exit_code=0
    - npm set registry https://registry.npmmirror.com
      && npm --cache .npm --prefer-offline install
      && rm -rf dist-all dist && mkdir dist-all
      && (for var in ${BUILD_ENV}; do npm --cache .npm --prefer-offline run "build:${var}"
      && mv dist dist-all/${var} || exit 1; done)
      || exit_code=$?
    - if [ $exit_code -ne 0 ]; then .gitlab-ci/dingding.sh build-failure; fi;
    - exit $exit_code
  artifacts:
    paths:
      - .gitlab-ci/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}
      - dist-all
    expire_in: 30 days

tags-build-npm:
  extends: .build-npm
  only:
    refs:
      - tags
  before_script:
    - .gitlab-ci/dingding.sh start
    - sh .gitlab-ci/docker-registry.sh getManifest "${CI_COMMIT_TAG}"
      && touch .gitlab-ci/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}/.image-tag-exists && echo "Image tag exists." && exit 0 || true
    - sh .gitlab-ci/docker-registry.sh tagManifest "${CI_COMMIT_SHA}" "${CI_COMMIT_TAG}"
      && echo "Exist cache, skip build." && exit 0 || true

develop-build-npm:
  extends: .build-npm
  only:
    refs:
      - develop
  before_script:
    - sh .gitlab-ci/docker-registry.sh getManifest "${CI_COMMIT_SHA}"
      && echo "Exist cache, skip build." && exit 0 || true

# Build image
.build-image:
  image:
    name: gcr.io/kaniko-project/executor:debug-v1.3.0
    entrypoint: ['']
  stage: build-image
  variables:
    # CACHE_IMAGES: '--image=registry.cn-shenzhen.aliyuncs.com/eddid-ops/node:12.18.3-alpine'
    CACHE_IMAGES: "--image=registry.cn-shenzhen.aliyuncs.com/eddid-ops/nginx:1.19.4-alpine"
  cache:
    key: docker-image
    paths:
      - .kaniko-cache
  before_script:
    - true && [ -f ".gitlab-ci/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}/${IMAGE_TAG}.manifest" ]
      && echo "User Image Cache." && exit 0 || true
  script:
    - exit_code=0
    - echo "{\"auths\":{\"$ALIYUN_REGISTRY\":{\"auth\":\"$ALIYUN_REGISTRY_AUTH\"}}}" > /kaniko/.docker/config.json
      && /kaniko/warmer --cache-dir=$CI_PROJECT_DIR/.kaniko-cache $CACHE_IMAGES
      && /kaniko/executor --registry-mirror=docker.m.daocloud.io --cache=true --cache-dir=$CI_PROJECT_DIR/.kaniko-cache
      --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/Dockerfile
      --destination "$ALIYUN_REGISTRY/$ALIYUN_REGISTRY_NAMESPACE/$CI_PROJECT_PATH_SLUG:$IMAGE_TAG"
      || exit_code=$?
    - if [ "$exit_code" -ne 0 ]; then sh .gitlab-ci/dingding.sh build-image-failure;
      else touch .gitlab-ci/.build-image-success; fi;
    - exit "$exit_code"

deploy-build-image:
  extends: .build-image
  variables:
    IMAGE_TAG: $CI_COMMIT_SHA
  only:
    refs:
      - develop

tags-build-image:
  extends: .build-image
  variables:
    IMAGE_TAG: $CI_COMMIT_TAG
  only:
    refs:
      - tags
  after_script:
    - if [ -f ".gitlab-ci/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}/.image-tag-exists" ]; then
      sh .gitlab-ci/dingding.sh image-done-with-tag-exists;
      elif [ -f ".gitlab-ci/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}/${IMAGE_TAG}.manifest" ]; then
      sh .gitlab-ci/dingding.sh image-done-with-cache;
      elif [ -f ".gitlab-ci/.build-image-success" ]; then
      sh .gitlab-ci/dingding.sh image-done;
      fi;

# Deploy
.deploy:
  stage: deploy
  image: registry.cn-shenzhen.aliyuncs.com/eddid-dev/helm-push:3.3.0
  variables:
    HELM_CHART_NAME: fund-h5
    DEPLOYMENTCHART_VERSION: 1.7.0-cicd.5
  script:
    - exit_code=0
    - echo $K8S_CONFIG | base64 -d > .kubeconfig
      && helm repo add $ALI_HELM_REPO_NAMESPACE $ALI_HELM_REPO_URL/$ALI_HELM_REPO_NAMESPACE
      --username=$ALI_HELM_USERNAME --password=$ALI_HELM_PASSWD
      && helm repo update
      && helm upgrade $CI_PROJECT_PATH_SLUG $ALI_HELM_REPO_NAMESPACE/${HELM_CHART_NAME}
      --namespace ${K8S_NAMESPACE} --version $DEPLOYMENTCHART_VERSION -f ${VALUES_FILE}
      --set-string image.tag=$IMAGE_TAG --install --kubeconfig .kubeconfig
      || exit_code=$?
    - if [ $exit_code -ne 0 ]; then .gitlab-ci/dingding.sh deploy-failure;
      else .gitlab-ci/dingding.sh deploy-done;
      fi;
    - exit "$exit_code"

develop-deploy:
  extends: .deploy
  variables:
    IMAGE_TAG: $CI_COMMIT_SHA
    VALUES_FILE: .gitlab-ci/values-develop.yaml
    K8S_NAMESPACE: develop
  only:
    refs:
      - develop
