#!/bin/bash
set -o errexit -o pipefail

# wget do not support http 'PUT' method, so use curl

CURRENT_DIR="$(dirname "$0")"
MANIFESTS_DIR="${CURRENT_DIR}/${DOCKER_REGISTRY_MANIFESTS_CACHE_FOLDER}"
mkdir -p "${MANIFESTS_DIR}"

getToken()
{

USERNAME=$(echo "$ALIYUN_REGISTRY_AUTH" | base64 -d | cut -d':' -f1)
PASSWORD=$(echo "$ALIYUN_REGISTRY_AUTH" | base64 -d | cut -d':' -f2)

JSON_RESULT=$(wget -qO- --post-data="username=${USERNAME}&password=${PASSWORD}\
&service=registry.aliyuncs.com:cn-shenzhen:26842\
&scope=repository:${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}:pull,push" \
"$ALIYUN_REGISTRY_AUTH_URL") \
|| \
JSON_RESULT=$(curl --fail -sS --data "username=${USERNAME}&password=${PASSWORD}\
&service=registry.aliyuncs.com:cn-shenzhen:26842\
&scope=repository:${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}:pull,push" \
"$ALIYUN_REGISTRY_AUTH_URL")

echo "$JSON_RESULT" | sh "${CURRENT_DIR}/JSON.sh" -l -s | sed -nE 's/^\[\"token\"\]\s+\"(.+)\"$/\1/p'

}

getManifest()
{

TAG=$1
TOKEN=$(getToken)

if [ -f "${MANIFESTS_DIR}/${TAG}.manifest" ]; then MANIFEST=$(cat "${MANIFESTS_DIR}/${TAG}.manifest"); \
else false; fi \
|| \
MANIFEST=$(wget -qO- --header='Accept: application/vnd.docker.distribution.manifest.v2+json' \
--header="Authorization: Bearer ${TOKEN}" \
"https://${ALIYUN_REGISTRY}/v2/${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}/manifests/${TAG}") \
|| \
MANIFEST=$(curl -sS --fail --header 'Accept: application/vnd.docker.distribution.manifest.v2+json' \
--header "Authorization: Bearer ${TOKEN}" \
"https://${ALIYUN_REGISTRY}/v2/${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}/manifests/${TAG}")

echo "${MANIFEST}" > "${MANIFESTS_DIR}/${TAG}.manifest"
echo "${MANIFEST}"

}

tagManifest()
{
OLD_TAG=$1
NEW_TAG=$2
TOKEN=$(getToken)

MANIFEST=$(getManifest "${OLD_TAG}")

echo "${MANIFEST}" > "${MANIFESTS_DIR}/${OLD_TAG}.manifest"

curl -sS --fail -X PUT --data "${MANIFEST}" \
--header "Authorization: Bearer ${TOKEN}" \
--header "Content-Type: application/vnd.docker.distribution.manifest.v2+json" \
"https://${ALIYUN_REGISTRY}/v2/${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}/manifests/${NEW_TAG}"

echo "${MANIFEST}" > "${MANIFESTS_DIR}/${NEW_TAG}.manifest"

}

existManifest()
{

TAG=$1
TOKEN=$(getToken)

if [ -f "${MANIFESTS_DIR}/${TAG}.manifest" ]; then exit 0; else false; fi \
|| \
curl -sS --fail --head --header 'Accept: application/vnd.docker.distribution.manifest.v2+json' \
--header "Authorization: Bearer ${TOKEN}" \
"https://${ALIYUN_REGISTRY}/v2/${ALIYUN_REGISTRY_NAMESPACE}/${CI_PROJECT_PATH_SLUG}/manifests/${TAG}" \
|| \
getManifest "${TAG}"

}

case "$1" in
    "getManifest") getManifest "$2"
        ;;
    "tagManifest") tagManifest "$2" "$3"
        ;;
    "existManifest") existManifest "$2"
        ;;
esac


