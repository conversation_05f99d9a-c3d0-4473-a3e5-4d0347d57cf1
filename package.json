{"name": "fund-h5", "version": "0.1.0", "private": true, "scripts": {"start": "npm run local", "local": "vue-cli-service serve --mode qa", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:preview": "vue-cli-service build --mode preview", "build:dev": "vue-cli-service build --mode develop", "build:uat": "vue-cli-service build --mode uat", "build:qa": "vue-cli-service build --mode qa", "build:sit": "vue-cli-service build --mode sit", "build:prd": "vue-cli-service build"}, "dependencies": {"@antv/f2": "^3.8.10-beta.1", "axios": "^0.24.0", "callapp-lib": "^3.5.3", "core-js": "^3.6.5", "dingtalk-jsapi": "^3.0.10", "fingerprintjs2": "^2.1.4", "jsencrypt": "^3.3.2", "jwt-decode": "^3.1.2", "mand-mobile": "^2.5.20", "moment": "^2.29.1", "sa-sdk-javascript": "^1.24.2", "store": "^2.0.12", "vconsole": "^3.9.5", "vee-validate": "2.1.7", "vue": "^2.6.11", "vue-i18n": "^8.26.7", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "postcss-pxtorem": "^5.1.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.11"}}