<template>
  <div id="app" :data-theme="theme" class="app" :class="[theme, $route.path.includes('/international') ? 'international' : '']">
    <router-view />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getAppToken } from '@/utils/auth';

export default {
  data() {
    return {
    }
  },
  created() {
    if(this.$jsBridge.isSupported('getAppInfo')){
      getAppToken();
      this.$jsBridge.run('getAppInfo', {
        /**
         * redRiseGreenFall: 红涨绿跌
         * greenRiseRedFall: 绿涨红跌
         */
        callback: ({ klineTheme, isInternational }) => {
          this.$store.commit('setKlineTheme', klineTheme);
          this.$store.commit('setIsInternational', isInternational);
          localStorage.setItem('klineTheme', klineTheme)
        }
      })
    }else { // 本地运行项目
      this.$store.commit('setKlineTheme', 'redRiseGreenFall');
      localStorage.setItem('klineTheme', 'redRiseGreenFall');

      const { inter } = this.$route.query;
      if(inter === 'inter') {
        this.$store.commit('setIsInternational', true);
        localStorage.setItem('isInternational', true);
      }
    }
  },
  mounted(){},
  computed:{
    ...mapState(['theme', 'token']),
  },
  watch: {
    $route:{
      handler(to, form){
        document.title = to.meta.title ? to.meta.title : '';
        if (to.path.split('/').includes('international')) {
          document.querySelector('body').setAttribute('class', 'international');
          document.querySelector('body').removeAttribute('data-theme')

          // Update the theme color configuration
          this.$themeDATA[this.theme] && Object.keys(this.$themeDATA[this.theme]).forEach((item) => {
            document.documentElement.style.setProperty(item, this.$themeDATA[this.theme][item]);
          });
        }else {
          document.querySelector('body').setAttribute('data-theme', this.theme);
          document.querySelector('body').removeAttribute('class')
        }
      }
    }
  }
}
</script>

<style lang="scss">
html {
  @include themeify {
    box-sizing: border-box;
    font-size: 28px;
    word-spacing: 1px;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
  }

  * {
    -moz-user-select: none;
    -o-user-select:none;
    -khtml-user-select:none;
    -webkit-user-select:none;
    -ms-user-select:none;
    user-select:none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  input,
  textarea {
    -moz-user-select: text;
    -o-user-select: text;
    -khtml-user-select: text;
    -ms-user-select: text;
    -webkit-user-select: text;
    user-select: text;
  }

  *::before,
  *::after {
    box-sizing: border-box;
    margin: 0;
  }
}

.app{
  @include background_color(bg-color);
  min-height: 100vh;
  padding-top: 1px;
  font-family: D-DIN, D, PingFangSC-Regular, PingFang SC, sans-serif;
}
</style>
