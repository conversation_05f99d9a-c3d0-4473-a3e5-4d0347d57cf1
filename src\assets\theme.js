export const themeDATA = {
  light: {
    // 中性色
    '--gray_01': '#4B596B',
    '--gray_02': '#C8CDD5',
    '--gray_03': '#E5E6EC',
    '--gray_04': '#F2F3F5',
    '--gray_05': '#F7F8FA',

    // 分割线
    '--line_01': '#E5E6EB',
    '--line_02': '#C8CDD5',

    // 品牌色
    '--brand_01': '#012169',
    '--brand_02': '#3D63A5',
    '--brand_03': '#6A8EC3',
    '--brand_04': '#A4BEE1',
    '--brand_05': '#E8F3FF',
    '--brand_06': '#F9FBFF',
    '--brand_07': '#F8D000',
    '--brand_opacity_01': '#012169',
    '--brand_opacity_02': '#F8D000',

    // 数据可视化
    '--data_01': '#5B8FF9',
    '--data_02': '#61DDAA',
    '--data_03': '#65789B',
    '--data_04': '#F6BD16',
    '--data_05': '#7262FD',
    '--data_06': '#78D3F8',
    '--data_07': '#9661BC',
    '--data_08': '#F6903D',
    '--data_09': '#008685',
    '--data_10': '#F08BB4',

    // 文字色
    '--text_1st': '#1C212A',
    '--text_2nd': '#4B596B',
    '--text_3rd': '#83909D',
    '--text_4rd': '#B6C2D0',
    '--text_5th': '#F1F3F7',
    '--text_disabled': '#C8CDD5',
    '--text_opacity': 'rgba(241, 243, 247, 0.6)',
    '--text_3rd_opacity': 'rgba(131, 144, 157, 0.15)',

    // 涨跌色
    '--green': '#0ACCA0',
    '--red': '#FF0B34',

    // 背景色
    '--background': '#FFFFFF',

    // 功能色
    '--error': '#FF4D4F',
    '--link': '#178FFF',
    '--success': '#52C41A',
    '--warning': '#FAAD13',
    '--warning_opacity': 'rgba(246, 189, 22, 0.15)',
    '--error_opacity': 'rgba(255, 77, 79, 0.15)',
    // 遮罩
    '--mask': 'rgba(0,0,0,0.25)',
  },
  dark: {
    // 中性色
    '--gray_01': '#b6c2d0',
    '--gray_02': '#434b55',
    '--gray_03': '#20232c',
    '--gray_04': '#181b22',
    '--gray_05': '#11121a',

    // 分割线
    '--line_01': '#393c4e',
    '--line_02': '#484c5d',

    // 品牌色
    '--brand_01': '#3560b5',
    '--brand_02': '#264ca3',
    '--brand_03': '#264ca3',
    '--brand_04': '#001252',
    '--brand_05': '#1a2535',
    '--brand_06': '#1c2430',
    '--brand_07': '#f8d000',

    // 数据可视化
    '--data_01': '#5b8ff9',
    '--data_02': '#61ddaa',
    '--data_03': '#65789b',
    '--data_04': '#f6bd16',
    '--data_05': '#7262fd',
    '--data_06': '#78d3f8',
    '--data_07': '#9661bc',
    '--data_08': '#f6903d',
    '--data_09': '#008685',
    '--data_10': '#f08bb4',

    // 文字色
    '--text_1st': '#f0f3f7',
    '--text_2nd': '#b6c2d0',
    '--text_3rd': '#8693a0',
    '--text_4rd': '#76808d',
    '--text_5th': '#f1f3f7',
    '--text_disabled': '#434b55',
    '--text_3rd_opacity': 'rgba(131, 144, 160, 0.15)',
    // 涨跌色
    '--green': '#0acca0',
    '--red': '#ff0b34',

    // 背景色
    '--background': '#1B1D28',

    // 功能色
    '--error': '#ff4d4f',
    '--link': '#178fff',
    '--success': '#52c41a',
    '--warning': '#faad13',
    '--warning_opacity': 'rgba(246, 189, 22, 0.15)',
    '--error_opacity': 'rgba(255, 77, 79, 0.15)',
  },
  dodgerlight: {
    // 中性色
    '--gray_01': '#4B596B',
    '--gray_02': '#C8CDD5',
    '--gray_03': '#E5E6EC',
    '--gray_04': '#F2F3F5',
    '--gray_05': '#F7F8FA',

    // 分割线
    '--line_01': '#E5E6EB',
    '--line_02': '#C8CDD5',

    // 品牌色
    '--brand_01': '#165DFF',
    '--brand_02': '#457DFF',
    '--brand_03': '#A2BEFF',
    '--brand_04': '#D0DFFF',
    '--brand_05': '#E7EEFF',
    '--brand_06': '#F3F7FF',
    '--brand_07': '#19DFB6',
    '--brand_opacity_01': '#165DFF',
    '--brand_opacity_02': '#19DFB6',

    // 数据可视化
    '--data_01': '#5B8FF9',
    '--data_02': '#61DDAA',
    '--data_03': '#65789B',
    '--data_04': '#F6BD16',
    '--data_05': '#7262FD',
    '--data_06': '#78D3F8',
    '--data_07': '#9661BC',
    '--data_08': '#F6903D',
    '--data_09': '#008685',
    '--data_10': '#F08BB4',

    // 文字色
    '--text_1st': '#1C212A',
    '--text_2nd': '#4B596B',
    '--text_3rd': '#83909D',
    '--text_4rd': '#B6C2D0',
    '--text_5th': '#F1F3F7',
    '--text_disabled': '#C8CDD5',
    '--text_opacity': 'rgba(241, 243, 247, 0.6)',
    '--text_3rd_opacity': 'rgba(131, 144, 157, 0.15)',
    // 涨跌色
    '--green': '#0ACCA0',
    '--red': '#FF0B34',

    // 背景色
    '--background': '#FFFFFF',

    // 功能色
    '--error': '#FF4D4F',
    '--link': '#178FFF',
    '--success': '#52C41A',
    '--warning': '#FAAD13',
    '--warning_opacity': 'rgba(246, 189, 22, 0.15)',
    '--error_opacity': 'rgba(255, 77, 79, 0.15)',
    // 遮罩
    '--mask': 'rgba(0,0,0,0.25)',
  },
  futulight: {
    // 中性色
    '--gray_01': '#4B596B',
    '--gray_02': '#C8CDD5',
    '--gray_03': '#E5E6EC',
    '--gray_04': '#F2F3F5',
    '--gray_05': '#F7F8FA',

    // 分割线
    '--line_01': '#E5E6EB',
    '--line_02': '#C8CDD5',

    // 品牌色
    '--brand_01': '#FE6900',
    '--brand_02': '#FE8733',
    '--brand_03': '#FEA566',
    '--brand_04': '#FFC399',
    '--brand_05': '#FEF0E5',
    '--brand_06': '#FFF7F2',
    '--brand_07': '#FEF0E5',
    '--brand_opacity_01': '#FE6900',
    '--brand_opacity_02': '#5C5C5C',

    // 数据可视化
    '--data_01': '#5B8FF9',
    '--data_02': '#61DDAA',
    '--data_03': '#65789B',
    '--data_04': '#F6BD16',
    '--data_05': '#7262FD',
    '--data_06': '#78D3F8',
    '--data_07': '#9661BC',
    '--data_08': '#F6903D',
    '--data_09': '#008685',
    '--data_10': '#F08BB4',

    // 文字色
    '--text_1st': '#1C212A',
    '--text_2nd': '#4B596B',
    '--text_3rd': '#83909D',
    '--text_4rd': '#B6C2D0',
    '--text_5th': '#F1F3F7',
    '--text_disabled': '#C8CDD5',
    '--text_opacity': 'rgba(241, 243, 247, 0.6)',
    '--text_3rd_opacity': 'rgba(131, 144, 157, 0.15)',
    // 涨跌色
    '--green': '#0ACCA0',
    '--red': '#FF0B34',

    // 背景色
    '--background': '#FFFFFF',

    // 功能色
    '--error': '#FF4D4F',
    '--link': '#178FFF',
    '--success': '#52C41A',
    '--warning': '#FAAD13',
    '--warning_opacity': 'rgba(246, 189, 22, 0.15)',
    '--error_opacity': 'rgba(255, 77, 79, 0.15)',
    // 遮罩
    '--mask': 'rgba(0,0,0,0.25)',
  },
  tigerlight: {
    // 中性色
    '--gray_01': '#4B596B',
    '--gray_02': '#C8CDD5',
    '--gray_03': '#E5E6EC',
    '--gray_04': '#F2F3F5',
    '--gray_05': '#F7F8FA',

    // 分割线
    '--line_01': '#E5E6EB',
    '--line_02': '#C8CDD5',

    // 品牌色
    '--brand_01': '#F7CC00',
    '--brand_02': '#F9D633',
    '--brand_03': '#FAE066',
    '--brand_04': '#FCEB99',
    '--brand_05': '#FEF9E5',
    '--brand_06': '#FEFCF2',
    '--brand_07': '#5C5C5C',
    '--brand_opacity_01': '#F7CC00',
    '--brand_opacity_02': '#5C5C5C',

    // 数据可视化
    '--data_01': '#5B8FF9',
    '--data_02': '#61DDAA',
    '--data_03': '#65789B',
    '--data_04': '#F6BD16',
    '--data_05': '#7262FD',
    '--data_06': '#78D3F8',
    '--data_07': '#9661BC',
    '--data_08': '#F6903D',
    '--data_09': '#008685',
    '--data_10': '#F08BB4',

    // 文字色
    '--text_1st': '#1C212A',
    '--text_2nd': '#4B596B',
    '--text_3rd': '#83909D',
    '--text_4rd': '#B6C2D0',
    '--text_5th': '#F1F3F7',
    '--text_disabled': '#C8CDD5',
    '--text_opacity': 'rgba(241, 243, 247, 0.6)',
    '--text_3rd_opacity': 'rgba(131, 144, 157, 0.15)',
    // 涨跌色
    '--green': '#0ACCA0',
    '--red': '#FF0B34',

    // 背景色
    '--background': '#FFFFFF',

    // 功能色
    '--error': '#FF4D4F',
    '--link': '#178FFF',
    '--success': '#52C41A',
    '--warning': '#FAAD13',
    '--warning_opacity': 'rgba(246, 189, 22, 0.15)',
    '--error_opacity': 'rgba(255, 77, 79, 0.15)',
    // 遮罩
    '--mask': 'rgba(0,0,0,0.25)',
  },
}