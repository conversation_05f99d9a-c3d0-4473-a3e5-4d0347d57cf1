<template>
  <div class="eddid-agree">
    <div class="agree-icon-wrap">
      <img
        @click="onChange"
        :src="require(`@/assets/images${!value && theme === 'dark' ? '/dark' : ''}/checkbox${value ? '-checked' : ''}.png`)"
        class="agree-icon"
      />
    </div>
    <slot></slot>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'EddidAgree',
  props: {
    iconType: {
      default: 'square',
      validator: value => ['square', 'circular'].includes(value)
    },
    value: {
      type: Boolean,
      default: false,
    },
    notNeedChangStatus: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    onChange() {
      if(!this.notNeedChangStatus){
        const newVal = !this.value;
        this.$emit('input', newVal, 'needChangStatus');
        return
      }
      this.$emit('input', this.value);
    },
  }
}
</script>

<style lang="scss" scoped>
.eddid-agree {
  width: 100%;
  display: flex;
  align-items: flex-start;
  color: #121c32;
  font-size: 28px;
  line-height: 48px;
}
.agree-icon-wrap {
  padding: 2px 20px 0 0;
  line-height: 48px;
}
.agree-icon {
  width: 28px;
  height: 28px;
}

.dark .agree {
  color: #868e9e;
}
</style>