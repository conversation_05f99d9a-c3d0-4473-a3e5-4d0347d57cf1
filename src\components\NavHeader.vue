<template>
  <div class="nav-header-container">
    <div class="title"><span>{{ innerTitle }}</span></div>
    <slot name="ContentLeft">
      <div class="content-left">
        <div @click="onBack" class="icon-wrap">
          <img :src="require(`@/assets/images${theme === 'dark' ? '/dark' : '' }/icon_nav_back.png`)" class="icon-back" /></div>
        <div @click="onClose" class="icon-wrap">
          <img :src="require(`@/assets/images${theme === 'dark' ? '/dark' : '' }/icon_nav_close.png`)" class="icon-close" /></div>
      </div>
    </slot>
    <slot name="ContentRight">
    </slot>
  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: 'NavHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    close: {
      type: Function,
    },
    back: {
      type: Function,
    }
  },
  data(){
    return {
      innerTitle: this.title,
    }
  },
  beforeMount() {
    if(!this.title) {
      const { title } = this.$route.meta;
      this.innerTitle = title;
    }
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    onClose(){
      if(this.close && typeof this.close === "function") {
        return this.close();
      }
      this.$jsBridge.isSupported('navBack') ? this.$jsBridge.run('navBack', { isClosePage: true }) : this.$router.push('/');
    },
    onBack(){
      if(this.back && typeof this.back === "function") {
        return this.back();
      }
      this.$router.go(-1);
    }
  }
}
</script>

<style lang="scss" scoped>
  .nav-header-container {
    @include background_color(bg-color);
    z-index: 999;
    opacity: 1;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 88px;
    .title {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 88px;
      @include font_color(text-color);
      font-size: 32px;
      line-height: 32px;
      font-weight: 500;
      span {
        width: 350px;
        margin-left: 30px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
      }
    }
  }

  .content-left {
    position: absolute;
    top: 0;
    left: 0;
    // width: 260px;
    height: 88px;

    .icon-wrap {
      display: inline-flex;
      align-items: center;
      height: 100%;
      padding: 0 30px;
    }

    .icon-wrap + .icon-wrap {
      margin-left: 8px;
    }

    .icon-back {
      width: 36px;
      height: 36px;
    }

    .icon-close {
      width: 36px;
      height: 36px;
    }
  }

</style>