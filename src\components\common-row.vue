<template>
  <div class="row-page">
    <span>{{ $t(contentLeft) }}</span>
    <slot class="content-right"></slot>
  </div>
</template>
<script>
export default {
  name: 'ed-row',
  data() {
    return {}
  },
  props: ['contentLeft'],
}
</script>
<style lang="scss" scoped>
.row-page {
  @include themeify {
    background: themed('background-color');
    color: themed('text-color');
    border-bottom-color: themed('line-color');
  }
  width: calc(100% - 60px);
  margin: 0px 30px;
  border-bottom: 1px solid;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 29px 0px;
  vertical-align: top;
  font-size: 28px;
  .content-right {
    display: inline-block;
    width: 440px;
    white-space: pre-wrap;
    text-align: right;
    line-height: 42px;
  }
}
</style>
