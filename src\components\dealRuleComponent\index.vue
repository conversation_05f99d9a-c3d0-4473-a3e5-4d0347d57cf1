<template>
  <div class="deal-rule-component">
    <div class="deal-rule-title">
      {{
        dealRuleCurrent == 1
          ? $t('fundMessage.subscribe') + $t('fundMessage.flow')
          : $t('fundMessage.redemption') + $t('fundMessage.flow')
      }}
    </div>
    <div v-if="dealRuleCurrent == 1" class="fund-order-box">
      <div class="fund-order-state">
        <ul>
          <li>
            {{ $t('fundMessage.subscribe') + $t('fundMessage.submitText') }}
          </li>
          <li>{{ $t('fundMessage.affirmShare') }}</li>
          <li>{{ $t('fundMessage.earningsArrive') }}</li>
        </ul>
        <ul>
          <li class="fund-img-li">
            <img src="../../assets/images/fund_detail_process.png" alt="" />
          </li>
        </ul>
        <ul class="last-ul">
          <!-- <li>{{ $t("fundMessage.TDayBeforeTen") }}</li> -->
          <li>{{ $t("fundMessage.TDayBeforeTen", { time: endOfTradingDay }) }}</li>
          <li>T+{{ shareConfirmedDay }}{{ $t("fundMessage.TDay") }}</li>
          <li>T+{{ toTheAccount }}{{ $t("fundMessage.tradingDay") }}</li>
        </ul>
      </div>
    </div>
    <div v-else class="fund-order-box">
      <div class="fund-order-state">
        <ul>
          <li>
            {{ $t('myAccount.redemptionSubmission')}}
          </li>
          <li>{{ $t('myAccount.confirmTheAmount') }}</li>
          <li>{{ $t('myAccount.redemptionToAccount') }}</li>
        </ul>
        <ul>
          <li class="fund-img-li">
            <img src="../../assets/images/fund_detail_process.png" alt="" />
          </li>
        </ul>
        <ul class="last-ul">
          <li>{{ $t("fundMessage.TDayBeforeTen", { time: endOfTradingDay }) }}</li>
          <li>T+{{ shareConfirmedDay }}{{ $t("fundMessage.TDay") }}</li>
          <li>T+{{ toTheAccount }}{{ $t("fundMessage.tradingDay") }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dealRuleCurrent: { 
      type: Number,
    },
      shareConfirmedDay: {
      type: Number,
    },
    toTheAccount: {
      type: Number
    },
    endOfTradingDay: {
      type: String
    }
  },
}
</script>

<style lang="scss" scoped>
.deal-rule-component {
  .deal-rule-title {
    font-size: 34px;
    font-weight: bold;
    padding: 28px 0;
  }
  .fund-order-box {
    // padding: 0 30px;
    .fund-order-state {
      padding: 40px 0 0;
      // border-bottom: 1px solid;
      // @include border_color(line-color);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      ul {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 20px;
        text-align: center;
        .fund-img-li {
          width: 100%;
          display: flex;
          justify-content: center;
          img {
            width: 510px;
            height: 40px;
          }
        }
      }
      .last-ul {
        @include font_color(btn-color);
        font-size: 24px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        li {
          margin: 0px;
          width: 33%;
          text-align: center;
        }
      }
    }
  }
}
</style>
