<template>
  <div class="ed-selector" :class="{'is-error': !!error}">
    <!-- <div class="select-container" @click="isSelectorShow=true">
      <div class='left'>
        <img :src="getAccountTypeIcon(currTradeAccount.value)" alt="">
        <span>{{currTradeAccount.lable}}</span>
      </div>
      <md-icon :name="isSelectorShow ? 'arrow-up' : 'arrow-down'"></md-icon>
    </div> -->
    <md-field-item :content="label" @click="showSelect" solid :disabled="disabled">
      <ed-svg-icon v-if="!disabled" iconClass="dropDown" class="dropDown" :class="{'arrow-up': seletorVisible}" slot="right" />
      <div slot="left" v-if="!label" class="unselect">
        {{$t('common.pleaseSelect')}}
      </div>
      
    </md-field-item>
    <div v-if="error" class="md-field-item-children">
      <div class="md-input-item-msg">
        <p>{{error}}</p>
      </div> <!----> 
    </div>
    <!-- <md-selector
      v-model="seletorVisible"
      :data="data"
      :title="title"
      :min-height="minHeight"
      :max-height="maxHeight"
      class="ed-selector"
      @choose="onSelectorConfirm"
      :defaultValue="false"
    ></md-selector> -->
    <md-popup
      v-model="seletorVisible"
      position="bottom"
      :mask-closable="maskClosable"
      class="md-selector ed-selector inner-popup"
    >
      <md-popup-title-bar
        :title="title"
        @confirm="$_onSelectorConfirm"
        @cancel="$_onSelectorCancel"
        :only-close="true"
      ></md-popup-title-bar>
      <div >
        <div class="md-selector-container">
          <md-scroll-view
            ref="scroll"
            :scrolling-x="false"
            :style="{
              'max-height': `${maxHeight}`,
              'min-height': `${minHeight}`
            }"
          >
            <slot name="header"></slot>
            <!-- audio-list with single select -->
            <template >
              <!-- <md-radio-list
                class="md-selector-list"
                ref="radio"
                :key="radioKey"
                :value="defaultValue"
                :options="data"
                :is-slot-scope="true"
                :icon="icon"
                :icon-disabled="iconDisabled"
                :icon-inverse="iconInverse"
                :icon-position="iconPosition"
                :icon-size="iconSize"
                :icon-svg="iconSvg"
                @change="$_onSelectorChoose"
              >
                <template slot-scope="{ option, index, selected }">
                  <slot :option="option" :index="index" :selected="selected"></slot>
                  <div class="custom-brief" @click.stop="expand(option)">
                    <span>{{ option.text }}</span>
                    <ed-svg-icon v-if="option.children && option.children.length"  iconClass="dropDown" class="dropDown" :class="{'arrow-up': option.show}" slot="right" />
                  </div>
                  <div  v-if="option.children && option.children.length && option.show">
                    <div v-for="item in option.children" :key="item.value">{{item.text}}</div>
                  </div>
                </template>
              </md-radio-list> -->
              <md-radio-group v-model="defaultValue" class="ed-radio">
                <div v-for="(item, key) in data" :key="key">
                  <div v-if="item.children && item.children.length" class="parent-item" @click.stop="expand(item)">
                    <span>{{item.text}}</span>
                    <ed-svg-icon v-if="item.children && item.children.length"  iconClass="dropDown" class="dropDown" :class="{'arrow-up': item.show}" slot="right" />
                    <div  v-if="item.children && item.children.length && item.show" @click.stop>
                      <md-radio v-for="el in item.children" :key="el.value" :label="item.text" 
                                :name="el.value"
                                :is-slot-scope="true"
                                :icon="icon"
                                :icon-disabled="iconDisabled"
                                :icon-inverse="iconInverse"
                                :icon-position="iconPosition"
                                :icon-size="iconSize"
                                :icon-svg="iconSvg"/>
                    </div>
                  </div>
                  <md-radio v-else  :label="item.text" 
                            :name="item.value"
                            :is-slot-scope="true"
                            :icon="icon"
                            class="parent-radio"
                            :icon-disabled="iconDisabled"
                            :icon-inverse="iconInverse"
                            :icon-position="iconPosition"
                            :icon-size="iconSize"
                            :icon-svg="iconSvg"/>
                </div>
                
              </md-radio-group>
            </template>
          </md-scroll-view>
        </div>
      </div>
    </md-popup>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { Icon, Selector, Popup, PopupTitleBar, ScrollView, RadioList, CheckList,Radio,RadioGroup } from "mand-mobile";
import { getAccountType } from "~/assets/utils/util";


export default {
  name: "select-account-type",
  components: {
    [Icon.name]: Icon,
    [ScrollView.name]: ScrollView,
    [RadioList.name]: RadioList,
    [CheckList.name]: CheckList,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Popup.name]: Popup,
    [PopupTitleBar.name]: PopupTitleBar
  },
  model:{
    event: 'change',
    prop: 'value'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    disabled:{
      type: Boolean,
      default: false
    },
    error: {
      type:String,
      default: ''
    },
    value: {
      default: '',
      type: String
    },
    multi: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: 'right',
    },
    iconDisabled: {
      type: String,
      default: '',
    },
    iconInverse: {
      type: String,
      default: '',
    },
    iconSvg: {
      type: Boolean,
      default: false,
    },
    iconSize: {
      type: String,
      default: 'md',
    },
    iconPosition: {
      type: String,
      default: 'right',
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    data: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      maxHeight: '60vh',
      minHeight: '160px',
      curSelected: {},
      seletorVisible: false,
      activeIndex: -1,
      tmpActiveIndex: -1,
      multiDefaultValue: [],
      defaultValue: '',
      radioKey: Date.now(),
      checkKey: Date.now() + 1,
      // data: [
      //   { "value":"2019080905","text":"中国民生", type: 'yz', 
      //     children: [
      //       { "value":"201","text":"800056872-101(HKD)" },
      //       { "value":"202","text":"800056872-114(USD)" },
      //       { "value":"203","text":"800056872-199(CNY))" },
      //     ] 
      //   },
      //   { "value":"2019080908","text":"中国工商", type: 'yz', 
      //     children: [
      //       { "value":"201","text":"800056872-101(HKD)" },
      //       { "value":"202","text":"800056872-114(USD)" },
      //       { "value":"203","text":"800056872-199(CNY))" },
      //     ] 
      //   },
      //   { "value":"2019080909","text":"中国工商", type: 'yz', 
      //     children: [
      //       { "value":"201","text":"800056872-101(HKD)" },
      //       { "value":"202","text":"800056872-114(USD)" },
      //       { "value":"203","text":"800056872-199(CNY))" },
      //     ] 
      //   },
      //   { "value":"2019080901","text":"中国工商", type: 'yz', 
      //     children: [
      //       { "value":"201","text":"800056872-101(HKD)" },
      //       { "value":"202","text":"800056872-114(USD)" },
      //       { "value":"203","text":"800056872-199(CNY))" },
      //     ] 
      //   },
      //   { "value":"2019080903","text":"中国工商| 2019080905", type: 'com' },
      //   { "value":"2019080902","text":"中国招商 | 2019080905", type: 'com' },
      //   { "value":"2019080901","text":"中国农行| 2019080901", type: 'com' }
      // ]
    };
  },
  watch:{
    value: function(val){
      let row = {}
      this.data.map(item => {
        if(item.value === val){
          this.$emit("getRow", item);
        }
      })
      
    }
  },
  computed: {
    label(){
      let label = ''
      this.data.map(item => {
        if(item.value === this.value){
          label = item.text
        }
      })
      if(!label){
        label = this.value
      }
      
      return label
    },
    hasSlot() {
      return !!this.$scopedSlots.default
    },
  },
  methods: {
    // getAccountTypeIcon(type) {
    //   return this[getAccountType(type)];
    // },
    showSelect(){
      // this.$emit("update:visible", true)
      this.seletorVisible  =  true
    },
    cancel(){
      // this.$emit("update:visible", false)
    },
    onSelectorConfirm(array) {
      // this.curSelected = {
      //   value: array.value,
      //   lable: array.text
      // };
     
      this.$emit("change", array.value);
      // this.$emit("getRow", array);
      
    },
    expand(opt){
      this.$refs.scroll.isInitialed = false
      this.$nextTick(()=>{
        this.$refs.scroll.$_initScroller()
      })
      if(!opt.show){
        this.$set(opt, 'show', true)
      }else{
        this.$set(opt, 'show', false)
      }
      
    },
    $_setScroller() {
      this.$refs.scroll.reflowScroller()
    },
    // MARK: events handler
    $_onSelectorConfirm() {
      if (this.multi) {
        this.$emit('confirm', this.multiDefaultValue.slice())
        this.seletorVisible = false
        return
      }

      if (this.tmpActiveIndex > -1) {
        this.activeIndex = this.tmpActiveIndex
        this.seletorVisible = false
        this.$emit('confirm', this.data[this.activeIndex])
      }
    },
    $_onSelectorCancel() {
      this.seletorVisible = false
      this.tmpActiveIndex = this.activeIndex

      if (this.tmpActiveIndex !== -1) {
        this.$refs.radio.selectByIndex(this.tmpActiveIndex)
      } else {
        // reset radio
        this.radioKey = Date.now()
        this.checkKey = Date.now() + 1
      }

      this.$emit('cancel')
    },
    $_onSelectorChoose(item, index) {
      this.tmpActiveIndex = index
      if (!this.isNeedConfirm) {
        this.activeIndex = index
        if(!item.children || item.children && !item.children.length){
          this.seletorVisible = false
        }
        this.$emit("change", item.value);
      }

      this.$emit('choose', item)
    },
    $_onSelectorShow() {
      /* istanbul ignore next  */
      this.$_setScroller()
      this.$emit('show')
    },
    $_onSelectorHide() {
      /* istanbul ignore next  */
      this.$emit('hide')
    },
  }
};
</script>

<style lang="scss" scoped>
.ed-selector{
  font-size: 24px;
}
.unselect{
  font-size: 30px;
  color: $number-colr;
  font-weight: normal;
}
.svg-icon{
  width: 24px !important;
}
.arrow-up{
  transform:rotate(180deg);
}
/ddep/ .md-selector .md-popup{
  z-index: 2000;
}
.ed-selector /deep/ .md-cell-item-title{
  text-align: left;
}
/deep/ .md-field-item-control{
  font-weight: normal !important;
  line-height: 50px;
  font-size: 30px;
  @include font_color($text-color)
}
/deep/ .md-popup-title-bar .title-bar-title p.title{
  font-size: 30px;
}
/deep/ .md-cell-item-title{
  font-size: 30px;
}
/deep/.md-field-item-content{
  font-weight: normal;
  color: $number-colr;
  padding: 0;
  // min-height: 20px;
  // padding-top: 20px;
  // padding-bottom: 20px;
}
.is-error /deep/.md-field-item-content:before {
    content: "";
    position: absolute;
    z-index: 2;
    -webkit-transform-origin: 100% 50%;
    transform-origin: 100% 50%;
    -webkit-transform: scaleY(.5) translateY(100%);
    transform: scaleY(.5) translateY(100%);
    bottom: 0;
    left: 0;
    right: auto;
    top: auto;
    width: 100%;
    border-bottom: 0.04rem solid $error-color !important;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}
.ed-selector /deep/ .md-field-item-content:before{
  border-bottom: 4px solid #1d1d1d;
  @include border_color(line-color) 
}
.ed-selector /deep/.md-radio-item:active{
  @include background_color(notice-background-color)
}
.dropDown{
  float: right;
  margin-top: -8px;
}
.ed-selector .ed-radio /deep/ .md-radio{
  display: block;
  font-size: 28px;
  line-height: 108px;
  
  margin: 0;
  .md-radio-icon{
    float: right;
    margin-top: 30px;
  }
  .md-radio-label{
    margin-left: 0;
  }
}
.ed-selector.ed-radio /deep/ .md-radio-icon{
  float: right;
}
.parent-radio{
  padding: 0px 30px;
}
.parent-item{
  font-size: 28px;
  line-height: 108px;
  padding: 0px 30px;
}
</style>
