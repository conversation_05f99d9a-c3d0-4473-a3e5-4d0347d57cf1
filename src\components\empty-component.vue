<template>
  <div class="empty-box">
    <img class="empty-icon" :src="theme === 'dark' ? noDataIconDark : noDataIcon" />
    <div class="empty-text text-font">{{ $t('common.productDevelop') }}</div>
    <div class="go-back" @click="goBack">{{ $t('common.btns.back') }}</div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import noDataIcon from '@/assets/images/empty.png';
import noDataIconDark from '@/assets/images/empty-dark.png';

export default {
  data () {
    this.noDataIcon = noDataIcon;
    this.noDataIconDark = noDataIconDark;
    return {}
  },

  computed: mapState(['theme']),

  methods: {
    goBack() {
      this.$jsBridge.run('navBack', { isClosePage: true })
    },
  }
}
</script>

<style lang="scss" scoped>
.empty-box {
  padding-top: 134px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.empty-icon {
  width: 233px;
  height: 230px;
}
.empty-text {
  margin-top: 30px;
  color: #868E9E;
  line-height: 44px;
  font-size: 28px;
  font-weight: 400;
  letter-spacing: 0.25px;
}
.go-back {
  width: 184px;
  margin-top: 68px;
  padding: 12px 0;
  text-align: center;
  color: #2D60E0;
  font-size: 26px;
  border-radius: 4px;
  border: 1px solid #2D60E0;
}
.dark.private-equity-fund-page {
  .empty-text {
    color: #E5E5E5;
  }
}
</style>