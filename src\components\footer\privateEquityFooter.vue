<template>
  <footer class="private-equity-footer" :class="[theme]">
    <div class="title">{{ $t('privateEquity.privateEquityFooter.title') }}</div>
    <div class="content">{{ $t('privateEquity.privateEquityFooter.content') }}</div>
    <img :src="require(`@/assets/images/eddid_logo_${theme}.png`)" alt="">
  </footer>
</template>

<script>
import { mapState } from 'vuex';
export default {
  computed: mapState(['theme']),
}
</script>
<style lang="scss" scoped>
.private-equity-footer {
  text-align: center;
  font-size: 24px;
  font-family: PingFangSC-Medium, PingFang SC;
  color: #4B596B;
  padding: 20px 32px 190px;
  line-height: 34px;
  .content {
    color: #83909D;
    margin: 16px 0 32px;
  }
  img {
    width: 142px;
    height: 38px;
  }
}

.dark {
  .private-equity-footer {
    color: #B6C2D0;
    .content {
      color: #8693A0;
    }
  }
}
</style>