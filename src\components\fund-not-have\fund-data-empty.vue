<template>
  <div class="fund-not-have">
    <div class="fund-not-have-box">
      <!-- <img v-if="companyImage" :src="require('@/assets/images/fund_company_' + $theme + '.png')" alt=""> -->
      <!-- <img v-if="subordinateImage || fundData" :src="require('@/assets/images/subordinate_fund_' + $theme + '.png')" alt=""> -->
      <img
        :src="require('@/assets/images/subordinate_fund_' + $theme + '.png')"
        alt=""
      />
      <div>{{ $t('fundMessage.notMessage') }}</div>
      <!-- <div v-else>{{companyImage ? $t('fundMessage.correlationCompany') : $t('fundMessage.correlationFund')}}</div> -->
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    // companyImage: {
    //     type: Boolean
    // },
    // subordinateImage: {
    //     type: Boolean
    // },
    // synopsisShowAll: {
    //     type: Boolean
    // },
    fundData: {
      type: Boolean,
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapState(['theme']),
  },
}
</script>

<style lang="scss" scoped>
.fund-not-have {
  @include font_color(second-text);
  font-size: 28px;
  .fund-not-have-box {
    text-align: center;
    img {
      width: 173.5px;
      height: 100px;
    }
  }
}
.company-image {
  height: 100%;
  top: 0;
}
.subordinate-image {
  height: 60%;
  top: 70%;
}
.synopsis-show-all {
  top: 85%;
}
</style>
