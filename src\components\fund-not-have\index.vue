<template>
  <div
    class="fund-not-have"
    :class="{
      'company-image': companyImage,
      'subordinate-image': !companyImage,
      'synopsis-show-all': synopsisShowAll,
    }"
  >
    <div class="fund-not-have-box">
      <img
        v-if="companyImage"
        :src="require(`@/assets/images/fund_company_${theme}.png`)"
        alt=""
      />
      <img
        v-if="subordinateImage"
        :src="require(`@/assets/images/subordinate_fund_${theme}.png`)"
        alt=""
      />
      <div>
        {{
          companyImage
            ? $t('fundMessage.correlationCompany')
            : $t('fundMessage.correlationFund')
        }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    companyImage: {
      type: Boolean,
    },
    subordinateImage: {
      type: Boolean,
    },
    synopsisShowAll: {
      type: Boolean,
    },
    fundData: {
      type: <PERSON><PERSON><PERSON>,
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapState(['theme']),
  },
}
</script>

<style lang="scss" scoped>
.fund-not-have {
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  @include font_color(second-text);
  font-size: 28px;
  .fund-not-have-box {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -130%);
    text-align: center;
    img {
      width: 173.5px;
      height: 100px;
    }
  }
}
.company-image {
  height: 100%;
  top: 0;
  .fund-not-have-box {
    top: 307px;
    transform: translate(-50%, 0);
  }
}
.subordinate-image {
  height: 60%;
  top: 70%;
}
.synopsis-show-all {
  top: 85%;
}
</style>
