<template>
  <div class="invest-pie-box">
    <div class="invest-pie-update-time" v-if="compositionsPie && compositionsPie.assetUpdateDate">
      {{ $t('fundMessage.updateTime') }}:{{ compositionsPie.assetUpdateDate }}
    </div>
    <div class="myChart-box">
      <canvas id="myChart_pie"></canvas>
      <ul v-if="this.compositionsPie && this.compositionsPie.assets && this.compositionsPie.assets.length">
        <li v-for="(item,index) in array" :key="index" class="pie-name-style">
          <span class="round" :class="'round' + index"></span>
          <span class="text">{{item.assetName}}</span>
          <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
        </li>
      </ul>
    </div>
    <!-- <canvas id="myChart_pie" v-if="compositionsPie.assets&&compositionsPie.assets.length>0"></canvas> -->
    <!-- <div class="column-chart-li" v-else>
            <fundDataEmpty :fundData="true"></fundDataEmpty>
        </div> -->
  </div>
</template>

<script>
import F2 from '@antv/f2'
// import fundDataEmpty from '@/components/fund-not-have/fund-data-empty'
export default {
  props: ['pieFundIsin', 'compositionsPie'],
  // components: {
  //     fundDataEmpty
  // },
  data() {
    return {
      pieChart: [],
      array: []
    }
  },
  watch: {
    // compositionsPie(newV) {
    //   debugger
    //   // console.log('watch===', newV, oldV);
    //   let v = this;
    //   if (newV.assets.length > 0) {
    //     this.$nextTick(() => {
    //       v.chartPie(newV.assets)
    //     });
        
    //   }
    // },
  },
  methods: {
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    
    chartPie(data) {
      //    console.log('pie图数据====', data);
      let newArr = []
      if(data && data.length) {
        data.forEach((item) => {
          // 文本溢出换行
          // item.assetName = item.assetName.split(' ')[0] + '...'
          newArr.push({
            assetName: item.assetName,
            proportion: Math.abs(Number((item.proportion)*100).toFixed(2)), // 饼图数据需是数字类型
            type: 1,
          })
        })
      }
      // debugger
      
      const map = {}
      // newArr.forEach(function (obj) {
      //   map[obj.assetName] = Number(obj.proportion) + '%'
      // })
      // console.log(map);
      let corlorList = [];
      if(window.localStorage.getItem('theme') === 'dark') {
        corlorList = [
          '#2D60E0',
          '#284AA1',
          '#304761',
          '#00C2AC',
          '#0B8A7F',
          '#184B4C',
        ];
      }else {
        corlorList = [
          '#2D60E0',
          '#7697EB',
          '#D7ECFF',
          '#00C2AC',
          '#59D7C9',
          '#BFF0EA',
        ];
      }
      const chart = new F2.Chart({
        id: 'myChart_pie',
        pixelRatio: window.devicePixelRatio,
        width: 180,
        height: 150,
        padding: '0',
      })
      chart.source(newArr)
      chart.tooltip(false);
      chart.legend(false
        // show:false,
        // position: 'right',
        // itemFormatter: function itemFormatter(val) {
        //   return val + '     ' + map[val]
        // },
        // nameStyle: {
        //   // 调整chart图文本显示宽度
        //   width: 100,
        // },
      )
      chart.coord('polar', {
        transposed: true,
        innerRadius: 0.7,
        radius: 1,
      })
      chart.axis(false)
      chart
        .interval()
        .position('type*proportion')
        .color('assetName', corlorList)
        .adjust('stack')
      chart.render()
    },
  },
  mounted() {
    // this.columnChart = newV.top10Holdings.sort(this.compare('proportion'))
    if(this.compositionsPie && this.compositionsPie.assets && this.compositionsPie.assets.length) {
      this.compositionsPie.assets = this.compositionsPie.assets.sort(this.compare('proportion'))
      this.array = this.compositionsPie.assets.slice(0,5)
      let lastArray = this.compositionsPie.assets.slice(5,this.compositionsPie.assets.length)
      // console.log('lastArray存在数据=====', lastArray);
      let num = 0;
      if (lastArray && lastArray.length) {
        lastArray.forEach((item) => {
          num = num + Number(item.proportion);
        })
        // 要处理--length > 5 时才会显示 其他状态
        let object = {
          assetName: this.$t('fundMessage.other'),
          proportion: num + ''
        }
        this.array = this.array.concat(object)
      }
      this.chartPie(this.array);
    }
  },
}
</script>

<style lang="scss" scoped>
.invest-pie-box {
  @include themeify {
    // background: themed("bg-color");
    color: themed("text-color");
  }
  .invest-pie-update-time {
    font-size: 22px;
    @include font_color(second-text);
  }
  .myChart-box {
    // width: 750px;
    height: 332px;
    position: relative;
    #myChart_pie {
      position: absolute;
      left: -50px;
      top: 10px;
    }
    display: flex;
    justify-content: space-between;
    align-items: top;
    ul {
      position: absolute;
      right: 0px;
      top: 30px;
      height: 332px;
      // width: 428px;
      width: 55%;
      li {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        span {
          display: inline-block;
          height: 42px;
          line-height: 42px;
        }
        .round {
          width: 14px;
          height: 14px;
          border-radius: 50% 50%;
          margin-left: 10px;
        }
        .round0 {
          background: #2D60E0;
        }
        .round1 {
          @include themeify {
            background: themed("pie-first-color");
          }
        }
        .round2 {
          @include themeify {
            background: themed("pie-second-color");
          }
        }
        .round3 {
          background: #00C2AC;
        }
        .round4 {
          @include themeify {
            background: themed("pie-third-color");
          }
        }
        .round5 {
          @include themeify {
            background: themed("pie-fourth-color");
          }
        }
        .text {
          @include themeify {
            color: themed("second-text");
          }
          font-size: 22px;
          padding-left: 14px;
          padding-right: 17px;
          // width: 285px;
          width: 65%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .right-percent {
          @include themeify {
            color: themed("second-text");
          }
          font-size: 22px;
          // width: 90px;
          width: 20%;
          text-align: left;
        }
      }
    }
  }
  .column-chart-li {
    margin-top: 20px;
  }
}
</style>
