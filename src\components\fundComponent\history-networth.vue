<template>
  <!-- 历史净值 -->
  <div class="history-net-worth-box">
    <!-- <div class="history-net-worth-header">
      <span>{{ $t('fundMessage.timeArea') }}</span>
      <span>{{ $t('fundMessage.upDown') }}</span>
    </div> -->
    <ul class="history-net-worth-data">
      <li class="history-net-worth-li-header">
        <span>{{ $t("fundMessage.timeText") }}</span>
        <span>{{ $t("fundMessage.netValue") }}</span>
        <span>{{ $t("fundMessage.upDown") }}</span>
      </li>
      <li
        v-for="(item, index) in fundTimeData"
        :key="index"
        class="history-net-worth-li"
      >
        <span>{{ item.transDate }}</span>
        <span>{{ Number(item.netValue).toFixed(4) }}</span>
        <!-- :class="Number(item.changePercent) < 0 ? 'output' : Number(item.changePercent) ? 'entry' : 'entry1'" -->
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(item.changePercent) < 0
                ? 'output'
                : Number(item.changePercent)
                ? 'entry'
                : 'entry1'
              : Number(item.changePercent) > 0
              ? 'output'
              : Number(item.changePercent)
              ? 'entry'
              : 'entry1'
          "
        >
          {{
            Number(item.changePercent) > 0
              ? "+" + (item.changePercent * 100).toFixed(2)
              : Number(item.changePercent)
              ? (item.changePercent * 100).toFixed(2)
              : "0.00"
          }}%
        </span>
      </li>
      <div @click="showHistoryNetWorth">
        <span>{{ $t("fundMessage.moreMessage") }}</span>
        <img src="../../assets/images/fund-detail-more.png" alt="" />
      </div>
    </ul>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
export default {
  props: ["fundIsin"],
  data() {
    return {
      fundTimeData: [],
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showHistoryNetWorth() {
      // 历史数据分页页面
      this.$router.push({
        path: "/historyNetworth",
        query: { id: this.fundIsin },
      });
    },
    performance() {
      performance(
        { page: 0, size: 5 },
        this.fundIsin,
        "",
        "netValues",
        "networth"
      ).then((res) => {
        // console.log('历史净值====', res);
        if (res.code == 200) {
          this.fundTimeData = res.data.content;
        }
      });
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.history-net-worth-box {
  .history-net-worth-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    @include font_color(second-text);
  }
  .history-net-worth-data {
    font-size: 26px;
    @include font_color(text-color);
    .history-net-worth-li-header,
    .history-net-worth-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 33.3%;
        &:nth-child(2) {
          text-align: center;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
    .history-net-worth-li {
      padding: 27px 0;
      border-bottom: 1px solid;
      @include border_color(line-color);
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      .entry {
        @include font_color(buy-color);
      }
      .entry1 {
        @include font_color(second-text);
      }
      .output {
        @include font_color(sell-color);
      }
    }
    div {
      padding: 27px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 28px;
      }
    }
  }
}
</style>
