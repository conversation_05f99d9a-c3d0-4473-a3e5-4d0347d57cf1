<template>
  <div class="history-erformance-box">
    <div class="history-erformance-header">
      <span>{{ $t("fundMessage.timeArea") }}</span>
      <span>{{ $t("fundMessage.upDown") }}</span>
    </div>
    <ul class="history-erformance-data">
      <template v-for="(item, index) in fundTimeData">
        <li class="history-erformance-li" :key="index">
          <span>{{ $t(`fundMessage.dateTime.${index}`) }}</span>
          <!-- :class="item < 0 ? 'output' : item ? Number(item) ? 'entry' : 'second-text' : 'second-text'" -->
          <span
            :class="
              klineTheme === 'redRiseGreenFall'
                ? item < 0
                  ? 'output'
                  : Number(item)
                  ? 'entry'
                  : 'second-text'
                : item > 0
                ? 'output'
                : Number(item)
                ? 'entry'
                : 'second-text'
            "
          >
            {{ performanceNum(item) }}
          </span>
        </li>
      </template>
      <div @click="showHistory">
        <span>{{ $t("fundMessage.moreMessage") }}</span>
        <img src="../../assets/images/fund-detail-more.png" alt="" />
      </div>
    </ul>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
export default {
  props: {
    fundIsin: {
      type: String,
    },
  },
  data() {
    return {
      fundTimeData: {},
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showHistory() {
      this.$router.push({
        path: "/historyPerformance",
        query: { id: this.fundIsin },
      });
    },
    performanceNum(data) {
      // console.log(Number(data));
      if (!data || !Number(data)) {
        return "0.00%";
      }
      if (Number(data) > 0) {
        return "+" + (data * 100).toFixed(2) + "%";
      } else {
        return (data * 100).toFixed(2) + "%";
      }
    },
  },
  mounted() {
    // console.log('fundIsin====', this.fundIsin);
    performance({}, this.fundIsin, "", "performance").then((res) => {
      // console.log(res);
      if (res.code == 200) {
        let newObj = {};
        // this.fundTimeData = res.data;
        // newObj[Object.keys(res.data)] = Object.values(res.data)
        Object.keys(res.data).forEach((item, i) => {
          if (
            [
              "cumulative1Week",
              "cumulative1M",
              "cumulative3M",
              "cumulative6M",
              "cumulative1Y",
            ].includes(item)
          ) {
            newObj[item] = Object.values(res.data)[i];
          }
        });
        this.fundTimeData = newObj;
      }
    });
  },
};
</script>

<style lang="scss" scoped>
.history-erformance-box {
  .history-erformance-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    @include font_color(second-text);
  }
  .history-erformance-data {
    font-size: 26px;
    @include font_color(text-color);
    .history-erformance-li {
      padding: 27px 0;
      border-bottom: 1px solid;
      @include border_color(line-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .entry {
        @include font_color(buy-color);
      }
      .output {
        @include font_color(sell-color);
      }
      .second-text {
        @include font_color(second-text);
      }
    }
    div {
      padding: 27px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 28px;
      }
    }
  }
}
</style>
