<template>
  <div class="hold-position-box" :class="{ investDetails: !investDetails }">
    <ul class="fund-possess-ratio-box">
      <li class="fund-possess-ratio">
        <p v-if="chartTitle">{{ $t('fundMessage.entrepotNumber') }}</p>
        <p>
          <span>{{ $t('fundMessage.updateTime') }}:&nbsp;</span>
          <span>{{compositionsPie.top10HoldingUpdateDate}}&nbsp;{{ weekText(compositionsPie.top10HoldingWeek) }}</span>
        </p>
        <!-- <div class="fund-possess-pie-update-time">
          {{ $t('fundMessage.updateTime') }}:&nbsp;{{
            compositionsPie.lastModifiedDate
          }}&nbsp;
          {{ weekText(compositionsPie.week) }}
        </div> -->
      </li>
      <li class="column-chart-li" v-for="(item, i) in columnChart" :key="i">
        <!-- <canvas id="columnChart"></canvas> -->
        <div class="chart-title">{{ item.holdingName }}</div>
        <div class="chart-column">
          <div>
            <div
              :style="{
                background: chartColorArr[i],
                width: chartWidth(columnChart, item, i) + '%',
              }"
            ></div>
          </div>
          <div>{{ (item.proportion*100).toFixed(2) }}%</div>
        </div>
      </li>
      <!-- <li class="column-chart-li" v-if="compositionsPie.top10Holdings&&compositionsPie.top10Holdings.length>0">
                <canvas id="columnChart"></canvas>
            </li> -->
      <!-- <li v-else class="column-chart-li">
                <fundDataEmpty :fundData="true"></fundDataEmpty>
            </li> -->
    </ul>
  </div>
</template>

<script>
import { mapState } from 'vuex'
// import F2 from '@antv/f2'
// import fundDataEmpty from '@/components/fund-not-have/fund-data-empty'
// import { fundCompositions } from '@/services/fund'
export default {
  props: {
    chartTitle: {
      type: Boolean,
    },
    investDetails: {
      type: Boolean,
    },
    columnFundIsin: {
      type: String,
    },
    compositionsPie: {
      type: Object,
    },
  },
  data() {
    return {
      columnChart: [],
      chartColorArr: [],
    }
  },
  watch: {
    compositionsPie(newV) {
      // console.log('柱watch===', newV, oldV);
      this.columnChart = newV.top10Holdings.sort(this.compare('proportion'))
    },
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    weekText(data) {
      let text
      switch (data) {
        case 'MONDAY':
          text = this.$t('common.weekTime.MONDAY')
          break
        case 'TUESDAY':
          text = this.$t('common.weekTime.TUESDAY')
          break
        case 'WEDNESDAY':
          text = this.$t('common.weekTime.WEDNESDAY')
          break
        case 'THURSDAY':
          text = this.$t('common.weekTime.THURSDAY')
          break
        case 'FRIDAY':
          text = this.$t('common.weekTime.FRIDAY')
          break
        case 'SATURDAY':
          text = this.$t('common.weekTime.SATURDAY')
          break
        case 'WEEKDAY':
          text = this.$t('common.weekTime.WEEKDAY')
          break
        default:
          break
      }
      return text
    },
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    chartWidth(data, item, i) {
      // chartWidth
      // if (i != 0) {
      //   console.log(data[i].proportion / data[0].proportion)
      // }
      return (data[i].proportion*100) / data[0].proportion
    },
  },
  mounted() {
    if (localStorage.getItem('theme') == 'dark') {
      this.chartColorArr = [
        '#2D60E0',
        '#2A54BC',
        '#284898',
        '#253B75',
        '#232F51',
        '#232F51',
        '#232F51',
        '#232F51',
        '#232F51',
        '#232F51',
      ]
      // this.chartColorArr = ['#232F51', '#232F51', '#232F51', '#232F51', '#232F51', '#232F51', '#253B75', '#284898', '#2A54BC', '#2D60E0'];
    } else {
      this.chartColorArr = [
        '#2D60E0',
        '#5780E6',
        '#81A0EC',
        '#ABBFF3',
        '#D5DFF9',
        '#D5DFF9',
        '#D5DFF9',
        '#D5DFF9',
        '#D5DFF9',
        '#D5DFF9',
      ]
      // this.chartColorArr = ['#D5DFF9', '#D5DFF9', '#D5DFF9', '#D5DFF9', '#D5DFF9', '#D5DFF9', '#ABBFF3', '#81A0EC', '#5780E6', '#2D60E0'];
    }
    // if (this.chartTitle) {
      // this.chartColumn(this.compositionsPie.top10Holdings);
      this.columnChart = this.compositionsPie.top10Holdings.sort(
        this.compare('proportion')
      )
    // }
  },
}
</script>

<style lang="scss" scoped>
.investDetails {
  height: 100%;
}
.hold-position-box {
  // overflow: auto;
  @include themeify {
    background: themed('background-color');
    color: themed('text-color');
  }
  .fund-possess-ratio-box {
    height: 1006px;
    // height: 4.75rem;
    overflow: hidden;
    padding: 20px 0;
    @include font_color(text-color);
    @include background_color(second-bg);
    .fund-possess-ratio {
      font-size: 30px;
      p {
        &:first-child {
          font-weight: bold;
        }
        &:last-child {
          margin-top: 10px;
          font-size: 22px;
          @include font_color(second-text);
        }
      }
    }
    .column-chart-li {
      // height: 90%;
      margin-top: 20px;
      font-size: 20px;
      @include font_color(text-color);
      .chart-title {
        width: 80%;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
      }
      .chart-column {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;
        div {
          &:first-child {
            width: 80%;
            height: 22px;
          }
          &:last-child {
            width: 15%;
          }
        }
      }
    }
  }
}
</style>
