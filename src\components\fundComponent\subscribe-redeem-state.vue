<template>
  <div class="subscribe-redeem-state">
    <img :src="orderData.status == 'SUBMITTED' ? require(`@/assets/images/icon_success.png`) : require(`@/assets/images/icon_default.png`)"
      alt="pic"
      class="subscribe-redeem-image"
    />
    <div class="subscribe-redeem-state-text">
      {{
        orderData.orderType == "Buy"
          ? $t("fundMessage.subscribe")
          : $t("fundMessage.redemption")
      }}{{
        orderData.status == "SUBMITTED"
          ? $t("myAccount.submittedSuccessfully")
          : $t("myAccount.youSubmit")
      }}
    </div>
    <div v-if="orderData.status == 'SUBMITTED'" class="order-state-content">
      <div class="order-type">
        <img :src="require(`../../assets/images/fund_submit_estimate1_${theme}.png`)" alt="pic"/>
        <div v-if="orderData.orderType == 'Buy'" class="order-message order-message1">
          <!-- <p>{{ $t("myAccount.expectedT") }}{{ tradeRulesData.confirmationPlusDay }}{{ $t("myAccount.orderDetailBuy") }}</p>
          <p>{{ $t("myAccount.expectedT") }}{{ tradeRulesData.moneyConfirmedPlusDay }}{{ $t("myAccount.checkYield") }}</p> -->
          <div>
            <p>{{ $t("myAccount.ConfirmShare") }}</p>
            <p>
              {{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.shareConfirmedDay && moment(tradeRulesData.shareConfirmedDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}
              {{ $t("myAccount.ConfirmedWithNAV", {time: tradeRulesData.navConfirmedDay && moment(tradeRulesData.navConfirmedDay).format("YYYY-MM-DD")}) }}
            </p>
          </div>
          <div>
            <p>{{ $t("myAccount.StartViewingEarning") }}</p>
            <p>{{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.profitArriveDay && moment(tradeRulesData.profitArriveDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}</p>
          </div>
        </div>
        <div v-else class="order-message order-message-sell">
          <div>
            <!-- <p>{{ $t("myAccount.submit") + $t("fundMessage.redemption") + orderData.orderTime }} </p>
            <p>{{ $t("myAccount.submitFund") }}{{ $t("myAccount.redemptionShare") }}{{ $t("fundMessage.orderText") }}</p> -->
            <p>{{ $t("myAccount.submit") + $t("fundMessage.redemption") }} </p>
            <!-- 取值当前时间即可, 如果取值后端返回值, 时分不准确(tradeRulesData.sellSubmitDay) -->
            <p>{{ moment().format("YYYY-MM-DD HH:mm") }}</p>
          </div>
          <div>
            <p>{{ $t("fundMessage.capitalReceive") }}</p>
            <!-- tradeRulesData.redemptionPlusDay -->
            <p>{{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.redemptionArriveDay && moment(tradeRulesData.redemptionArriveDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}</p>
          </div>
        </div>
      </div>
      <ul class="order-state-ul">
        <li>
          <label>{{
            orderData.orderType == "Buy"
              ? $t("myAccount.paymentAccount")
              : $t("myAccount.redeemTo")
          }}</label>
          <div>{{ $t("myAccount.paymentAccountContent") }}</div>
        </li>
        <li>
          <label>{{ $t("myAccount.productName") }}</label>

          <div>
            {{
              locale === "zh-hans"
                ? !orderData.productName.cn
                  ? "--"
                  : orderData.productName.cn
                : orderData.productName.hk
            }}
          </div>
        </li>
        <li>
          <label>{{
            orderData.orderType == "Buy"
              ? $t("myAccount.subscriptionAmount")
              : $t("myAccount.redemptionShare")
          }}</label>
          <div>
            {{ orderData.orderType === "Buy" ? orderData.currency + " " + Number(orderData.amount).toFixed(2) : Number(orderData.quantity) }}
          </div>
        </li>
      </ul>
    </div>
    <div v-else class="order-state-error">
      {{ $t("fundMessage.loseCause")
      }}{{
        orderData.orderType == "Buy"
          ? $t("fundMessage.subscribe")
          : $t("fundMessage.redemption")
      }}
    </div>
    <div
      class="btn-type"
      :class="{ 'state-error': orderData.status != 'SUBMITTED' }"
    >
      <md-button @click="goBack" type="primary">{{
        orderData.status == "SUBMITTED"
          ? $t("myAccount.complete")
          : $t("myAccount.return")
      }}</md-button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
export default {
  computed: {
    ...mapState(["theme", "locale"]),
  },
  props: {
    orderData: {
      type: Object,
    },
    tradeRulesData: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.orderData.orderTime = moment(this.orderData.orderTime).format("YYYY-MM-DD HH:mm");
  },
  methods: {
    moment,
    goBack() {
      // 跳转基金账户页面
      this.$jsBridge.run("toPage", {
        jumpType: "NATIVE",
        loginState: "JUDGMENT",
        openAccountState: "JUDGMENT_FUND",
        navigationContentCode: "FUND_ACCOUNT",
        navigationUri: "FUND_ACCOUNT",
        titleDisplay: "DISPALY",
      });
      // this.$router.go(-1)
    },
    expectedBefore(time, num) {
      /**
       * time: 日期
       * num: 需要加上的天数
       */
      let newTime = moment(time).add(num, 'days')
      return moment(newTime).format('YYYY-MM-DD HH:mm')
    }
  },
};
</script>
<style>
.md-button-inner {
  font-size: 28px !important;
}
</style>
<style lang="scss" scoped>
.subscribe-redeem-state {
  text-align: center;
  .subscribe-redeem-image {
    width: 142px;
    height: 142px;
    margin-top: 100px;
  }
  .subscribe-redeem-state-text {
    font-size: 38px;
    font-weight: 400;
    margin-top: 31px;
  }
  .order-state-content {
    .order-type {
      padding: 0 105px;
      display: flex;
      justify-content: center;
      align-items: top;
      font-size: 26px;
      margin: 105px 0;
      img {
        width: 40px;
        height: 187px;
      }
      .order-message {
        text-align: left;
        margin-left: 40px;
        div {
          &:last-child {
            margin-top: 30px;
          }
          p {
            &:last-child {
              font-size: 24px;
              margin-top: 16px;
              @include font_color(second-text);
            }
          }
        }
      }
      .order-message-sell {
        div {
          &:last-child {
            margin-top: 60px;
          }
        }
      }
      .order-message1 {
        height: 146px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .order-state-ul {
      padding: 0 30px;
      font-size: 28px;
      margin-bottom: 70px;
      @include background_color(second-bg);
      li {
        padding: 36px 0;
        display: flex;
        justify-content: space-between;
        align-items: top;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border-bottom: 0;
        }
        div {
          width: 470px;
          text-align: right;
        }
      }
    }
  }
  .order-state-error {
    margin-top: 105px;
    font-size: 26px;
    @include font_color(second-text);
  }
  .btn-type {
    padding: 57px 30px 0;
    border-radius: 2px;
  }
  .state-error {
    margin-top: 569px;
  }
}
</style>
