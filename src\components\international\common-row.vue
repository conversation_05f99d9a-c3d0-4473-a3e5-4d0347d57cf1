<template>
  <div class="row-page">
    <span class="content-left">{{ $t(contentLeft) }}</span>
    <div  class="content-right" :class="{'border-right': borderStyle}">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ed-row',
  data() {
    return {}
  },
  props: {
    contentLeft: {
      type: String,
      default: ''
    },
    borderStyle: {
      type: Boolean,
      default: false
    }
  },
}
</script>
<style lang="scss" scoped>
.row-page {
  color: var(--text_1st);
  padding: 0 32px;
  background: var(--background);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  line-height: 44px;
  font-size: 28px;
  .content-left {
    min-width: 140px;
    padding: 20px 0px;
    flex-shrink: 0;
    color: var(--text_3rd);
  }
  .content-right {
    padding: 20px 0px 20px 10px;
    text-align: right;
    flex-grow: 1;
  }
  .border-right {
    border-bottom: 1px solid var(--line_01);
  }
}
</style>
