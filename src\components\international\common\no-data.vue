<template>
  <div class="no-data">
    <!-- <img class='icon' :src="theme==='dark' ? noDataIconDark : noDataIcon" /> -->
    <div>{{$t('fundMessage.notMessage')}}</div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
const noDataIcon = require('@/assets/images/international/no_content.png');
const noDataIconDark = require('@/assets/images/international/no_content.png')

export default {
  name: "no-data",
  data(){
    return {
      noDataIcon,
      noDataIconDark,
    }
  },
  computed: {
    ...mapState(['theme']),
  },
}
</script>

<style lang="scss">
.no-data {
  text-align: center;
  font-size: 28px;
  padding: 230px 0 256px;
  color: var(--text_1st);
  .icon {
    height: 400px;
    margin: 100px auto 10px;
  }
}
</style>

