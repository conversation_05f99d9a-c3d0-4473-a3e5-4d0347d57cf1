
<template>
  <div>
    <div v-if="!isInApp" :class="['download-app', theme]">
      <div class='left'>
        <div class='name'>{{$t('common.EDOneName')}}</div>
        <div class='introduction'>{{$t(`${$store.state.isInternational || $route.query.inter === 'inter' ? 'common.EDOneIntroduction2' : 'common.EDOneIntroduction'}`)}}</div>
      </div>
      <div class='right'>
        <div class='open' @click='download'>{{$t('common.OpenApp')}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { downloadEddidOne } from '@/utils/download'

export default {
  props: {
    type: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: '',
    }
  },
  data () {
    return {
      isInApp: true,
    }
  },
  computed: mapState(['theme']),
  mounted() {
    this.isInApp = this.$jsBridge.isSupported('getAppInfo');
  },
  methods: {
    download(){
      downloadEddidOne({ type: this.type })
    },
  },
}
</script>

<style lang="scss" scoped>
.download-app {
  background: var(--background);
  z-index: 2;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20px 30px;
  // box-shadow: 0 -2px 0 0 #eee inset;

  .left {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding-left: 82px;

    &::before {
      content: '';
      width: 66px;
      height: 66px;
      background: url('../../../assets/images/logo-ed.png') no-repeat center center;
      background-size: cover;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .name {
      color: var(--brand_01);
      font-size: 28px;
      line-height: 1;
      font-weight: 500;
    }

    .introduction {
      font-size: 20px;
      color: var(--text_3rd);
    }
  }

  .right {
    .open {
      height: 60px;
      line-height: 60px;
      color: #fff;
      background: #4082f9;
      border-radius: 8px;
      text-align: center;
      padding-left: 18px;
      padding-right: 18px;
      font-size: 24px;
    }
  }
}

.download-app.dark {
  // box-shadow: 0 -2px 0 0 #1d1d1d inset;
}
</style>