<template>
  <div class="footer-vue">
    <ul class="fund-kind-footer">
      <li>{{ $t('fundMessage.fundIntroduceInter.title') }}</li>
      <li>
        <p>{{ $t('fundMessage.fundIntroduceInter.content') }}</p>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.footer-vue {
  background: var(--background);
  padding: 48px 32px;
  .fund-kind-footer {
    padding:24px;
    background: var(--gray_05);
    border-radius: 10px;
    li {
      font-size: 24px;
      p {
        line-height: 36px;
        color: var(--text_3rd);
      }
      &:first-child {
        font-weight: 500;
        margin-bottom: 16px;
        text-align: center;
        color: var(--gray_01);
      }
    }
  }
}
</style>
