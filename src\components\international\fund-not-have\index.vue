<template>
  <div
    class="fund-not-have"
  >
    <div class="fund-not-have-box">
      <!-- <img v-if="companyImage" :src="require(`@/assets/images/international/no_content.png`)" alt="" />
      <img v-if="subordinateImage" :src="require(`@/assets/images/international/no_content.png`)" alt="" /> -->
      <div>
        {{ companyImage ? $t('fundMessage.correlationCompany') : $t('fundMessage.correlationFund') }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    companyImage: {
      type: Boolean,
    },
    subordinateImage: {
      type: Boolean,
    },
    synopsisShowAll: {
      type: Boolean,
    },
    fundData: {
      type: Boolean,
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapState(['theme']),
  },
}
</script>

<style lang="scss" scoped>
.fund-not-have {
  .fund-not-have-box {
    text-align: center;
    font-size: 28px;
    padding: 230px 0 256px;
    color: var(--text_1st);
    img {
      height: 400px;
      margin: 100px auto 10px;
    }
  }
}
</style>
