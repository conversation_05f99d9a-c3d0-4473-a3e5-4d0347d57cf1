<template>
  <div class="invest-pie-box">
    <div class="invest-pie-update-time" v-if="compositionsPie && compositionsPie.assetUpdateDate">
      {{ $t('fundMessage.updateTime') }}:{{ compositionsPie.assetUpdateDate }}
    </div>
    <div class="myChart-box">
      <canvas id="myChart_pie"></canvas>
      <ul v-if="compositionsPie && compositionsPie.assets && compositionsPie.assets.length">
        <li v-for="(item,index) in array" :key="index" class="pie-name-style">
          <span class="round" :class="'round' + index"></span>
          <span class="text">{{item.assetName}}</span>
          <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import F2 from '@antv/f2'
export default {
  props: ['pieFundIsin', 'compositionsPie'],
  data() {
    return {
      pieChart: [],
      array: []
    }
  },
  methods: {
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    
    chartPie(data) {
      let newArr = []
      if(data && data.length) {
        data.forEach((item) => {
          newArr.push({
            assetName: item.assetName,
            proportion: Math.abs(Number((item.proportion)*100).toFixed(2)), // 饼图数据需是数字类型
            type: 1,
          })
        })
      }
      // debugger
      
      const map = {}
      let corlorList = [];
      if(window.localStorage.getItem('theme') === 'dark') {
        corlorList = [
          '#5B8FF9',
          '#61DDAA',
          '#65789B',
          '#F6BD16',
          '#7262FD',
          '#78D3F8'
        ];
      }else {
        corlorList = [
          '#5B8FF9',
          '#61DDAA',
          '#65789B',
          '#F6BD16',
          '#7262FD',
          '#78D3F8'
        ];
      }
      const chart = new F2.Chart({
        id: 'myChart_pie',
        pixelRatio: window.devicePixelRatio,
        width: 180,
        height: 150,
        padding: '0',
      })
      chart.source(newArr)
      chart.tooltip(false);
      chart.legend(false
        // show:false,
        // position: 'right',
        // itemFormatter: function itemFormatter(val) {
        //   return val + '     ' + map[val]
        // },
        // nameStyle: {
        //   // 调整chart图文本显示宽度
        //   width: 100,
        // },
      )
      chart.coord('polar', {
        transposed: true,
        innerRadius: 0.7,
        radius: 1,
      })
      chart.axis(false)
      chart
        .interval()
        .position('type*proportion')
        .color('assetName', corlorList)
        .adjust('stack')
      chart.render()
    },
  },
  mounted() {
    if(this.compositionsPie && this.compositionsPie.assets && this.compositionsPie.assets.length) {
      this.compositionsPie.assets = this.compositionsPie.assets.sort(this.compare('proportion'))
      this.array = this.compositionsPie.assets.slice(0,5)
      let lastArray = this.compositionsPie.assets.slice(5,this.compositionsPie.assets.length)
      let num = 0;
      if (lastArray && lastArray.length) {
        lastArray.forEach((item) => {
          num = num + Number(item.proportion);
        })
        // 要处理--length > 5 时才会显示 其他状态
        let object = {
          assetName: this.$t('fundMessage.other'),
          proportion: num + ''
        }
        this.array = this.array.concat(object)
      }
      this.chartPie(this.array);
    }
  },
}
</script>

<style lang="scss" scoped>
.invest-pie-box {
  color: var(--text_1st);
  .invest-pie-update-time {
    font-size: 22px;
    color: var(--text_3rd);
  }
  .myChart-box {
    // width: 750px;
    height: 332px;
    position: relative;
    #myChart_pie {
      position: absolute;
      left: -50px;
      top: 10px;
    }
    display: flex;
    justify-content: space-between;
    align-items: top;
    ul {
      position: absolute;
      right: 0px;
      top: 30px;
      height: 332px;
      // width: 428px;
      width: 55%;
      li {
        width: 100%;
        color: var(--text_1st);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        span {
          display: inline-block;
          height: 42px;
          line-height: 42px;
        }
        .round {
          width: 16px;
          height: 16px;
          border-radius: 50% 50%;
          margin-left: 10px;
        }
        .round0 {
          background: #5B8FF9;
        }
        .round1 {
          background: #61DDAA;
        }
        .round2 {
          background: #65789B;
        }
        .round3 {
          background: #F6BD16;
        }
        .round4 {
          background: #7262FD;
        }
        .round5 {
         background: #78D3F8;
        }
        .text {
          font-size: 22px;
          padding-left: 14px;
          padding-right: 17px;
          width: 65%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .right-percent {
          font-size: 22px;
          width: 20%;
          text-align: left;
        }
      }
    }
  }
  .column-chart-li {
    margin-top: 20px;
  }
}
</style>
