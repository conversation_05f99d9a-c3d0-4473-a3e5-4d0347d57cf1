<template>
  <!-- 七年日化--charts图 -->
  <div class="annual-box">
    <div class="annual-chart-title">
      <p>
        <span></span>
        <span>{{ $t("fundMessage.thisFund") }}:</span>
        <!-- :class="Number(chartMessage.change) > 0 ? 'fund-money' : Number(chartMessage.change) ? 'fund-money1' : 'fund-money2'" -->
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(chartMessage.change) < 0
                ? 'fund-money1'
                : Number(chartMessage.change)
                ? 'fund-money'
                : 'fund-money2'
              : Number(chartMessage.change) < 0
              ? 'fund-money'
              : Number(chartMessage.change)
              ? 'fund-money1'
              : 'fund-money2'
          "
        >
          {{
            Number(chartMessage.change) > 0
              ? "+" + (Number(chartMessage.change) * 100).toFixed(2)
              : Number(chartMessage.change)
              ? (Number(chartMessage.change) * 100).toFixed(2)
              : "0.00"
          }}%
        </span>
      </p>
      <p>
        <span>{{ chartMessage.transDate }}</span>
        <span>{{ chartMessage.week }}</span>
      </p>
    </div>
    <canvas id="myChart"></canvas>
    <ul class="chart-tab-data">
      <li class="chart-tab-li">
        <div
          v-for="(item, index) in $t('fundMessage.fundSearchTimeCanvas')"
          :key="index"
          :class="{ chartActive: chartCurrent == item.key }"
          @click="checkChart(item)"
        >
          {{ item.label }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapState } from "vuex";
const F2 = require("@antv/f2");
const ScrollBar = require("@antv/f2/lib/plugin/scroll-bar");
F2.Chart.plugins.register(ScrollBar);
import { performance } from "@/services/fund";
export default {
  props: ["fundIsin", "type"],
  data() {
    return {
      chart: null,
      chartArray: [],
      chartMessage: {},
      chartCurrent: "LAST_MONTH",
    };
  },
  methods: {
    performance(type) {
      performance(
        { timeInterval: type },
        this.fundIsin,
        "",
        "trend-chart",
        "trend"
      ).then((res) => {
        // console.log('chart数据图表====', res)
        if (res.code == 200) {
          this.chartMessage = JSON.parse(JSON.stringify(res.data));
          // res.data.trendChartDTO.content --- 图标数据
          // res.data.trendChartDTO.header --- 坐标取值名称
          var newArr = [];
          const trendChartDTO = res.data?.trendChartDTO?.content || [];
          if(trendChartDTO.length > 0){
            trendChartDTO.forEach((item) => {
              newArr.push({
                date: item[0],
                changePercent: Number(item[3])
              });
            });
          // console.log("newArr=====", newArr);
          // 找最大最小值  找到之后 做左侧坐标轴
            this.drawChart(newArr);
          } else {
           this.drawChart([{}]); 
          }
        }
      });
    },
    drawChart(data) {
      // Step 1: 创建 Chart 对象
      let that = this;
      this.chart = new F2.Chart({
        id: "myChart",
        pixelRatio: window.devicePixelRatio, // 指定分辨率
        // padding: ["auto", 0, "auto", 45],
        padding: ["auto", 0, "auto", 50],
        plugins: ScrollBar,
        // height: 210,
      });
      // Step 2: 载入数据源
      // console.log(this.chartArray);
      this.chart.source(data, {
        date: {
          type: "timeCat",
          tickCount: 3,
          range: [0, 1],
        },
        changePercent: {
          // nice: true,
          tickCount: 5,
          // max: 5,
          // min: -5,
          formatter: function formatter(val) {
            return (val * 100).toFixed(2) + "%";
          },
        },
      });

      // Step 3：创建图形语法，绘制柱状图，由 genre 和 sold 两个属性决定图形位置，genre 映射至 x 轴，sold 映射至 y 轴
      // chart.interval().position('genre*sold').color('genre');
      this.chart.axis("date", {
        label: function label(text, index, total) {
          const textCfg = {
            textAlign: "center",
          };
          if (index === 0) {
            textCfg.textAlign = "left";
          } else if (index === total - 1) {
            textCfg.textAlign = "right";
          }
          return textCfg;
        },
      });
      this.chart.tooltip({
        showCrosshairs: true,
        custom: true, // 是否自定义 tooltip 提示框
        showXTip: true, // 是否展示 X 轴的辅助信息
        showYTip: true, // 是否展示 Y 轴的辅助信息
        snap: true, // 是否将辅助线准确定位至数据点
        crosshairsType: "xy", // 辅助线的种类
        crosshairsStyle: {
          // 配置辅助线的样式
          lineDash: [2], // 点线的密度
          // stroke: "rgba(45, 96, 224, 1)",
          stroke: "#012169", // 辅助线颜色
          lineWidth: 1,
        },
        xTipBackground: {
          fill: "#012169",
        },
        yTipBackground: {
          fill: "#012169",
        },
      });
      this.chart
        .area()
        .position("date*changePercent")
        .color(
          // this.theme == "dark"
          //   ? "l(90) 0:#4B7EFF 1:#21232D"
          //   : "l(90) 0:#4B7EFF 1:#FFFFFF"
          "l(90) 0:#012169 1:#0245A2"
        )
        .size(1);
      this.chart
        .line()
        .position("date*changePercent")
        .color(
          // this.theme == "dark"
          //   ? "l(90) 0:#4B7EFF 1:#4B7EFF"
          //   : "l(90) 0:#4B7EFF 1:#4B7EFF"
          "l(90) 0:#012169 1:#0245A2"
        )
        .size(1);

      // Step 4: 渲染图表
      this.chart.render();
    },
    checkChart(data) {
      // console.log('月份tab切换=====', data);
      this.chartCurrent = data.key;
      this.chart.clear();
      this.performance(this.chartCurrent);
    },
  },
  computed: {
    ...mapState(["theme", "token", "locale", "klineTheme"]),
  },
  mounted() {
    // this.$nextTick(()=>{
    //     this.drawChart();
    // });
    this.performance(this.chartCurrent);
  },
};
</script>

<style lang="scss" scoped>
.annual-box {
  .annual-chart-title {
    font-size: 22px;
    color: var(--text_1st);
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
      span {
        &:last-child {
          margin-left: 15px;
        }
      }
      &:first-child {
        span {
          &:first-child {
            display: inline-block;
            width: 14px;
            height: 14px;
            margin-right: 13px;
            border-radius: 50%;
            background: var(--brand_01);
          }
        }
      }
    }
  }
  #myChart {
    width: 100% !important;
    height: 100% !important;
  }
  // 去除 overflow: auto; 生成的滚动条
  ::-webkit-scrollbar {
    display: none;
  }
  .chart-tab-data {
    color: var(--text_3rd);
    font-size: 26px;
    padding-bottom: 32px;
    // width: 690px;
    width: 100%;
    overflow: auto;
    .chart-tab-li {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 768px;
      padding-right: 30px;
      div {
        line-height: 56px;
        width: 128px;
        text-align: center;
      }
      .chartActive {
        border-radius: 28px;
        color: var(--brand_01);
        background: var(--brand_05);
      }
    }
  }
}
</style>
