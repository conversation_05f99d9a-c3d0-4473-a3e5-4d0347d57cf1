<template>
  <div class="deal-rule-box">
    <ul class="deal-rule-select" v-if="tradeRulesData && JSON.stringify(tradeRulesData) !== '{}'">
      <li class="deal-rule-tab">
        <div v-for="(item, index) in $t('fundMessage.dealRuleTabs')" :key="index" @click="checkType(item)"
             :class="{ activeType: dealRuleCurrent == item.name }">
          <div>{{ item.label }}</div>
          <div :key="index" class="bottom-border" :class="{ activeBorder: dealRuleCurrent == item.name }"></div>
        </div>
      </li>
      <template v-if="dealRuleCurrent != 3">
        <!-- 步骤条 -->
        <li class="deal-rule-rate-time deal-rule-rate-top">
          <DealRuleComponent :dealRuleCurrent="dealRuleCurrent"
                             :shareConfirmedDay="shareConfirmedDay" :toTheAccount="toTheAccount"></DealRuleComponent>
        </li>
        <!-- 申购规则 -->
        <template v-if="dealRuleCurrent == 1">  
          <div class="fund-order-box">
            <div class="fund-order-rate">
              <!-- 申购费率 -->
              <div>
                <p>{{ $t("fundMessage.subscribe") }}{{ $t("fundMessage.rate") }}</p>
                <p v-if="showLodder()">
                  <template v-if="tradeRulesData && tradeRulesData.subscriptionRatePreferential.length > 1 && tradeRulesData.subscriptionRatePreferential[0].rate">
                    <div class="ladder-charge">
                      <span>{{ $t("fundMessage.ladderCharge") }}</span>
                      <img :src="require(`@/assets/images/international/${showLadderCharge ? 'fold' : 'unfold'}.png`)" alt="" @click="showLadderCharge = !showLadderCharge">
                    </div>
                  </template>
                  <template v-else>
                    <span class="span1" v-if="tradeRulesData.subscriptionRatePreferential&&tradeRulesData.subscriptionRatePreferential[0]">
                      {{
                        Number(tradeRulesData.subscriptionRatePreferential[0].rate)
                          ? (
                              tradeRulesData.subscriptionRatePreferential[0].rate * 100
                            ).toFixed(2)
                          : "0.00"
                      }}%
                    </span>
                    <span class="span2" v-if="tradeRulesData.subscriptionRate&&tradeRulesData.subscriptionRate[0]">
                      {{
                        Number(tradeRulesData.subscriptionRate[0].rate)
                          ? (tradeRulesData.subscriptionRate[0].rate * 100).toFixed(2)
                          : "0.00"
                      }}%
                    </span>
                  </template>
                </p>
                <p v-else>
                  <template v-if="tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate">
                    <div class="ladder-charge">
                      <span>{{ $t("fundMessage.ladderCharge") }}</span>
                      <img :src="require(`@/assets/images/international/${showLadderCharge ? 'unfold' : 'fold'}.png`)" alt="" @click="showLadderChargeFun">
                    </div>
                  </template>
                  <template v-else>
                    <span class="span1" v-if="tradeRulesData.subscriptionRate&&tradeRulesData.subscriptionRate[0]">
                      {{
                        Number(tradeRulesData.subscriptionRate[0].rate)
                          ? (tradeRulesData.subscriptionRate[0].rate * 100).toFixed(2)
                          : "0.00"
                      }}%
                    </span>
                    <span class="span2"></span>
                  </template>
                </p>
              </div>
              <!-- 赎回费率 -->
              <div>
                <!-- <p>
                  {{ $t("fundMessage.redemption") }}{{ $t("fundMessage.rate") }}
                </p>
                <p v-if="tradeRulesData.redemptionRatePreferential">
                  <span class="span1">
                    {{
                      Number(tradeRulesData.redemptionRatePreferential)
                        ? (tradeRulesData.redemptionRatePreferential * 100).toFixed(
                            2
                          )
                        : "0.00"
                    }}%
                  </span>
                  <span class="span2">
                    {{
                      Number(tradeRulesData.redemptionRate)
                        ? (tradeRulesData.redemptionRate * 100).toFixed(2)
                        : "0.00"
                    }}%
                  </span>
                </p>
                <p v-else>
                  <span class="span1">
                    {{
                      Number(tradeRulesData.redemptionRate)
                        ? (tradeRulesData.redemptionRate * 100).toFixed(2)
                        : "0.00"
                    }}%
                  </span>
                  <span class="span1"></span>
                </p> -->
                
                <!-- platformFeeRate: 平台费率 -->
                <p>{{ $t('myAccount.platformFeeRate') }}</p>
                <p v-if="tradeRulesData && tradeRulesData.platformFeeRate.length > 1 && tradeRulesData.platformFeeRate[0].rate" class="ladder-color ladder-charge">
                  <span>{{ $t("fundMessage.ladderCharge") }}</span>
                  <img :src="require(`@/assets/images/international/${platformLen1 ? 'unfold' : 'fold'}.png`)" alt="" @click="platformLen1Fun">
                </p>
                <p :class="{ 'platform-len1': tradeRulesData.platformFeeRate[0].rate }" v-else>
                  {{ tradeRulesData.platformFeeRate[0].rate ? formatRate(tradeRulesData.platformFeeRate[0].rate) : '0.00%' }}
                </p>
              </div>
            </div>
            <!-- 申购费率--阶梯式收费 -->
            <div class="ladder-charge-table" v-if="!platformLen1 && showLadderCharge">
              <ul>
                <li>
                  <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                  <div>{{ $t("myAccount.purchaseRates") }}</div>
                </li>
                <template v-if="showLodder()">
                  <template v-for="(item, index) in tradeRulesData.subscriptionRatePreferential">
                    <!-- <template v-for="(item1, index1) in tradeRulesData.subscriptionRatePreferential"></template> -->
                    <li :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                      <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                      <div>
                        <span>{{ (item.rate * 100).toFixed(2) }}%</span>
                        <span class="discounts-price" v-if="tradeRulesData.subscriptionRate[index].rate">{{ (tradeRulesData.subscriptionRate[index].rate * 100).toFixed(2) }}%</span>
                      </div>
                    </li>
                  </template>
                </template>
                <template v-else>
                  <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index" :class="{'background-grey': (index % 3 == 0)}">
                    <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                    <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                  </li>
                </template>
              </ul>
            </div>
            <!-- 平台费率--阶梯式收费 -->
            <div class="ladder-charge-table" v-if="!showLadderCharge && platformLen1">
              <ul>
                <li>
                  <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                  <div>{{ $t("myAccount.platformFeeRate") }}</div>
                </li>
                <template v-if="tradeRulesData.platformFeeRate.length > 1">
                  <li v-for="(item, index) in tradeRulesData.platformFeeRate" :key="index" :class="{'background-grey':  index % 2 != 0 && index != 0}">
                    <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                    <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                  </li>
                </template>
              </ul>
            </div>
          </div>
        </template>
        <!-- 赎回规则 -->
        <template v-else>
          <li class="deal-rule-rate-time deal-rule-rate-top">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[0].label }}</label>
              <span>
                {{ dealRuleValueRedem(tradeRulesData.minRansom) }}
              </span>
            </div>
          </li>
          <li class="deal-rule-rate-time">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[2].label }}</label>
              <span>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t('common.tradingDay')}}</span>
            </div>
          </li>
          <li class="deal-rule-rate-time">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[3].label }}</label>
              <span>T+{{ tradeRulesData.redemptionPlusDay }}{{ $t('common.tradingDay')}}</span>
            </div>
          </li>
        </template>
      </template>
      <!-- 费用说明 -->
      <template v-else>
        <li class="deal-rule-rate-time deal-rule-expense">
          <div class="deal-border-radius">
            <div class="expense-table">
              <!-- <p>{{ $t('fundMessage.subscriptionRate') }}</p> -->
              <p>{{ $t('fundMessage.subscriptionRateInter') }}</p>
              <p class="ladder-charge"  v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                <span>{{ $t("fundMessage.ladderCharge") }}</span>
                <img :src="require(`@/assets/images/international/${subscriptCost ? 'unfold' : 'fold'}.png`)" alt="" @click="subscriptCost = !subscriptCost">
              </p>
              <p :class="{ activeColor: !tradeRulesData.subscriptionRate[0].rate }" class="subscription-rate" v-else>
                {{
                  tradeRulesData.subscriptionRate[0].rate
                    ? formatRate(tradeRulesData.subscriptionRate[0].rate)
                    : '--'
                }}
              </p>
            </div>
            <!-- 申购费--阶梯式收费规则 -->
            <template v-if="subscriptCost">
              <div class="ladder-charge-table-box" v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                <div class="ladder-charge-table">
                  <ul>
                    <li>
                      <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                      <div>{{ $t("myAccount.purchaseRates") }}</div>
                    </li>
                    <template v-if="showLodder()">  
                      <li v-for="(item, index) in tradeRulesData.subscriptionRatePreferential" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                        <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                        <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                      </li>
                    </template>
                    <template v-else>
                      <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                        <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                        <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                      </li>
                    </template>
                  </ul>
                </div>
              </div>
            </template>
            <!-- 平台费 -->
            <div class="expense-table">
              <p>{{ $t('fundMessage.platformFeeRate') }}</p>
              <p v-if="tradeRulesData.platformFeeRate.length > 1" class="ladder-color ladder-charge">
                <span>{{ $t("fundMessage.ladderCharge") }}</span>
                <img :src="require(`@/assets/images/international/${platformCost ? 'unfold' : 'fold'}.png`)" alt="" @click="platformCost = !platformCost">
              </p>
              <p :class="{ activeColor: !tradeRulesData.platformFeeRate[0].rate }" v-else>
                {{
                  tradeRulesData.platformFeeRate[0].rate
                    ? formatRate(tradeRulesData.platformFeeRate[0].rate)
                    : '--'
                }}
              </p>
            </div>
            <!-- 平台费--阶梯式收费 -->
            <template v-if="platformCost">
              <div class="ladder-charge-table-box" v-if="tradeRulesData.platformFeeRate.length > 1">
                <div class="ladder-charge-table">
                    <ul>
                      <li>
                        <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                        <div>{{ $t("myAccount.platformFeeRate") }}</div>
                      </li>
                      <li v-for="(item, index) in tradeRulesData.platformFeeRate" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                        <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                        <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                      </li>
                    </ul>
                  </div>
              </div>
            </template>
          </div>
        </li>
      </template>
      <div class="more-data" @click="goRule">
        <span>{{ $t('fundMessage.moreMessage') }}</span>
        <img src="@/assets/images/international/more.png" alt="">
      </div>
    </ul>
  </div>
</template>

<script>
import DealRuleComponent from '@/components/international/dealRuleComponent'
import { tradeRules, performance } from '@/services/fund'
import { getClosedDays } from '@/services/account'

import moment from 'moment'
import { fmoney } from '@/utils/util.js'
export default {
  components: {
    DealRuleComponent
  },
  props: {
    ID: {
      type: String,
      default: ''
    },
    tradeRulesDetails: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tradeRulesData: {},
      dealRuleCurrent: 1,
      shareConfirmedDay: null,
      toTheAccount: 0,
      closeDayData: [],
      dataTotal: false,
      pageNum: 0,
      pageSize: 365,
      showLadderCharge: false,
      ruleTabData: { label: '申购规则', name: 1 },
      platformLen1: false, // 平台费率--阶梯式收费
      subscriptCost: false, // 申购费--阶梯式收费
      platformCost: false // 平台费--阶梯式收费
    }
  },
  methods: {
    formatRate(item) {
      return fmoney(item*100,2) + '%'
    },
    checkType(data) {
      this.ruleTabData = data;
      // tabs
      this.dealRuleCurrent = data.name;
      this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
      if(data.name === 1) {
        this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
      }else {
        this.toTheAccount = this.tradeRulesData.redemptionPlusDay
      }
    },
    dealRuleValue(item1, currency) {
      if (item1 && currency == 'HKD') {
        return fmoney(item1,2) + this.$t('myAccount.' + 'HKDollar')
      } else if(item1 && currency == 'USD'){
        return fmoney(item1,2) + this.$t('myAccount.' + 'dollar')
      } else {
        return '0.00'
      }
    },
    dealRuleValueRedem(item1) {
      if (item1) {
        return fmoney(item1,4) + this.$t('myAccount.copies')
      } else {
        return '0.00' + this.$t('myAccount.copies')
      }
    },
    performance() {
      let params = {
        page: this.pageNum, 
        size: this.pageSize,
        closeDateEnd: (new Date().getFullYear() + '-12-31'),
        closeDateStart: (new Date().getFullYear() + '-01-01')
      }
      getClosedDays(
        params,
        this,
        this.ID,
        this.$jsBridge.isSupported('getAppInfo')
      ).then((res) => {
        // 基金休息日
        // console.log('基金休息日====', res);
        if (res.code == 200) {
          if(res.data.totalElements <= this.pageSize) {
            this.dataTotal = false;
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
          }else {
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
            this.dataTotal = true;
          }
        }
      })
    },
    uploadData() {
      // 加载更多
      this.pageSize = this.pageSize + 15;
      this.performance()
    },
    showLodder () {
      if (this.tradeRulesData.subscriptionRatePreferential && (this.tradeRulesData.subscriptionRatePreferential.length > 1) && this.tradeRulesData.subscriptionRatePreferential[0].rate) {
        return true
      } else {
        return false
      }
    },
    showLadderChargeFun() {
      this.platformLen1 = false;
      this.showLadderCharge = !this.showLadderCharge;
    },
    platformLen1Fun() {
      this.showLadderCharge = false;
      this.platformLen1 = !this.platformLen1;
    },
    goRule() {
      console.log(this.ruleTabData);
      let {name, label} = this.ruleTabData
      this.$router.push({
        path: '/international/dealRule',
        query: {id: this.ID, type: name, label}
      })
    }
  },
  mounted() {
    this.dealRuleCurrent = this.$route.query.type ?  parseInt(this.$route.query.type) : 1
    this.tradeRulesData = this.tradeRulesDetails
    this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
    this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
    this.performance()
  },
}
</script>

<style lang="scss" scoped>
.deal-rule-box {
  height: 100%;
  overflow: auto;
  color: var(--text_1st);
  .deal-rule-select {
    .deal-rule-tab {
      padding: 22px 0 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 30px;
      color: var(--text_3rd);
      text-align: center;
      li {
        margin-right: 50px;
        // &:last-child {
        //   margin-right: 0;
        // }
      }
    }
    // 申购规则--赎回规则--费用说明
    .deal-rule-rate-time {
      padding: 0;
      font-size: 28px;
      .deal-rule-rate-time-list {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 28px 0;
        border-bottom: 1px solid var(--line_01) !important;
        &:last-child {
          border: 0;
        }
        .discount {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          .ladder-charge {
            width: 100%;
            margin: 0;
            font-size: 28px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            span {
              color: var(--text_1st);
              font-size: 26px;
            }
            img { 
              width: 36px;
              height: 36px;
              margin-left: 15px;
              margin-top: 8px;
            }
          }
        }
        .activeColor {
          color: var(--red);
          margin-right: 20px;
          max-width: 200px;
          text-align: center;
        }
        .line-through {
          color: var(--text_3rd);
          text-decoration: line-through;
          text-align: center;
          max-width: 200px;
        }
      }
      .deal-rule-rate-time-border {
        border: 0;
      }
      .rule-describe {
        font-size: 26px;
        line-height: 39px;
        padding: 41px 0 40px;
        p {
          &:last-child {
            margin-top: 50px;
          }
        }
      }
      .fund-rest {
        font-size: 34px;
        font-weight: bold;
        padding: 28px 0;
      }
      .fund-rest-list {
        font-size: 26px;
        padding: 27px 0;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border: 0;
        }
      }
      .expense-table {
        padding: 28px 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        border-top: 1px solid var(--line_01);
        &:first-child {
          border: 0;
        }
        p {
          width: 50%;
          &:last-child {
            text-align: right
          }
        }
        .ladder-color {
          color: var(--text_1st);
        }
        .subscription-rate {}
        .ladder-charge {
          margin: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          span {
            color: var(--text_1st);
            font-size: 28px;
          }
          img { 
            width: 36px;
            height: 36px;
            margin-left: 15px;
          }
        }
      }
      .expense-table-header {
        p {
          // height: 80px;
          text-align: center;
          // line-height: 80px;
          border: 1px solid;
          border-left: 0;
          border-right: 0;
          border-bottom: 0;
          @include border_color(lam-bg);
          @include background_color(lam-bg);
          @include font_color(text-color);
        }
      }
      .expense-remask {
        font-size: 24px;
        line-height: 42px;
        margin-top: 80px;
        @include font_color(second-text);
      }
      .all-list-data {
        padding: 27px 0;
        text-align: center;
        @include font_color(second-text);
        img {
          width: 28px;
        }
      }
    }

    .deal-rule-rate-top {
      margin-top: 20px;
    }
    // 申购规则
    .fund-order-box {
      border-top: 1px solid var(--line_01);
      .fund-order-state {
        padding: 40px 0 39px;
        // border-bottom: 1px solid;
        // @include border_color(line-color);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        ul {
          display: flex;
          justify-content: space-around;
          align-items: center;
          margin-bottom: 20px;
          text-align: center;
          .fund-img-li {
            width: 100%;
            display: flex;
            justify-content: center;
            img {
              width: 510px;
              height: 40px;
            }
          }
        }
        .last-ul {
          color: var(--text_1st);
          font-size: 24px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          li {
            margin: 0px;
            width: 33%;
            text-align: center;
          }
        }
      }
      .fund-order-rate {
        padding: 30px 0;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        color: var(--text_1st);
        font-size: 24px;
        div {
          width: 50%;
          &:last-child {
            margin-left: 20px;
          }
          p {
            &:last-child {
              margin-top: 8px;
              display: flex;
              align-items: flex-end;
              .span1 {
                font-size: 40px;
                font-weight: normal;
                color: var(--red);
                margin-right: 20px;
                word-break: break-all; // 解决数字英文字母不换行问题
              }
              .span2 {
                color: var(--text_3rd);
                text-decoration: line-through;
              }
            }
          }
          .ladder-charge {
            width: 100%;
            margin: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            span {
              color: var(--text_1st);
              font-size: 28px;
            }
            img { 
              width: 36px;
              height: 36px;
              margin-left: 15px;
            }
          }
        }
        .platform-len1 {
          color: var(--red);
          font-size: 40px;
          font-weight: normal;
        }
      }
      // 阶梯式收费
      .ladder-charge-table {
        // padding-bottom: 30px;
        border-bottom: 1px solid var(--line_01);
        ul {
          background: var(--background);
          li {
            font-size: 28px;
            font-weight: 400;
            color: var(--text_1st);
            padding: 18px 24px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            &:first-child {
              font-size: 26px;
              color: var(--text_3rd);
              background: var(--gray_05);
            }
            div {
              width: 70%;
              &:last-child {
                width: 30%;
                text-align: right;
              }
            }
          }
        }
        .background-grey {
          background: var(--gray_05);
        }
        .discounts-price {
          margin-left: 16px;
          color: var(--text_3rd);
          text-decoration: line-through;
        }
      }
    }
    .deal-rule-expense {
      color: var(--text_1st);
      background: var(--background);
      padding: 0;
      .ladder-charge-table-title {
        margin: 50px 0 30px;
        font-size: 26px;
        font-weight: 600;
        color: var(--text_1st);
      }
      .ladder-charge-table-box {
        .ladder-charge-table {
          ul {
            li {
              font-size: 28px;
              font-weight: 400;
              color: var(--text_1st);
              padding: 18px 24px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              &:first-child {
                color: var(--text_3rd);
                font-size: 26px;
                font-weight: 500;
                background: var(--gray_05);
              }
              div {
                width: 70%;
                &:last-child {
                  text-align: right;
                }
              }
            }
            .background-grey {
              background: var(--gray_05);
            }
          }
        }
      }
    }
  }
}
</style>
