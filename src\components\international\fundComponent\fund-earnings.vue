<template>
  <!-- 万元收益 -->
  <div class="fund-earnings-box">
    <div class="fund-earnings-header">
      <span>{{ $t("fundMessage.timeText") }}</span>
      <span>{{ $t("fundMessage.earnings") }}</span>
    </div>
    <ul class="fund-earnings-data">
      <li
        v-for="(item, index) in fundTimeData"
        :key="index"
        class="fund-earnings-li"
      >
        <span>{{ item.profitDate }}</span>
        <!-- :class="Number(item.cumulative10Thousand) < 0 ? 'output' : (Number(item.cumulative10Thousand) == 0 ? '':'entry')" -->
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(item.cumulative10Thousand) < 0
                ? 'output'
                : Number(item.cumulative10Thousand) == 0
                ? ''
                : 'entry'
              : Number(item.cumulative10Thousand) > 0
              ? 'output'
              : Number(item.cumulative10Thousand) == 0
              ? ''
              : 'entry'
          "
        >
          {{
            Number(item.cumulative10Thousand) > 0
              ? "+" + Number(item.cumulative10Thousand).toFixed(4)
              : Number(item.cumulative10Thousand).toFixed(4)
          }}
        </span>
      </li>
      <div class="more-data" @click="showComplete">
        <span>{{ $t('fundMessage.moreMessage') }}</span>
        <img src="@/assets/images/international/more.png" alt="">
      </div>
    </ul>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
export default {
  props: ["fundIsin"],
  data() {
    return {
      fundTimeData: [],
      pageNum: 0,
      pageSize: 5,
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showComplete() {
      this.$router.push({
        path: "/international/fundEarnings",
        query: { id: this.fundIsin },
      });
    },
    performance() {
      performance(
        { page: this.pageNum, size: this.pageSize },
        this.fundIsin,
        "",
        "ten-thousand"
      ).then((res) => {
        // console.log('万元收益=====', res)
        if (res.code == 200) {
          this.fundTimeData = res.data.content;
        }
      });
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.fund-earnings-box {
  .fund-earnings-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    color: var(--text_3rd);
    border: 1px solid var(--line_01);
    border-left: 0;
    border-right: 0;
  }
  .fund-earnings-data {
    font-size: 26px;
    color: var(--text_1st);
    .fund-earnings-li {
      padding: 27px 0;
      border-bottom: 1px solid var(--line_01);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
