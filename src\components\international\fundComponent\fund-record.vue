<template>
  <div class="fund-record-box">
    <ul class="fund-record-select">
      <li class="fund-record-tab">
        <div
          v-for="(item, index) in $t('fundMessage.interFundRecordTabs')"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: fundRecordCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: fundRecordCurrent == item.name }"
          ></div>
        </div>
      </li>
    </ul>
    <template v-if="this.fundRecordCurrent == 1">
      <ul class="fund-record-list">
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundAssetSize') }}</label>
          <div class="fund-record-label-right">
            {{ managerData.assetSize }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundEstablishTime') }}</label>
          <div class="fund-record-label-right">
            {{ managerData.establishTime }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t("fundMessage.fundCompany") }}</label>
          <div class="fund-record-label-right">
            {{ fundsNameFun(managerData, 'companyName') }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>ISIN</label>
          <div class="fund-record-label-right">{{ managerData.fundIsin }}</div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundStockType') }}</label>
          <div class="fund-record-label-right">{{ managerData.stockType }}</div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundBondType') }}</label>
          <div class="fund-record-label-right">
            {{ $t(`fundMessage.bondType.${managerData.type}`) }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundEstablishPlace') }}</label>
          <div class="fund-record-label-right">
            {{ managerData.establishPlace }}
          </div>
        </div>
      </ul>
    </template>
    <template v-else>
      <ul class="fund-file-box">
        <li class="fund-file-header">
          <span>{{ $t('fundMessage.type') }}</span>
          <span>{{ $t('fundMessage.updateTime') }}</span>
        </li>
        <li v-if="managerData.prospectusUrl" class="fund-file-catalog" @click="goPdf(1)">
          <div>
            <img src="@/assets/images/international/fund_file.png" alt="" />
            <span>{{ $t('fundMessage.fundStatement') }}</span>
          </div>
          <div>{{ managerData.prospectusUrlCnModifiedDate }}</div>
        </li>
        <li v-if="managerData.semiAnnualManagerReportUrl" class="fund-file-catalog" @click="goPdf(2)">
          <div>
            <img src="@/assets/images/international/fund_file.png" alt="" />
            <span>{{ $t('fundMessage.semiAnnualManagerReportUrl') }}</span>
          </div>
          <div>{{ managerData.semiAnnualManagerReportUrlModifiedDate }}</div>
        </li>
        <li v-if="managerData.annualManagerReportUrl" class="fund-file-catalog" @click="goPdf(3)">
          <div>
            <img src="@/assets/images/international/fund_file.png" alt="" />
            <span>{{ $t('fundMessage.annualManagerReportUrlEn') }}</span>
          </div>
          <div>{{ managerData.annualManagerReportUrlEnModifiedDate }}</div>
        </li>
        <li v-if="managerData.keyFactStatementUrl" class="fund-file-catalog" @click="goPdf(4)">
          <div>
            <img src="@/assets/images/international/fund_file.png" alt="" />
            <span>{{ $t('fundMessage.keyFactStatementUrlCn') }}</span>
          </div>
          <div>{{ managerData.keyFactStatementUrlCnModifiedDate }}</div>
        </li>
        <li v-if="managerData.providerFactSheetTUrl" class="fund-file-catalog" @click="goPdf(5)">
          <div>
            <img src="@/assets/images/international/fund_file.png" alt="" />
            <span>{{ $t('fundMessage.prospectusUrlCn') }}</span>
          </div>
          <div>{{ managerData.providerFactSheetModifiedDate }}</div>
        </li>
      </ul>
    </template>
    <div class="more-data" @click="moreData">
      <span>{{ $t('fundMessage.moreMessage') }}</span>
      <img src="@/assets/images/international/more.png" alt="">
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import Loading from '@/components/loading/index'
import { fundRecord } from '@/services/fund'
import { Toast } from 'mand-mobile'
import { getPdfDocumentUrl } from "@/utils/util";
export default {
  components: {
    Loading,
  },
  props: {
    ID: {
      type: String
    }
  },
  data() {
    return {
      fundRecordCurrent: 1,
      managerData: {},
      isLoading: true,
      updateTime: '',
    }
  },
  methods: {
    checkType(data) {
      this.fundRecordCurrent = data.name
    },
    moreData(){
      this.$router.push({
        path: "/international/fundRecord",
        query: { id: this.ID },
      });
    },
    fundRecord() {
      this.isLoading = true
      fundRecord(this.ID, '').then((res) => {
        // console.log('基金档案数据=====', res);
        if (res.code == 200) {
          this.managerData = res.data
          this.managerData.prospectusUrlCnModifiedDate = moment(res.data.prospectusUrlCnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.semiAnnualManagerReportUrlModifiedDate = moment(res.data.semiAnnualManagerReportUrlModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.annualManagerReportUrlEnModifiedDate = moment(res.data.annualManagerReportUrlEnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.keyFactStatementUrlCnModifiedDate = moment(res.data.keyFactStatementUrlCnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.providerFactSheetModifiedDate = moment(res.data.providerFactSheetModifiedDate).format(
            'YYYY-MM-DD'
          )
          
        }
        this.isLoading = false
      })
    },
    fundsNameFun(data, type) {
      if (this.locale == 'zh-hans') {
        return data[type] && data[type].cn ? data[type].cn : '-'
      } else if (this.locale == 'zh-hant') {
        return data[type] && data[type].hk ? data[type].hk : '-'
      } else {
        return data[type] && data[type].us ? data[type].us : '-'
      }
    },
    goPdf(num) {
      let url = false;
      let pdfTitle = '';
      let data = getPdfDocumentUrl(this.managerData)
      if (num == 1) {
        // 基金说明书
        pdfTitle = this.$t('fundMessage.fundStatement')
        url = data[this.locale].prospectusUrl
      } else if (num == 2) {
        pdfTitle = this.$t('fundMessage.semiAnnualManagerReportUrl')
        // 基金半年度报告
        url = data[this.locale].semiAnnualManagerReportUrl
      } else if (num == 3) {
        pdfTitle = this.$t('fundMessage.annualManagerReportUrlEn')
        // 基金年度报告
        url = data[this.locale].annualManagerReportUrl 
      } else if (num == 4) {
        pdfTitle = this.$t('fundMessage.keyFactStatementUrlCn')
        // 产品资料概况
        url = data[this.locale].keyFactStatementUrl
      } else if (num == 5) {
        // 基金说明书
        pdfTitle = this.$t('fundMessage.prospectusUrlCn')
        url = data[this.locale].providerFactSheetTUrl
      } 
      console.log(url,'------components------->',pdfTitle)
      if(url) {
        this.$jsBridge.run('openPdf', {
          pdfUrl: url,
          pdfTitle: pdfTitle
        })
      }else {
        Toast.info(this.$t('myAccount.noUseDocument'))
      }
    },
  },
  computed: {
    ...mapState(['theme', 'locale']),
  },
  mounted() {
    this.fundRecord()
  },
}
</script>

<style lang="scss" scoped>
.fund-record-box {
  height: 100%;
  overflow: auto;
  background: var(--background);
  .fund-record-select {
    .fund-record-tab {
      padding: 22px 30px 8px;
      color: var(--text_3rd);
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 30px;
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .fund-record-list {
    padding: 0;
    color: var(--text_1st);
    margin-top: 20px;
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-top: 0;
    }
    .fund-record-label {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding-top: 34px;
      font-size: 28px;
      .fund-record-label-right {
        width: 474px;
        color: var(--text_3rd);
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      &:last-child {
        padding-bottom: 30px;
      }
    }
  }
  .fund-file-box {
    background: var(--background);
    .fund-file-header {
      padding: 18px 0;
      margin-top: 16px;
      font-size: 26px;
      color: var(--text_3rd);
      border: 1px solid var(--line_01);
      border-left: 0;
      border-right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .fund-file-catalog {
      padding: 20px 0;
      color: var(--text_3rd);
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--line_01);
      div {
        &:first-child {
          color: var(--text_1st);
          display: flex;
          align-items: center;
          img {
            width: 48px;
            height: 48px;
            margin-right: 20px;
          }
        }
      }
    }
  }
}
</style>
