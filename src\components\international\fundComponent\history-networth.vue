<template>
  <!-- 历史净值 -->
  <div class="history-net-worth-box">
    <ul class="history-net-worth-data" v-if="fundTimeData && fundTimeData.length > 0">
      <li class="history-net-worth-li-header">
        <span>{{ $t("fundMessage.timeText") }}</span>
        <span>{{ $t("fundMessage.netValue") }}</span>
        <span>{{ $t("fundMessage.upDown") }}</span>
      </li>
      <li
        v-for="(item, index) in fundTimeData"
        :key="index"
        class="history-net-worth-li"
      >
        <span>{{ item.transDate }}</span>
        <span>{{ Number(item.netValue).toFixed(4) }}</span>
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(item.changePercent) < 0
                ? 'output'
                : Number(item.changePercent)
                ? 'entry'
                : 'entry1'
              : Number(item.changePercent) > 0
              ? 'output'
              : Number(item.changePercent)
              ? 'entry'
              : 'entry1'
          "
        >
          {{ Number(item.changePercent) > 0 ? "+" + (item.changePercent * 100).toFixed(2) : Number(item.changePercent) ? (item.changePercent * 100).toFixed(2) : "0.00" }}%
        </span>
      </li>
      <div class="more-data" @click="showHistoryNetWorth">
        <span>{{ $t('fundMessage.moreMessage') }}</span>
        <img src="@/assets/images/international/more.png" alt="">
      </div>
    </ul>
    <template v-else>
      <fund-not-have :companyImage="true"></fund-not-have>
    </template>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
import FundNotHave from '@/components/international/fund-not-have'
export default {
  props: ["fundIsin"],
  components: {
    FundNotHave
  },
  data() {
    return {
      fundTimeData: [],
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showHistoryNetWorth() {
      // 历史数据分页页面
      this.$router.push({
        path: "/international/historyNetworth",
        query: { id: this.fundIsin },
      });
    },
    performance() {
      performance(
        { page: 0, size: 5 },
        this.fundIsin,
        "",
        "netValues",
        "networth"
      ).then((res) => {
        // console.log('历史净值====', res);
        if (res.code == 200) {
          this.fundTimeData = res.data.content;
        }
      });
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.history-net-worth-box {
  .history-net-worth-data {
    font-size: 26px;
    color: var(--text_1st);;
    .history-net-worth-li-header,
    .history-net-worth-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 33.3%;
        &:nth-child(2) {
          text-align: center;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
    .history-net-worth-li-header {
      padding: 18px 0;
      font-size: 24px;
      color: var(--text_3rd);
      border: 1px solid var(--line_01);
      border-left: 0;
      border-right: 0;
    }
    .history-net-worth-li {
      padding: 27px 0;
      border-bottom: 1px solid var(--line_01);
    }
  }
}
</style>
