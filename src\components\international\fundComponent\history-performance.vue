<template>
  <div class="history-erformance-box">
    <template v-if="fundTimeData">  
      <div class="history-erformance-header">
        <span>{{ $t("fundMessage.timeArea") }}</span>
        <span>{{ $t("fundMessage.upDown") }}</span>
      </div>
      <ul class="history-erformance-data">
        <template v-for="(item, index) in fundTimeData">
          <li class="history-erformance-li" :key="index">
            <span>{{ $t(`fundMessage.dateTime.${index}`) }}</span>
            <span
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? item < 0
                    ? 'output'
                    : Number(item)
                    ? 'entry'
                    : 'second-text'
                  : item > 0
                  ? 'output'
                  : Number(item)
                  ? 'entry'
                  : 'second-text'
              "
            >
              {{ performanceNum(item) }}
            </span>
          </li>
        </template>
        <div class="more-data" @click="showHistory">
          <span>{{ $t('fundMessage.moreMessage') }}</span>
          <img src="@/assets/images/international/more.png" alt="">
        </div>
      </ul>
    </template>
    <template v-else>
      <fund-not-have :companyImage="true"></fund-not-have>
    </template>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
import FundNotHave from '@/components/international/fund-not-have'
export default {
  props: {
    fundIsin: {
      type: String,
    },
  },
  components: {
    FundNotHave
  },
  data() {
    return {
      fundTimeData: null,
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showHistory() {
      this.$router.push({
        path: "/international/historyPerformance",
        query: { id: this.fundIsin },
      });
    },
    performanceNum(data) {
      // console.log(Number(data));
      if (!data || !Number(data)) {
        return "0.00%";
      }
      if (Number(data) > 0) {
        return "+" + (data * 100).toFixed(2) + "%";
      } else {
        return (data * 100).toFixed(2) + "%";
      }
    },
  },
  mounted() {
    // console.log('fundIsin====', this.fundIsin);
    performance({}, this.fundIsin, "", "performance").then((res) => {
      // console.log(res);
      if (res.code == 200) {
        let newObj = {};
        // this.fundTimeData = res.data;
        // newObj[Object.keys(res.data)] = Object.values(res.data)
        Object.keys(res.data).forEach((item, i) => {
          if (
            [
              "cumulative1Week",
              "cumulative1M",
              "cumulative3M",
              "cumulative6M",
              "cumulative1Y",
            ].includes(item)
          ) {
            newObj[item] = Object.values(res.data)[i];
          }
        });
        this.fundTimeData = newObj;
      }
    });
  },
};
</script>

<style lang="scss" scoped>
.history-erformance-box {
  .history-erformance-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    color: var(--text_3rd);
    border: 1px solid var(--line_01);
    border-left: 0;
    border-right: 0;
  }
  .history-erformance-data {
    font-size: 26px;
    color: var(--text_1st);
    .history-erformance-li {
      padding: 27px 0;
      border-bottom: 1px solid var(--line_01);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
