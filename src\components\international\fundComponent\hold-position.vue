<template>
  <div class="hold-position-box" :class="{ investDetails: !investDetails }">
    <ul class="fund-possess-ratio-box">
      <li class="fund-possess-ratio">
        <p v-if="chartTitle">{{ $t('fundMessage.entrepotNumber') }}</p>
        <p>
          <span>{{ $t('fundMessage.updateTime') }}:&nbsp;</span>
          <span>{{compositionsPie.top10HoldingUpdateDate}}&nbsp;{{ weekText(compositionsPie.top10HoldingWeek) }}</span>
        </p>
      </li>
      <li class="column-chart-li" v-for="(item, i) in columnChart" :key="i">
        <div class="chart-title">{{ item.holdingName }}</div>
        <div class="chart-column">
          <div>
            <div
              class="chart-column-radius"
              :style="{
                background: chartColorArr[i],
                width: chartWidth(columnChart, item, i) + '%',
              }"
            ></div>
          </div>
          <div>{{ (item.proportion*100).toFixed(2) }}%</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    chartTitle: {
      type: Boolean,
    },
    investDetails: {
      type: Boolean,
    },
    columnFundIsin: {
      type: String,
    },
    compositionsPie: {
      type: Object,
    },
  },
  data() {
    return {
      columnChart: [],
      chartColorArr: [],
    }
  },
  watch: {
    compositionsPie(newV) {
      this.columnChart = newV.top10Holdings.sort(this.compare('proportion'))
    },
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    weekText(data) {
      let text
      switch (data) {
        case 'MONDAY':
          text = this.$t('common.weekTime.MONDAY')
          break
        case 'TUESDAY':
          text = this.$t('common.weekTime.TUESDAY')
          break
        case 'WEDNESDAY':
          text = this.$t('common.weekTime.WEDNESDAY')
          break
        case 'THURSDAY':
          text = this.$t('common.weekTime.THURSDAY')
          break
        case 'FRIDAY':
          text = this.$t('common.weekTime.FRIDAY')
          break
        case 'SATURDAY':
          text = this.$t('common.weekTime.SATURDAY')
          break
        case 'WEEKDAY':
          text = this.$t('common.weekTime.WEEKDAY')
          break
        default:
          break
      }
      return text
    },
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    chartWidth(data, item, i) {
      return (data[i].proportion*100) / data[0].proportion
    },
  },
  mounted() {
    if (localStorage.getItem('theme') == 'dark') {
      this.chartColorArr = [
        '#5B8FF9',
        '#61DDAA',
        '#65789B',
        '#F6BD16',
        '#7262FD',
        '#78D3F8',
        '#9661BC',
        '#F6903D',
        '#008685',
        '#F08BB4'
      ]
    } else {
      this.chartColorArr = [
        '#5B8FF9',
        '#61DDAA',
        '#65789B',
        '#F6BD16',
        '#7262FD',
        '#78D3F8',
        '#9661BC',
        '#F6903D',
        '#008685',
        '#F08BB4'
      ]
    }
    this.columnChart = this.compositionsPie.top10Holdings.sort(
      this.compare('proportion')
    )
  },
}
</script>

<style lang="scss" scoped>
.investDetails {
  height: 100%;
}
.hold-position-box {
  background: var(--background);
  color: var(--text_1st);
  .fund-possess-ratio-box {
    // height: 1006px;
    // height: 4.75rem;
    overflow: hidden;
    padding: 20px 0;
    color: var(--text_3rd);
    background: var(--background);
    .fund-possess-ratio {
      font-size: 30px;
      color: var(--text_1st);
      p {
        &:last-child {
          color: var(--text_3rd);
          margin-top: 10px;
          font-size: 22px;
        }
      }
    }
    .column-chart-li {
      margin-top: 20px;
      font-size: 20px;
      color: var(--text_1st);
      .chart-title {
        width: 80%;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
      }
      .chart-column {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;
        div {
          &:first-child {
            width: 80%;
            height: 22px;
          }
          &:last-child {
            width: 15%;
          }
        }
        .chart-column-radius {
          border-radius: 0px 6px 6px 0px;
        }
      }
    }
  }
}
</style>
