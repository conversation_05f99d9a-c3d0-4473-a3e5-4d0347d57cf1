<template>
  <div class="performance-trend-box">
    <div class="performance-trend-title">
      <p>
        <span></span>
        <span>{{ $t('fundMessage.thisFund') }}:</span>
        <span>+21.67</span>
      </p>
      <p>
        <span>2021-11-03</span>
        <span>周三</span>
      </p>
    </div>
    <canvas id="myChart"></canvas>
  </div>
</template>

<script>
const F2 = require('@antv/f2')
export default {
  data() {
    return {
      chartArray: [
        {
          time: '2016-08-08 00:00:00',
          tem: 10,
        },
        {
          time: '2016-08-08 00:10:00',
          tem: 22,
        },
        {
          time: '2016-08-08 00:30:00',
          tem: 16,
        },
        {
          time: '2016-08-09 00:35:00',
          tem: 26,
        },
        {
          time: '2016-08-09 01:00:00',
          tem: 12,
        },
        {
          time: '2016-08-09 01:20:00',
          tem: 26,
        },
        {
          time: '2016-08-10 01:40:00',
          tem: 18,
        },
        {
          time: '2016-08-10 02:00:00',
          tem: 26,
        },
        {
          time: '2016-08-10 02:20:00',
          tem: 12,
        },
      ],
    }
  },
  methods: {
    drawChart() {
      // Step 1: 创建 Chart 对象
      const chart = new F2.Chart({
        id: 'myChart',
        pixelRatio: window.devicePixelRatio, // 指定分辨率
        width: 350,
        height: 230,
        padding: 'auto',
      })

      // Step 2: 载入数据源
      // console.log(this.chartArray);
      chart.source(this.chartArray, {
        time: {
          type: 'timeCat',
          tickCount: 3,
          range: [0, 1],
        },
        tem: {
          tickCount: 5,
          min: 0,
        },
      })

      // Step 3：创建图形语法，绘制柱状图，由 genre 和 sold 两个属性决定图形位置，genre 映射至 x 轴，sold 映射至 y 轴
      // chart.interval().position('genre*sold').color('genre');
      chart.axis('time', {
        label: function label(text, index, total) {
          const textCfg = {
            textAlign: 'center',
          }
          if (index === 0) {
            textCfg.textAlign = 'left'
          } else if (index === total - 1) {
            textCfg.textAlign = 'right'
          }
          // textCfg.text = 'time:' + text;
          return textCfg
        },
      })
      chart.tooltip({
        showCrosshairs: true,
        custom: true, // 是否自定义 tooltip 提示框
        showXTip: true, // 是否展示 X 轴的辅助信息
        showYTip: true, // 是否展示 Y 轴的辅助信息
        snap: true, // 是否将辅助线准确定位至数据点
        crosshairsType: 'xy', // 辅助线的种类
        crosshairsStyle: {
          // 配置辅助线的样式
          lineDash: [2], // 点线的密度
          stroke: 'rgba(255, 0, 0, 0.25)',
          lineWidth: 2,
        },
      })

      chart
        .area()
        .position('time*tem')
        .color('l(90) 0:#1890FF 1:#f7f7f7')
        .size(1)
      chart
        .line()
        .position('time*tem')
        .color('l(90) 0:#1890FF 1:#f7f7f7')
        .size(1)

      // Step 4: 渲染图表
      chart.render()
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.drawChart()
    })
  },
}
</script>

<style lang="scss" scoped>
.performance-trend-box {
  .performance-trend-title {
    font-size: 22px;
    @include font_color(second-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
      span {
        &:last-child {
          margin-left: 15px;
        }
      }
      &:first-child {
        span {
          &:first-child {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-right: 13px;
            border-radius: 50%;
            @include background_color(tag-text-blue);
          }
          &:last-child {
            @include font_color(error-color);
          }
        }
      }
    }
  }
}
</style>
