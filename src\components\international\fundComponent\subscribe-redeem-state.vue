<template>
  <div class="subscribe-redeem-state">
    <!-- orderData.status == 'SUBMITTED' -->
    <img :src="require(`@/assets/images/international/illustrations_${orderData.status == 'SUBMITTED' ? 'success' : 'error'}.png`)" alt="pic" class="subscribe-redeem-image" />
    <div class="subscribe-redeem-state-text">
      {{ orderData.orderType == "Buy" ? $t("fundMessage.subscribe") : $t("fundMessage.redemption") }}
      {{ orderData.status == "SUBMITTED" ? $t("myAccount.submittedSuccessfully") : $t("myAccount.youSubmit") }}
    </div>
    <div v-if="orderData.status == 'SUBMITTED'" class="order-state-content">
      <div class="order-type">
        <img :src=" require(`@/assets/images/international/progress_bar_01.png`) " alt="pic" />
        <div v-if="orderData.orderType == 'Buy'" class="order-message order-message1">
          <!-- <p>{{ $t("myAccount.expectedT") }}{{ tradeRulesData.confirmationPlusDay}}{{ $t("myAccount.orderDetailBuy") }}</p>
          <p>{{ $t("myAccount.expectedT")}}{{ tradeRulesData.moneyConfirmedPlusDay}}{{ $t("myAccount.checkYield") }}</p> -->
          <div>
            <p>{{ $t("myAccount.ConfirmShare") }}</p>
            <p>
              {{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.shareConfirmedDay && moment(tradeRulesData.shareConfirmedDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}
              {{ $t("myAccount.ConfirmedWithNAV", {time: tradeRulesData.navConfirmedDay && moment(tradeRulesData.navConfirmedDay).format("YYYY-MM-DD")}) }}
            </p>
          </div>
          <div>
            <p>{{ $t("myAccount.StartViewingEarning") }}</p>
            <p>{{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.profitArriveDay && moment(tradeRulesData.profitArriveDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}</p>
          </div>
        </div>
        <div v-else class="order-message">
          <!-- <div>
            <p>{{ $t("myAccount.submit") + $t("fundMessage.redemption") + orderData.orderTime }}</p>
          </div>
          <div>
            <p>{{ $t("fundMessage.capitalReceive") }}&nbsp;{{ $t("fundMessage.estimateText") }}T+{{ tradeRulesData.redemptionPlusDay }}日</p>
          </div> -->
          <div>
            <p>{{ $t("myAccount.submit") + $t("fundMessage.redemption") }} </p>
            <p>{{ moment().format("YYYY-MM-DD HH:mm") }}</p>
          </div>
          <div>
            <p>{{ $t("fundMessage.capitalReceive") }}</p>
            <p>{{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.redemptionArriveDay && moment(tradeRulesData.redemptionArriveDay).endOf('day').format('YYYY-MM-DD HH:mm') }) }}</p>
          </div>
        </div>
      </div>
      <ul class="order-state-ul">
        <li>
          <label>
            {{ orderData.orderType == "Buy" ? $t("myAccount.paymentAccount") : $t("myAccount.redeemTo") }}
          </label>
          <div>{{ $t("myAccount.paymentAccountContent") }}</div>
        </li>
        <li>
          <label>{{ $t("myAccount.productName") }}</label>
          <div>
            {{ resolveLangName(locale,orderData.productName) }}
          </div>
        </li>
        <li>
          <label>
            {{ orderData.orderType == "Buy" ? $t("myAccount.subscriptionAmount") : $t("myAccount.redemptionShare") }}
          </label>
          <div>
            {{ orderData.orderType === "Buy" ? orderData.currency + " " + fmoney(orderData.amount,2) : Number(orderData.quantity) }}
          </div>
        </li>
      </ul>
    </div>
    <div v-else class="order-state-error">
      {{ $t("fundMessage.loseCause") }}{{ orderData.orderType == "Buy" ? $t("fundMessage.subscribe") : $t("fundMessage.redemption") }}
    </div>
    <div class="btn-type" :class="{ 'state-error': orderData.status != 'SUBMITTED' }">
      <md-button @click="goBack" type="primary" round>
        {{ orderData.status == "SUBMITTED" ? $t("myAccount.complete") : $t("myAccount.return") }}
      </md-button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
import { fmoney } from "@/utils/util.js";
import { resolveLangName } from "@/utils/fund-type";
export default {
  computed: {
    ...mapState(["theme", "locale"]),
  },
  props: {
    orderData: {
      type: Object,
    },
    tradeRulesData: {
      type: Object,
    },
  },
  data() {
    return {
      fmoney,
      resolveLangName
    };
  },
  mounted() {
    this.orderData.orderTime = moment(this.orderData.orderTime).format("YYYY-MM-DD HH:mm");
  },
  methods: {
    moment,
    goBack() {
      // 跳转基金账户页面
      this.$jsBridge.run("toPage", {
        jumpType: "NATIVE",
        loginState: "JUDGMENT",
        openAccountState: "JUDGMENT_FUND",
        navigationContentCode: "FUND_ACCOUNT",
        navigationUri: "FUND_ACCOUNT",
        titleDisplay: "DISPALY",
      });
      // this.$router.go(-1)
    },
  },
};
</script>
<style>
.md-button-inner {
  font-size: 28px !important;
}
</style>
<style lang="scss" scoped>
.subscribe-redeem-state {
  text-align: center;
  min-height: 100vh;
  background: var(--background);
  .subscribe-redeem-image {
    width: 160px;
    height: 160px;
    margin-top: 86px;
  }
  .subscribe-redeem-state-text {
    font-size: 36px;
    font-weight: 500;
    margin-top: 24px;
  }
  .order-state-content {
    .order-type {
      display: flex;
      justify-content: center;
      align-items: top;
      font-size: 26px;
      padding: 44px 105px;
      // margin: 60px 0;
      margin-bottom: 80px;
      img {
        width: 48px;
        height: 187px;
      }
      .order-message {
        text-align: left;
        margin-left: 36px;
        color: var(--gray_01);
        font-size: 26px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        div {
          &:last-child {
            margin-top: 40px;
          }
          p {
            &:last-child {
              margin-top: 16px;
            }
          }
        }
      }
      .order-message1 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .order-state-ul {
      padding: 0 32px;
      font-size: 28px;
      background: var(--background);
      li {
        padding: 18px 0;
        display: flex;
        justify-content: space-between;
        align-items: top;
        border-bottom: 1px solid var(--line_01);
        label {
          color: var(--text_3rd);
          flex-shrink: 0;
          padding-right: 10px;
        }
        &:last-child {
          border-bottom: 0;
        }
        div {
          width: 470px;
          text-align: right;
        }
      }
    }
  }
  .order-state-error {
    margin-top: 105px;
    font-size: 26px;
    color: var(--text_3rd);
  }
  .btn-type {
    padding: 80px 32px 0;
  }
  .state-error {
    margin-top: 569px;
  }
}
</style>
