<template>
  <div
    v-if="isshow"
    @touchstart.stop.prevent="TouchDisable"
    @touchmove.stop.prevent="TouchDisable"
    class="md-toast center"
  >
    <div class="md-popup with-mask center" style="">
      <div class="md-popup-mask" style=""></div>
      <div class="md-popup-box md-fade" style="">
        <div class="md-toast-content">
          <md-icon name="spinner" size="lg" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  data: function () {
    return {
      isshow: false,
    }
  },
  methods: {
    TouchDisable() {
      return false
    },
  },
}
</script>
