<template>
  <div class="label-header-box" :class="[theme]">
    <img :src="require(`@/assets/images/eddid_${theme}.png`)" alt="" class="logo-image">
    <div class="label-title-box">
        <div class="label-left-box"></div>
        <div class="label-title">{{ labelTitle }}</div>
        <div class="label-top-border"></div>
    </div>
    <img :src="require(`@/assets/images/quotation_${theme}.png`)" alt="" class="label-bottom-image">
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  props: {
    labelTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: mapState(['theme'])
}
</script>

<style lang="scss" scoped>
.label-header-box {
  margin-top: 100px;
  padding-bottom: 32px;
  .logo-image {
    width: 166px;
    height: 40px;
  }
  .label-title-box {
    margin-top: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    .label-left-box {
      width: 16px;
      height: 16px;
      background: #012169;
    }
    .label-title {
      width: 28%;
      color: #012169;
      font-size: 36px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: bold;
      margin: -10px 14px 0 8px;
      // line-height: 50px;
    }
    .label-top-border {
      width: 70%;
      border-top: 1px solid #012169;
    }
  }
  .label-bottom-image {
    width: 48px;
    height: 48px;
  }
}

.dark {
  .label-title-box {
    .label-left-box {
      background: #F0F3F7;
    }
   .label-title {
      color: #F0F3F7;
    }
    .label-top-border {
      border-color: #F0F3F7;
    }
  }
}
</style>