/* 解决 ios 1px 显示略粗的问题 */
.ed-border,
.border-top,
.border-right,
.border-bottom,
.border-left {
  position: relative;
}
/* 上边框 */
.border-top:before {
  border-top: 1px solid #F1F4F9;
  @include border_color(input-line);
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform-origin: left top;
  -webkit-transform-origin: left top;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-top:before {
    transform: scaleY(0.5);
    -webkit-transform: scaleY(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-top:before {
    transform: scaleY(0.3333);
    -webkit-transform: scaleY(0.3333);
  }
}
/* 右边框 */
.border-right:before {
  border-top: 1px solid #F1F4F9;
  @include border_color(input-line);
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  transform-origin: right top;
  -webkit-transform-origin: right top;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-right:before {
    transform: scaleX(0.5);
    -webkit-transform: scaleX(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-right:before {
    transform: scaleX(0.3333);
    -webkit-transform: scaleX(0.3333);
  }
}
/* 下边框 */
.border-bottom:before {
  border-top: 1px solid #F1F4F9;
  @include border_color(input-line);
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  transform-origin: left bottom;
  -webkit-transform-origin: left bottom;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-bottom:before {
    transform: scaleY(0.5);
    -webkit-transform: scaleY(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-bottom:before {
    transform: scaleY(0.3333);
    -webkit-transform: scaleY(0.3333);
  }
}
/* 左边框 */
.border-left:before {
  border-top: 1px solid #F1F4F9;
  @include border_color(input-line);
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform-origin: left top;
  -webkit-transform-origin: left top;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-left:before {
    transform: scaleX(0.5);
    -webkit-transform: scaleX(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-left:before {
    transform: scaleX(0.3333);
    -webkit-transform: scaleX(0.3333);
  }
}
/* 四周边框 */
.ed-border:before {
  border: 1px solid #bdc0d0;
  @include border_color(input-line);
  content: "";
  display: block;
  width: 100%;
  height: 200%;
  position: absolute;
  left: 0;
  top: 0;
  transform-origin: top left;
  -webkit-transform-origin: top left;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .ed-border:before {
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .ed-border:before {
    transform: scale(0.3333);
    -webkit-transform: scale(0.3333);
  }
}