/* ============ mand-mobile UI strat ============ */
.international.app {
  & .md-button.default {
    background: var(--background);
    color: var(--text_1st);
    &::after {
      border: 1px solid var(--line_01);
    }
  }

  & .md-button.primary:not(.plain) {
    border: 0;
    background: var(--brand_01);
    &::after {
      content: none;
    }
  }

  & .md-button.bulletin:not(.plain) {
    background: var(--brand_07);
    color: var(--text_1st);
    &::after {
      content: none;
    }
  }

  & .md-field {
    background: var(--background);
  }

  & .md-field-item-control {
    color: var(--text_1st);
  }

  & .md-input-item-fake,
  & .md-input-item-input {
    color: var(--text_1st);
  }

  & .md-input-item-input::placeholder {
    color: var(--text_3rd);
    font-size: 30px;
    line-height: 1;
  }
}

#app.international {
  & .md-field-item-content {
    background: var(--background);

    &::before {
      border-color: var(--line_01);
    }
  }
}

.international .md-popup-mask {
  background: var(--mask);
}
.inter-dialog-confirm {
  .md-dialog-content {
    background: var(--background);
    width: 550px;
    padding: 48px 20px;
    border-radius: 30px;
  }

  .md-dialog-body {
    background-color: transparent;
    padding: 0;

    .md-dialog-title {
      margin-bottom: 30px;
      color: var(--text_1st);
      font-size: 34px;
      line-height: 52px;
      font-weight: 600;
    }
    .md-dialog-text {
      color: var(--text_2nd);
      font-size: 28px;
      line-height: 40px;
      font-weight: 400;
      margin-bottom: 40px;
      text-align: center;
    }
  }

  .md-dialog-actions {
    margin-top: 20px;

    &::after {
      content: none;
    }

    .md-dialog-btn {
      height: 80px;
      font-size: 28px;
      line-height: 44px;
      border-radius: 40px;
    }
    .md-dialog-btn + .md-dialog-btn {
      margin-left: 30px;
    }

    .md-dialog-btn:first-child {
      background: var(--background);
      color: var(--text_1st);

      &::before {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        border: 1px solid var(--gray_02);
        border-radius: 40px;
        transform: scale(1);
        transform-origin: 0 0;
      }
    }
    .md-dialog-btn:last-child {
      background: var(--brand_01);
      color: var(--text_5th);
    }
  }
}

.international {
  .padding-20 {
    .md-button.md-button.round:first-child {
      border: none;
    }
  }
  // 支付
  .md-number-keyboard {
    color: var(--text_1st);
    font-size: 54px;
    .md-popup {
      .md-popup-box {
        // .number-keyboard-header {
        //   font-size: 22px;
        //   color: var(--brand_01);
        //   padding: 14px 0 16px;
        //   background: var(--gray_05);
        //   display: flex;
        //   justify-content: center;
        //   align-items: center;
        //   img {
        //     width: 75px;
        //     height: 22px;
        //     margin-right: 16px;
        //   }
        // }
        .md-number-keyboard-container {
          .keyboard-operate {
            .keyboard-operate-list {
              .delete {
                color: var(--text_1st);
              }
              .confirm {
                color: var(--text_5th);
                background: var(--brand_01);
              }
            }
          }
        }
      }
    }
  }
  // 按钮
  .md-button.round {
    height: 80px;
    margin-right: 22px;
    border-radius: 100px;
    &:first-child {
      border: 1px solid var(--gray_02);
    }
    &:last-child {
      margin-right: 0;
    }
  }
  .md-button.default {
    border: 1px solid var(--line_02);
  }
  .entry {
    color: var(--red);
  }
  .entry1 {
    color: var(--text_3rd);
  }
  .output {
    color: var(--green);
  }
  .fund-money {
    color: var(--red);
  }
  .fund-money1 {
    color: var(--green);
  }
  .fund-money2 {
    color: var(--text_3rd);
  }
  .more-data {
    padding: 20px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 36px;
      height: 36px;
      margin-left: 8px;
      margin-top: 2px;
    }
  }
  .bottom-border {
    margin: 8px auto 0;
    width: 32px;
    height: 8px;
    border-radius: 3px;
  }
  .activeType {
    text-align: center;
    color: var(--text_1st);
    opacity: 1;
    font-family: "PingFangSC-Medium";
    font-weight: 500;
    font-style: normal;
    letter-spacing: 0px;
    .activeBorder {
      border-radius: 8px;
      background: var(--brand_01);
    }
  }

  // 输入框默认文本字体颜色
  input::-webkit-input-placeholder{
    color:var(--text_3rd);
  }
  input::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:var(--text_3rd);
  }
  input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:var(--text_3rd);
  }
  input:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:var(--text_3rd);
  }

  // toast--样式
  .md-toast-content {
    background: #000000;
    opacity: 0.75;
    padding: 24px 40px;
    color: var(--text_5th);
    font-size: 30px;
    border-radius: 16px;
    backdrop-filter: blur(20px);
  }
  .md-toast-text {
    color: var(--text_5th);
    font-size: 30px;
    line-height: 46px;
    font-weight: 400;
  }
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .inter-dialog-confirm .md-dialog-actions .md-dialog-btn:first-child::before {
    width: 200%;
    height: 200%;
    border-radius: 80px;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .inter-dialog-confirm .md-dialog-actions .md-dialog-btn:first-child::before {
    width: 300%;
    height: 300%;
    border-radius: 120px;
    transform: scale(0.3333);
  }
}

