@font-face {
  font-family: 'D-DIN';
  src: url("~@/assets/font/D-DIN.ttf") format('trueType');
  font-weight: normal;
  font-style: normal;
}

html,
body {
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 28px;
}
//html *,
html :after,
html :before {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
a {
  text-decoration: none;
  color: $primary-color;
}
.container {
  padding: 30px;
}
.x-between-y-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mgt20 {
  margin-top: 20px;
}
.mgb50 {
  margin-bottom: 50px;
}
.mgb70 {
  margin-bottom: 70px;
}
/* ============ 公共 UI end ============ */

/* ============ mand-mobile UI strat ============ */
.md-button {
  height: 90px;
  line-height: 28px;
  font-size: 28px;
  font-weight: 400;
  border-radius: 2px;
}
.md-button.default {
  @include background_color(bg-color);
  @include font_color(btn-default);

  &::after {
    border: 1px solid transparent;
    @include border_color(input-line);
  }
}
.md-button.primary:not(.plain) {
  background-color: $primary-color;
  &::after {
    content: none;
  }
}
// pre {
//   white-space: pre-line;
//   line-height: 42px;
//   font-size: 26px;
//   overflow: auto;
//   word-break: break-all;
//   word-wrap: break-word;
//   // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
//   //   Microsoft YaHei, 微软雅黑, Arial, sans-serif;
//   // color: $number-colr
// }
// // table样式
// .common-tab-bar.md-tab-bar {
//   .md-tab-bar-list  {
//     width: 100%;
//     .md-tab-bar-item {
//       font-size: 30px;
//       color: #666;
//        &.is-active {
//          color: #333;
//          position: relative;
//         &::before {
//           content: '';
//           position: absolute;
//           bottom: 0;
//           left:50%;
//           transform: translateX(-50%);
//           width: 20px !important;          height: 6px;
//           background: #3773e1;
//           border-radius: 4px;
//         }
//        }
//     }
//   }
//   .md-tab-bar-ink {
//     display: none;
//   }
// }
.md-toast-text{
  white-space: pre-wrap;
}

.md-field {
  @include background_color(bg-color);
}
#app .md-field-item-content {
  @include background_color(bg-color);
  // min-height: 80px;

  &::before {
    content: "";
    z-index: 0;
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #bdc0d0;
    @include border_color(input-line);
    transform-origin: top left;
    border-radius: 2px;
    transform: scale(1);

    @media screen and(-webkit-min-device-pixel-ratio: 2) {
      width: 200%;
      height: 200%;
      border-radius: 4px;
      transform: scale(0.5);
    }
    @media screen and(-webkit-min-device-pixel-ratio: 3) {
      width: 300%;
      height: 300%;
      border-radius: 6px;
      transform: scale(0.3333);
    }
  }
}
#app .md-field-item.is-error .md-field-item-content::before {
  @include border_color(status-error);
}
.md-field-item-control {
  @include font_color(text-color);
}
.app .md-input-item-fake,
.app .md-input-item-input {
  height: 80px;
  padding: 0 20px;
  @include font_color(text-color);
}
.app .md-input-item-input::placeholder {
  @include font_color(tip-text);
  font-size: 30px;
  line-height: 1;
}
.app .md-field-item.is-android .md-field-item-control,
.app .md-input-item.is-android .md-input-item-fake,
.app .md-input-item.is-android .md-input-item-input {
  font-weight: 400;
}

.md-popup-mask {
  @include background_color(bg-mask);
}
.dialog-confirm {
  .md-dialog-content {
    @include background_color(bg-dialog);
    width: 500px;
    border-radius: 4px;
  }

  .md-dialog-body {
    background-color: transparent;
    padding: 80px 24px 70px;

    .md-dialog-title {
      margin-bottom: 30px;
      @include font_color(text-color);
      font-size: 28px;
      line-height: 28px;
      font-weight: 500;
    }
    .md-dialog-text {
      @include font_color(second-text);
      font-size: 28px;
      line-height: 32px;
      font-weight: 400;
      text-align: center;
    }
  }

  .md-dialog-actions {
    .md-dialog-btn {
      height: 90px;
      font-size: 28px;
      line-height: 28px;
    }
    .md-dialog-btn:first-child {
      @include background_color(bg-btn-default);
      @include font_color(second-text);

      &::before {
        content: none;
      }
    }
    .md-dialog-btn:last-child {
      background-color: $primary-color;
      color: #FFFFFF;
    }
    &::after {
      content: none;
    }
  }
}

// 私募基金--dialog单独样式
.dialog-confirm-private {
  .md-dialog-content {
    width: 500px;
    border-radius: 30px;
    padding: 48px 40px;
    background: #fff;
    .md-dialog-body {
      color: #1C212A;
      padding: 0;
      .md-dialog-title {
        font-size: 34px;
        font-weight: bold;
      }
      .md-dialog-text {
        font-size: 28px;
        line-height: 40px;
        text-align: center;
      }
    }
    .md-dialog-actions {
      font-size: 28px;
      margin-top: 48px;
      border-radius: 40px;
      background: #012169;
      &::after {
        border: none;
      }
      .md-dialog-btn {
        &:last-child {
          height: 80px;
          line-height: 80px;
          color: #F1F3F7;
        }
      }
    }
  }
}

body[data-theme="dark"] {
  .dialog-confirm .md-dialog-body {
    @include thin_border($directions: (top, right, left));
    &::after {
      border-color: #000;
    }
  }
  .md-dialog-actions .md-dialog-btn:first-child {
    @include thin_border($directions: (top, bottom, left), $radius: (0, 0, 0, 4px));
    &::after {
      border-color: #000;
    }
  }
  // 私募基金--dialog单独样式
  .dialog-confirm-private {
    .md-dialog-content {
      background: #1B1D28;
      .md-dialog-body {
        color: #F0F3F7;
        .md-dialog-title {
          color: #F0F3F7;
        }
      }
      .md-dialog-actions {
        background: #173A87;
      }
    }
  }
}
// .md-cell-item-content{
//   @include font_color(text-color)
// }
// .md-cell-item-body:before{
//   @include border_color(split-line)
// }

// .md-tab-bar {
//   @include background_color(background-color);
// }
// .md-tab-bar-item {
//   @include font_color("tab-color");
// }
// .md-tab-bar-item {
//   &.is-active {
//     @include font_color("text-color");
//   }
// }
// .md-picker-column{
//   @include background_color(item-background-color);
// }
// .md-picker-column-masker.top:before{
//   @include border_color(split-line)
// }
// .md-picker-column-masker.bottom:after{
//   border-width: 4px;
//   @include border_color(split-line)
// }
/* ============ mand-mobile UI end ============ */
