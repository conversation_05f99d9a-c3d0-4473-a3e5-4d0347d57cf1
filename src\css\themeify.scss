@import "./theme.scss";

/*
  使用demo
  .app-home {
    font-size: 18px;
    @include themeify {
      color: themed('font-color');
    }
  }
 */

@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    $theme-map: $theme-map !global;
    body[data-theme="#{$theme-name}"] & {
      @content;
    }
  }
}
//声明一个根据Key获取颜色的function
@function themed($key) {
  @return map-get($theme-map, $key);
}

//获取背景颜色
@mixin background_color($color) {
  @include themeify {
    background-color: themed($color);
  }
}
// 阴影
@mixin box_shadow( $color) {
  @include themeify {
    box-shadow: 0px 2px 10px 0px themed($color);
  }
}
//获取字体颜色
@mixin font_color($color) {
  @include themeify {
    color: themed($color);
  }
}
//获取边框颜色
@mixin border_color($color) {
  @include themeify {
    border-color: themed($color);
  }
}
@mixin border_direction_color($direction: left, $color: red, $size: 2px) {
  @include themeify {
    border-#{$direction}: $size solid themed($color);
  }
}


// 1px border
@mixin thin_border($directions: (top, right, bottom, left), $color: line-color, $radius: (0, 0, 0, 0), $position: after) {
  $isOnlyOneDir: string==type-of($directions);
  @if ($isOnlyOneDir) {
    $directions: ($directions);
  }

  position: relative;
  &::#{$position} {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    pointer-events: none;
    transform: scale(1);
    transform-origin: 0 0;
    // 添加 border
    @each $direction in $directions {
      border-#{$direction}: 1px solid transparent;
    }

    @include themeify {
      border-color: themed($color);
    }
    // 添加 border-radius
    @if (list==type-of($radius)) {
      border-radius: nth($radius, 1)  nth($radius, 2)  nth($radius, 3) nth($radius, 4);
    } @else {
      border-radius: $radius;
    }
  }

  @media only screen and (-webkit-min-device-pixel-ratio: 2) {
    &::#{$position} {
      width: 200%;
      height: 200%;
      @if (list==type-of($radius)) {
        border-radius: nth($radius, 1) * 2 nth($radius, 2) * 2 nth($radius, 3) * 2 nth($radius, 4) * 2;
      } @else {
        border-radius: $radius * 2;
      }
      transform: scale(0.5);
      transform-origin: 0 0;
    }
  }

  @media only screen and (-webkit-min-device-pixel-ratio: 3) {
    &::#{$position} {
      width: 300%;
      height: 300%;
      @if (list==type-of($radius)) {
        border-radius: nth($radius, 1) * 3 nth($radius, 2) * 3 nth($radius, 3) * 3 nth($radius, 4) * 3;
      } @else {
        border-radius: $radius * 3;
      }
      transform: scale(0.3333);
      transform-origin: 0 0;
    }
  }
}
