export default {
  pleaseEnter: 'Please enter',
  pleaseSelect: 'Please select',
  pleaseEnterRight: 'Please enter a correct',
  common: 'Common',
  pleaseUpload: 'Please upload a Bank statement',
  select: 'Select',
  btns: {
    modify: 'Modify',
    confirm: 'Confirm',
    affirm: 'Confirm',
    reset: 'Reset',
    back: 'Back',
    next: 'Next',
    cancel: 'Cancel',
    apply: 'Apply',
    toDeposit: 'To make deposit',
    imApply: 'Apply now',
    goto: 'Go to',
    examinecause: 'Check the reasons',
    submit: 'Submit',
    resign: 'Re-sign',
    gotIt: 'Got it',
    goOn: 'Continue',
  },
  integer: 'Please enter a positive and integal number',
  formatIncorrect: 'Incorrect format',
  tradingDay: 'Trading day',
  fundClose: 'Fund closed date',
  weekTime: {
    MONDAY: 'Monday',
    TUESDAY: 'Tuesday',
    WEDNESDAY: 'Wednesday',
    THURSDAY: 'Thursday',
    FRIDAY: 'Friday',
    SATURDAY: 'Saturday',
    WEEKDAY: 'Sunday',
  },
  EDOneName: 'Eddid ONE',
  EDOneIntroduction: 'Invest smart, pick Eddid',
  EDOneIntroduction2: "One-stop Trading APP",
  OpenApp: 'Open the APP',
  year: '',
  month: '',
  day: '',
  productDevelop: "New product is coming soon",
  networkAnomalyCheck: "Network exception, please check the network!",
  networkAnomalyLogin: "Network exception, please login again!",
  networkAnomalyRetry: "Network exception, please try again later!",
  Later: "Remind me later",
  SubmitQuestionnaire: "Submit questionnaire",
  Expired: "The validity period of your risk profile assessment has expired. To avoid any disruption to your fund subscriptions, please complete the Risk Profile Questionnaire as soon as possible.",
  ExpiringSoon: "The validity period of your risk profile assessment will expire on {time}. To avoid any disruption to your fund subscriptions, please complete the Risk Profile Questionnaire as soon as possible.",
  hotline: "Customer service hotline: +852 2655 0338 / Trading hotline: +852 2655 0328",
  pullfresh: "Pull down to refresh",
  releaseRefresh: "Release to refresh",
  refreshing: "Refreshing...",
  pullUpLoadMore: "Pull up to load more data",
  loading: "Loading ...",
  allLoaded: "All loaded"
};
