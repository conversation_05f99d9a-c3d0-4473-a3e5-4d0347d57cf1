export default {
  fundTypeTabs: [
    {
      name: 'all',
      label: 'All',
    },
    {
      name: 'FIXED_INTEREST',
      label: 'Bond',
    },
    {
      name: 'EQUITY',
      label: 'Equity',
    },
    {
      name: 'MIXED_ASSET',
      label: 'Balanced',
    },
    {
      name: 'MONEY_MARKET',
      label: 'MMF',
    },
  ],
  correlationCompany: 'No fund company available',
  fundCompany: 'Fund company',
  correlationFund: 'No related fund available',
  username: 'Name',
  tenure: 'Tenure',
  tenureReportBack: 'Tenure Return',
  sofar: 'Until now',
  day: 'Days',
  type: 'Type',
  territory: 'Location',
  distribute: 'Distribution',
  investment: 'Investment',
  fundRiskLevel: {
    HIGH: 'High risk',
    MID_HIGH: 'Medium to high risk',
    MIDDLE: 'Medium risk',
    MID_LOW: 'Medium to low risk',
    LOW: 'Low risk',
  },
  bondType: {
    EQUITY: 'Equity type',
    FIXED_INTEREST: 'Bond type',
    MIXED_ASSET: 'Mixed type',
    MONEY_MARKET: 'Monetary type',
  },
  currencyType: {
    CNY: 'CNY',
    HKD: 'HKD',
    USD: 'USD',
   },
  fundSearchTabs: [
    { name: 'LAST_MONTH', label: '1Y' },
    { name: 2, label: 'Earning yield', type: true },
    { name: 3, label: 'Latest NAV', type: true },
    { name: 4, label: 'Filter more' },
  ],
  timeTile: 'Since Inception',
  timeTileView: 'Since Inception',
  fundSearchTime: [
    { key: 'LAST_MONTH', label: '1M Performance' },
    { key: 'THREE_MONTHS', label: '3M Performance' },
    { key: 'SIXTH_MONTHS', label: '6M Performance' },
    { key: 'LAST_YEAR', label: '1Y Performance' },
    { key: 'THREE_YEARS', label: '3Y Performance' },
    { key: 'SINCE_LAUNCH', label: 'Since Inception' },
  ],
  fundSearchTimeCanvas: [
    { key: 'LAST_MONTH', label: '1M' },
    { key: 'THREE_MONTHS', label: '3M' },
    { key: 'SIXTH_MONTHS', label: '6M' },
    { key: 'LAST_YEAR', label: '1Y' },
    { key: 'THREE_YEARS', label: '3Y' },
    { key: 'SINCE_LAUNCH', label: 'Since Inception' },
  ],
  fundBtnText: 'Subscribe now',
  fundBtnTextSubscribe: 'Subscribe',
  discounts: 'Discounts',
  fundName: 'Name of Fund',
  fundAssetSize: 'Fund size',
  fundEstablishTime: 'Fund establish date',
  fundStockType: 'Share class',
  fundBondType: 'Asset class',
  fundEstablishPlace: 'Fund domicile',
  fundIntroduce: {
    title: 'Fund distribution services are provided by Eddid Securities and Futures Limited',
    content: 'This page is not a legal document, please read the Fund Subscription Agreement and Prospectus before investment.',
    content1: 'Past performance is not indicative of future performance, and discreet investment is suggested for the risk of the stock market.',
  },
  fundIntroduceInter: {
    title: "Eddid Securities and Futures Limited is the fund's authorized distributor",
    content: "Although this fund has been authorized by the Securities Regulatory Commission of Hong Kong, such approval does not imply official recommendation, nor does it imply recommendation or certification of the fund, nor does it guarantee the commercial benefits or performance of the fund. The past performance of the fund does not indicate its future performance. There are risks in the market, and investment needs to be cautious.",
  },
  annualYield: 'Seven Day Annualized Yeild',
  cumulative1Y: '% change in 1Y',
  amountText: '%Change',
  expressive: 'Performance',
  netAssetValue: 'Latest NAV',
  earnings: '10k of earning',
  interTypeTabs: [
    // { name: 1, label: 'Seven Day Annualized Yeild' },
    { name: 1, label: 'Performance trends' },
    { name: 2, label: 'Return' },
    { name: 3, label: '10k of earning' },
  ],
  typeTabs: [
    // { name: 1, label: 'Seven Day Annualized Yeild' },
    { name: 1, label: 'Perfomance forecast' },
    { name: 2, label: 'Historical performance' },
    { name: 3, label: '10k of earning' },
  ],
  typeTabs1: [
    { name: 1, label: 'Perfomance forecast' },
    { name: 2, label: 'Historical performance' },
    { name: 3, label: 'Net value of history' },
  ],
  chartTabs: [
    { name: 1, label: '1M' },
    { name: 2, label: '3M' },
    { name: 3, label: '6M' },
    { name: 4, label: '1Y' },
    { name: 5, label: '3Y' },
    { name: 6, label: 'Since Inception' },
  ],
  thisFund: 'This Fund',
  dateTime: {
    cumulative1Week: '1W',
    cumulative1M: '1M',
    cumulative3M: '3M',
    cumulative6M: '6M',
    cumulative1Y: '1Y',
    cumulative3Y: '3Y',
    cumulative5Y: '5Y',
    cumulativeMonthly: 'MTD',
    cumulativeQuarterly: 'QTD',
    cumulativeYearly: 'YTD',
    cumulativeSinceLaunch: 'Since Inception',
  },
  timeArea: 'Time zone',
  upDown: '%Change',
  timeText: 'Date',
  netValue: 'NAV',
  cumulative1Day: 'Day %Change',
  moreMessage: 'More data',
  notMessage: 'No data',
  updateMoreMessage: 'All loaded',
  fundPulic: 'Fund position',
  checkDetails: 'View details',
  investDistribute: 'Investment distribution',
  propertyStructure: 'Property structure',
  industryDistribute: 'Distribution by industry',
  updateTime: 'Update time',
  entrepotNumber: 'Top 10 Stock positions',
  entrepotNumberRatio: 'Top 10 Stock positions %',
  checkText: 'Expand view more',
  checkClose: 'close',
  chartTypeTabs: [
    { name: 1, label: 'Investment distribution' },
    { name: 2, label: 'Top 10 positions' },
  ],
  dealRule: 'Trading rules',
  dealFlow: 'Trading process, operation fee',
  addOptional: 'Add Watchlist',
  addOptionalOver: 'Added to watchlist',
  deleteOptional: 'Added to watchlist',
  subscribe: 'Subscribe',
  submitText: 'Submit',
  ladderCharge: 'Ladder charge',
  ladderChargeLabel: {
    title: 'For ladder charge, the charge standard is as follows, with an example of subscription fee:',
    min: 'Amount ≤ HKD 3,499,999 0.30%',
    median: 'HKD 3,499,999＜Amount ≤ HKD 14,999,999 0.20%',
    max: 'Amount＞HKD 14,999,999 0.10%',
    eg: "Assume that the client's subscription amount is HKD 20,000,000, and the subscription fee is: 0.30% × HKD 3,499,999 + 0.20% × HKD 11,500,000 + 0.10% × HKD 5,000,001 = HKD 38,500",
  },
  ladderChargeLabelArr: [
    'For ladder charge, the charge standard is as follows, with an example of subscription fee:',
    'Amount≤HKD 3,499,999: 0.30%',
    'HKD 3,499,999＜Amount≤HKD 14,999,999: 0.20%',
    'Amount＞HKD 14,999,999: 0.10%',
    "Assume that the client's subscription amount is HKD 20,000,000, and the subscription fee is:",
    '0.30% × HKD 3,499,999 + 0.20% × HKD 11,500,000 + 0.10% × HKD 5,000,001 = HKD 38,500',
  ],
  affirmShare: 'Confirmation of shares',
  earningsArrive: 'Return posted',
  fundRecord: 'Fund Profile',
  fundManager: 'Fund manager',
  prospectusUrlCn: 'Monthly report',
  fundStatement: 'Prospectus',
  semiAnnualManagerReportUrl: 'Semi-annual report',
  annualManagerReportUrlEn: 'Annual report',
  keyFactStatementUrlCn: 'Product Information Overview',
  redemption: 'Redemption',
  rate: 'Rate',
  subscriptionRate: 'Subscription fee',
  subscriptionRateInter: 'Subscription fee',
  redemptionRate: 'Redemption fee',
  platformFeeRate: 'Platform fee',
  managementFeeRate: 'Management fee',
  fundRecordType: 'Overview, Announcements, Dividends, Documents',
  dealRuleTabs: [
    { name: 1, label: 'Subscription rules' },
    { name: 2, label: 'Redemption rules' },
    { name: 3, label: 'Fee description' },
  ],
  ruleData: [
    { label: 'Initial investment amount', value: '8,000.00HKD' },
    { label: 'Subscription fee', value: 0.05, oldValue: 3.0 },
    { label: 'Confirmed', value: 'T+4' },
  ],
  redeemData: [
    { label: 'Minimum Redemption Share', value: 0.0001 },
    { label: 'Redemption fee', value: 0.0, oldValue: 3.0 },
    { label: 'Amount Confirmation Date', value: 'T+4' },
    { label: 'Amount posted', value: 'T+4~14' },
  ],
  startCast: 'Initial investment amount',
  unequivocalTime: 'Confirmed',
  flow: 'Process',
  subscribeDescribe: {
    content:'Day T: Refers to the fund trading day; cut-off time is {time}; subscription before {time} will be considered as the order on T day, and after {time} will be considered as the order on T+1 day; weekends and holidays are not fund trading days. (All above times are Hong Kong time.)',
    content1:'Orders cancellation : You can cancel your orders submitted before {timeEnd} on T day.',
  },
  fundReast: 'Fund closed date',
  expenseName: 'Fee name',
  expenseRemark:
    'Remarks: Charges are subject to change from time to time without prior notice; the above information does not represent the full cost of the fund. For details of other charges of the fund, please refer to the fund documentation. Please contact the customer service for details.',
  interFundRecordTabs: [
    { name: 1, label: 'Fund Overview' },
    { name: 2, label: 'Fund documents' }
  ],
  fundRecordTabs: [
    { name: 1, label: 'Overview' },
    { name: 2, label: 'Documents' },
  ],
  invest: {
    idea: 'Investment idea',
    ideaContent: '',
    strategy: 'Investment strategy',
    strategyContent: '',
    fundObjective: 'Fund Objective'
  },
  affirmBtn: 'Confirm',
  loseText: 'Failed',
  partNumber: 'Share',
  orderText: 'Order',
  capitalReceive: 'Funds receive',
  estimateText: 'Estimate',
  accountDesc: 'to the securities account to stock trading, IPO',
  loseCause: 'A network error has occurred. Please refresh the network and try again',
  aHundredMillion: 'B',
  TDayBeforeTen: 'By day T {time}',
  TDay: 'at the NAV of day T',
  tradingDay: 'Trading day',
  shareTitle: 'Eddid Fund',
  other: 'other',
  more: 'More',
  packUp: 'Pack up',
  subordinateFund: 'Subordinate Fund',
  fees: "All the fees below are obtained by our Company from product issuers or product platforms, which clients do not need to pay extra fees."
};
