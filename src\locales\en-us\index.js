// 递归获取当前文件夹下的所有.json文件
const lang = {};
const files = require.context('./', true, /\.json$|\.js$/);
files.keys().forEach((key) => {
  if(key === './index.js'){
    return
  }
  let name = '';
  const splitNameArr = key.replace(/(\.\/|\.json)|(\.\/|\.js)/g, '').split('-');
  splitNameArr.forEach((item, index) => {
    if (index !== 0) item = item.charAt(0).toUpperCase() + item.slice(1);
    name += item;
  });
  if((/\.json$/).test(key)){
    lang[name] = files(key)
  }else{
    lang[name] = files(key).default;
  }
  
});
// console.log('lang>>>>>', lang);

export default lang;
