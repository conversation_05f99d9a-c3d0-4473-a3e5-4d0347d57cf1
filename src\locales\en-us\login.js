export default {
  register: 'Register',
  loginTitle: 'Mobile Phone Number Verification Code Login',
  loginBtnLabel: 'Login',
  phoneCodeOptions: {
    86: 'Chinese mainland +86',
    852: 'Hong Kong, China +852',
    886: 'Taiwan, China +886',
    853: 'Macao, China  +853',
  },
  phone: 'Phone number',
  vcode: 'Verification code',
  getVerCode: 'Get verification code',
  password: 'Password',
  repassword: 'Confirm password',
  hadAccount: 'Already have an account,',
  regsiterSuccess: 'Registration completed',
  accountExist: 'Account already exists',
  loginNow: 'Login now',
  chooseAreaCode: 'Select area code',
  tips: {
    passwordRule: 'Set password, 6-16 digits, at least two types of numbers/letters/symbols',
    repassword: 'Please enter password again',
    regsiterSuccess: 'Congratulations on your successful registration, please login now to continue to complete the account',
    accountExist: 'Your account already exists, please login first to continue to complete the account opening',
    phoneFormatError: 'Please enter the correct mobile phone number',
    registering: 'Registration in progress',
  },
};
