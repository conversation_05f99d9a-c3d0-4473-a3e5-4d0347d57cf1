export default {
  allTheRedemption: 'Current share only supports full redemption',
  sevenDescribeRow1: 'The seven-day annualized rate of return is',
  sevenDescribeRow2: 'Money funds past seven days per 10,000 fund shares',
  sevenDescribeRow3: 'Net income discounted to an annualized rate of return.',
  sevenDescribeRow4: 'Formula:',
  sevenDescribeRow5: 'Annualized Prospective Yield (%) of past 7-day =',
  sevenDescribeRow6: '(NAV on day T8- NAV on day T1)/ NAV on day T1 /',
  sevenDescribeRow7: 'number of natural days x365 x100%',
  thousandDescribeRow1: 'The invested 10k return refers to',
  thousandDescribeRow2: 'if 10k HKD/USD amount is held, the income in the',
  thousandDescribeRow3: "of the corresponding currency's earnings.",
  thousandDescribeRow4: 'Formula:',
  thousandDescribeRow5: 'Invested 10k return = 10,000 / NAV on day T-1 *',
  thousandDescribeRow6: '(NAV on day T - NAV on day T-1)',
  SevenDayAnnualization: [
    { value: 'The 7-day annualized yield is', id: 1 },
    { value: 'Monetary fund per 10,000 fund shares in the past seven days', id: 2 },
    { value: 'The annual rate of return converted from net income.', id: 3 },
    { value: 'Calculation formula:', id: 4 },
    { value: 'Annualized for the last seven days (%) = (T8 days net worth -T1 days net worth) /T1 days net worth/days x365x100%', id: 5 },
    // { value: '值-T1日单位净值）/T1日单位净值/', id: 6 },
    // { value: '自然日天数 x365 x100%', id: 7 }
  ],
  TenThousandYuanRevenue: [
    { value: 'The income of 10,000 yuan refers to the income of the corresponding currency that can be obtained on the day if the fund is purchased and held at 10,000 HKD or US dollars.', id: 1 },
    // { value: '1万元港币或美元的基金，当日可获得', id: 2 },
    // { value: '的对应币种的收益。', id: 3 },
    { value: 'Calculation formula:', id: 2 },
    { value: 'Revenue of ten thousand Yuan = 10,000 / T-day net worth * (T-day net worth -T-1 day net worth)', id: 3 },
    // { value: '日净值-T-1日净值）', id: 6 }
  ],
  apply: 'Subscribe',
  HKD: 'HKD',
  paymentAccount: 'Paying account',
  paymentAccountContent: 'Fund account',
  productName: 'Product name',
  productNameContent: 'E Fund (HK) Hong Kong Dollar Money Market C Acc HKD',
  yieldInRecentYear: 'Yield in 1 YR',
  yieldInRecentYearText: 'Performance in the past 1 year',
  yieldInRecentYearNum: '+6.25%',
  redeem: 'Redeem',
  redeemTo: 'Redeem to',
  subscriptionRules: 'Subscription rules',
  redemptionRules: 'Redemption rules',
  subscriptionAmount: 'Purchase amount',
  redemptionShare: 'Redeem share',
  buyPlaceholder: 'Minimum',
  additional: 'Min additional',
  sellPlaceholder: 'Min redemption',
  allBuy: 'Full buy',
  allSell: 'Full redeem',
  redemptionQuantity: 'Maximum redeemable:',
  HKDollar: 'HKD',
  dollar: 'USD',
  yuan: 'CNY',
  share: 'share',
  shareSingle: '(share)',
  promote: 'Upgrade',
  inputPlaceHolder: '',
  purchaseRates: 'Subscription fee',
  redemptionRate: 'Redemption fee',
  platformFeeRate: 'Platform fee',
  ConfirmShare: "Confirm share",
  ExpectedBefore: "Expected before{time}",
  ConfirmedWithNAV: "（Confirmed with{time}NAV）",
  StartViewingEarning: "Start viewing earning",
  FundPosted: "Fund posted",
  expectedT: 'Expected T+',
  expected: 'Expected',
  redemptionBouncedExpectedT: 'Fund expected T+',
  redemptionBouncedExpectedT1: 'Fund expected',
  redemptionBouncedExpectedTAfter: 'posted',
  redemptionBouncedExpectedTAfter1: 'posted',
  subscriptionDescription: 'confirm share, and start calculating the earnings',
  subscriptionDescription1: 'confirm share, and start calculating the earnings',
  subscriptionDescriptionbuy: 'confirm share (subject to',
  subscriptionDescriptionbuy1: 'confirm share (subject to',
  orderDetailBuy: 'confirm share',
  redemptionDescriptionlabel: 'at T+',
  redemptionDescriptionlabel1: 'at',
  redemptionDescriptionlabelAfter: 'NAV, expected T+',
  redemptionDescriptionlabelAfter1: 'NAV, expected',
  redemptionDescriptionlabelEnd: 'fund posted',
  redemptionDescriptionlabelEnd1: 'fund posted',
  redemptionConfirmationDescriptionRight: 'NAV)',
  passwordPrompt: 'Please enter the 6-15 digit trading password',
  protocolDescription: 'I have read and agree to',
  agreement: 'Notice for Fund Investment Client Before Buying and Selling',
  popUpTitle: 'Please enter trading password',
  capitalFlow: 'Capital flow',
  financialAccount: 'Fund account',
  financialBusiness: 'Funds',
  allDates: 'All dates',
  incomeDetails: 'Income details',
  cumulativeIncome: 'Cumulative income (HKD)',
  nonTradingDay: '(Non trading day)',
  orderRecord: 'Order History',
  orderSubmitted: 'Orders submitted',
  allOrders: 'All Orders',
  state: 'Status',
  name: 'Name',
  amountOfMoney: 'Amount/Share',
  orderTime: 'Order time',
  risklevel: 'Risk level',
  evaluationResults: 'Assessment results',
  balancedType: 'Balanced type',
  balancedTypeBalanced: 'Conservative type',
  description: 'Ability to accept investments with moderate risk and price volatility in pursuit of asset appreciation and current income',
  descriptionBalanced: 'I want absolute safety of my principal and can only accept lower risk and price fluctuations for a smaller return on my investment',
  category: 'Category',
  describe: 'Description',
  ordinaryInvestors: 'General Investors',
  mediumRisk: '3 (medium risk)',
  investmentObjectives: 'Investment objective',
  balance: 'Balanced',
  balanceBuy: 'Cash avalible:',
  investmentPeriod: 'Investment period',
  withinTwoYears: 'Within 2 years',
  declarant: 'Reported net asset',
  investorType: 'Investor type',
  lessThan: 'Less than HKD500,000',
  actualHandlingCharge: 'Actual commission fee',
  derivativeKnowledge: 'Derivative knowledge',
  reEvaluation: 'Re-assess',
  transaction: 'Fund account',
  securitiesAccount: 'Securities account',
  futuresAccount: 'Futures account',
  totalAssets: 'Net asset',
  myTotalAssets: 'Total asset',
  profitAndLoss: 'Floating profit and loss',
  IPOCenter: 'IPO',
  depositFunds: 'Deposit',
  internalTransfer: 'Internal transfer',
  currencyExchange: 'Currency exchange',
  depositAccountNumber: 'Securities accoun (margin)',
  updateTime: 'Update time',
  yesterdayEarnings: "Yesterday's earnings",
  cashBalance: 'Surplus funds',
  fundsInTransit: 'Cash in transit',
  positionFund: 'Position fund',
  accountCumulativeIncome: 'Cumulative income',
  fundPosition: 'Fund position',
  money: 'Amount',
  section: 'Section',
  ladderChargeTableTitle: 'Ladder charge rules',
  amountAndYesterdayEarnings: "Amount/ yesterday's earnings",
  positionIncome: 'Position earnings',
  explainThePrompt: 'Please enter purchase amount',
  redemptionPrompt: 'Please enter redeem share',
  Buy: 'Purchase amount',
  BuyOrder: 'Fund subscribe',
  shareOutBonus: 'Dividend to',
  USD: 'USD',
  Sell: 'Redeem share',
  SellOrder: 'Fund redeem',
  CANCELLED: 'Cancelled',
  CANCELL: 'Cancel',
  Dividend: 'Dividend amount',
  DividendOrder: 'Fund dividend',
  ALLOCATED: 'Assigned',
  cancell: 'Cancel',
  CONFIRMED: 'Confirmed',
  REGISTERED: 'Registered',
  confim: 'Confirm',
  FAILED: 'Failed to deduct the funds',
  PROCESSING: 'In process',
  submit: 'Submit',
  submitFund: 'Submit',
  SUBMITTED: 'Submitted',
  nearlyAWeek: '1W',
  nearlyAMonth: '1M',
  nearlyAYear: '1Y',
  today: 'Today',
  abnormalAmounts: 'Insufficient available cash',
  shareAbnormal: 'Insufficient available position share',
  shareTooBig: 'The redemption share cannot exceed the maximum redeemable share',
  minimumRedeem: 'The redemption share cannot be less than the minimum redeemable share',
  afterTheRedemptionShareIsSmall: 'The share cannot be less than the minimum position share after redemption',
  submittedSuccessfully: 'Submitted successfully',
  youSubmit: 'Submission failed',
  checkYield: 'Start viewing today earning',
  complete: 'Finished',
  return: 'Return',
  noPosition: 'No position',
  optional: 'Watchlists',
  fundShare: 'Share',
  noPsition: 'Insufficient position',
  focus: 'Added to Watchlists successfully',
  nofocus: 'Deleted from Watchlists',
  itsSuccess: 'Order withdrawn successfully',
  regionIsNotSupported: 'Your region does not support subscription',
  subscriptionIsNotSupported: 'The current fund does not support subscription',
  redemptionIsNotSupported: 'The current fund does not support redemption',
  lowOnCash: 'Insufficient available cash',
  resetPassword: 'Confirm that you need to reset your password?',
  forgotPassword: 'Forgot password',
  passwordMistake: 'Incorrect password',
  determine: 'Confirm',
  loadIsComplete: 'All loaded',
  loadMore: 'Load More',
  orderInProcess: 'Orders are in process and cannot be cancelled!',
  redemptionSubmission: 'Redemption submission',
  confirmTheAmount: 'Confirm',
  redemptionToAccount: 'Redemption to account',
  abnormalAccount: 'Abnormal account. Please call +852-2655 0338 for more information',
  riskDescription1: 'The risk level, investment objective or investment',
  riskDescription2: 'The number of years of capital and your risk tolerance do not match',
  riskDescription3: 'tolerance, so you cannot buy it',
  riskDescriptionComplexProduct1: 'The product is a complex product and the investor buys the',
  riskDescriptionComplexProduct2: 'Please evaluate the risk of the product carefully beforehand. Product History',
  riskDescriptionComplexProduct3: 'Results are for reference only and are not indicative of future trends',
  riskDescriptionComplexProduct4: 'The forecast.',
  goOn: 'Continue',
  specialtyInvestor: 'This fund is only suitable for professional investors, please contact customer service for details',
  specialtyInvestor1: 'Only professional investors can apply, please contact customer service for details',
  noUseDocument: 'No available documents',
  complexProductAgreement: "I've read and agreed to",
  pleaseChoose: 'Please read and check the service agreement',
  EDOneName: 'Eddid ONE',
  EDOneIntroduction: 'Professional one-stop investment and financial steward',
  OpenApp: 'Open the App',
  Tips: 'Fund subscription currency and fund product currency should be the same',
  startTime: 'Start time',
  endTime: 'End time',
  all: 'All',
  lastMonth: '1M',
  lastThreeMonth: '3M',
  nearlyYear: '1Y',
  pleaseSelectStartTimeAndEndTime: 'Please select start time and end time',
  endTimeCannotBeLessThanStartTime: 'End time cannot be less than start time',
  agree: 'Agree',
  disagree: 'Disagree',
  copies: 'share',
  noMoreData: 'No more data',
  know: 'I understand',
  intoFlow: 'Inflow',
  outFlow: 'Outflow',
  mismatch: 'Mismatch',
  matched: 'Successfully adapted'
};
