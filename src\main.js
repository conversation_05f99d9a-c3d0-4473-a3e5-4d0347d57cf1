import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import '@/utils/responsive';
import '@/utils/vee-validate.js';
import i18n from '@/utils/i18n';
import '@/utils/bootstrap';
import 'mand-mobile/lib/mand-mobile.css';
import { jsBridge, setNavBar } from './utils/jsBridge';
import eventBus from '@/utils/eventBus';
import Loading from '@/plugin/loading';
import '@/utils/sensors';
import '@/utils/h5-share';
import { themeDATA } from './assets/theme';
import moment from 'moment';
Vue.prototype.$moment = moment;
moment.locale('zh-cn');

(function() {

  if (!sessionStorage.length) {
      // 这个调用能触发目标事件，从而达到共享数据的目的
      localStorage.setItem('getSessionStorage', Date.now());
  }

  // 该事件是核心
  window.addEventListener('storage', function(event) {
      if (event.key == 'getSessionStorage') {
          // 已存在的标签页会收到这个事件
          localStorage.setItem('sessionStorage', JSON.stringify(sessionStorage));
          localStorage.removeItem('sessionStorage');

      } else if (event.key == 'sessionStorage' && !sessionStorage.length) {
          // 新开启的标签页会收到这个事件
          var data = JSON.parse(event.newValue),
                  value;

          for (let key in data) {
              sessionStorage.setItem(key, data[key]);
          }
      }
  });
})();
const source = jsBridge.isSupported('getAppInfo') ? 'app' : 'pc';

if (source) {
  store.commit('setSource', source);
}

Vue.prototype.$bus = eventBus;
Vue.prototype.$jsBridge = jsBridge;
Vue.prototype.$setNavBar = setNavBar;
Vue.prototype.$themeDATA = themeDATA;

Vue.use(Loading);

Vue.config.productionTip = false;
if (process.env.NODE_ENV && process.env.NODE_ENV !== "production" && process.env.NODE_ENV !== "uat") {
  const Vconsole = require("vconsole");
  // eslint-disable-next-line no-unused-vars
  new Vconsole();
}

new Vue({
  router,
  store,
  i18n,
  // init localstorage, vuex, Logo message
  render: h => h(App)
}).$mount('#app')
