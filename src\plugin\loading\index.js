import LoadingComponent from '@/components/loading';

const Loading = {
  install (Vue, options) {
    const LoadingConstructor = Vue.extend(LoadingComponent);
    const loadingInstance = new LoadingConstructor();
    loadingInstance.$mount(document.createElement('div'));
    document.body.appendChild(loadingInstance.$el);

    Vue.prototype.$loading = {
      _queue: 0,
      show () {
        if (!this._queue) {
          loadingInstance.isshow = true;
        }
        this._queue++;
      },
      hide () {
        this._queue--;
        if (!this._queue) {
          loadingInstance.isshow = false;
        }
      },
      // 重置。应用场景：在请求过程中用户触发了后退
      reset () {
        this._queue = 0;
        loadingInstance.isshow = false;
      },
    };
  }
};

export default Loading;
