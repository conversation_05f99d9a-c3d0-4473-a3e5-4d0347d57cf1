import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import zhCN from 'vee-validate/dist/locale/zh_CN'; // 引入本地化语言
import VueI18n from 'vue-i18n';
import zhhansLocale from '@/locales/zh-hans/index';
import zhhantLocale from '@/locales/zh-hant/index';
import enUSLocale from '@/locales/en-us/index';
import { urlParse } from '@/utils/util';
Vue.use(VueI18n);

const lang = urlParse(window.location.search).lang || 'zh-hans'
const theme = urlParse(window.location.search).theme || 'light';
store.commit('setLang', lang);
store.commit('setTheme', theme);

// 覆盖默认错误信息提示
zhCN.messages.required = (field) => `${field}`;

const i18n = new VueI18n({
  // locale: sessionStorage.getItem('lang') || store.state.locale || 'zh-hans',
  locale: lang,
  messages: {
    "zh-hans": zhhansLocale,
    "zh-hant": zhhantLocale,
    "en": enUSLocale
  }
})

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Home',
    meta: {
      title: i18n.t('router.home')
    },
    component: () => import(/* webpackChunkName: "home" */ '@/views/Home.vue')
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: i18n.t('router.login')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/login.vue')
  },
  {
    path: '/risk',
    name: 'risk',
    meta: {
      title: i18n.t('router.risk')
    },
    component: () => import(/* webpackChunkName: "risk" */ '@/views/risk/index.vue'),
    redirect: '/risk/tips',
    children: [
      {
        path: '/risk/tips',
        name: 'riskTips',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "riskTips" */ '@/views/risk/riskTips.vue')
      },

      {
        path: '/risk/assessment',
        name: 'riskAssessment',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "riskAssessment" */ '@/views/risk/riskAssessment.vue')
      },
      {
        path: '/risk/questions/:id',
        name: 'riskQuestions',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "riskQuestions" */ '@/views/risk/riskQuestions.vue')
      },
      {
        path: '/risk/disclosure',
        name: 'riskDisclosure',
        meta: {
          title: i18n.t('router.riskDisclosure')
        },
        component: () => import(/* webpackChunkName: "riskDisclosure" */ '@/views/risk/riskDisclosure.vue')
      },
      {
        path: '/risk/result',
        name: 'riskResult',
        meta: {
          title: i18n.t('router.riskResult')
        },
        component: () => import(/* webpackChunkName: "riskResult" */ '@/views/risk/riskResult.vue')
      },
      {
        path: '/risk/level',
        name: 'riskLevel',
        meta: {
          title: i18n.t('router.riskLevel')
        },
        component: () => import(/* webpackChunkName: "riskLevel" */ '@/views/risk/riskLevel.vue')
      },
    ]
  },
  {
    path: '/result',
    name: 'result',
    meta: {
      title: i18n.t('router.result')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/result/index.vue')
  },
  { // 公司列表
    path: '/fundCompany',
    name: 'fundCompany',
    meta: {
      title:  i18n.t('router.fundCompany')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/fund-company/index.vue')
  },
  { // 公司数据
    path: '/fundCompanyDetails',
    name: 'fundCompanyDetails',
    meta: {
      title:  i18n.t('router.fundCompanyDetails')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/fund-company/company-details.vue')
  },
  { // 基金排行----产品类型
    path: '/fundKind',
    name: 'fundKind',
    meta: {
      title: i18n.t('router.fundKind')
    },
    component: () => import(/* webpackChunkName: "fundKind" */ '@/views/fund-kind/index.vue')
  },
  {
    path: '/historyNetworth',
    name: 'historyNetworth',
    meta: {
      title: i18n.t('router.historyNetworth')
    },
    component: () => import(/* webpackChunkName: "historyNetworth" */ '@/views/product-details/history-networth.vue')
  },
  { // 产品详情
    path: '/productDetails',
    name: 'productDetails',
    meta: {
      title: i18n.t('router.productDetails')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/product-details/index.vue'),
    children: [

    ]
  },
  { // 基金账户
    path: '/fundAccount',
    name: 'fundAccount',
    meta: {
      title: i18n.t('router.fundAccount')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/fund-account/index.vue'),
    children: [

    ]
  },
  {  //  订单记录
    path: '/orderRecord',
    name: 'orderRecord',
    meta: {
      title: i18n.t('router.orderRecord')
    },
    component: () => import(/* webpackChunkName: "result" */ '@/views/fund-account/order-record.vue')
  },
  {  //  订单详情
    path: '/orderDetail',
    name: 'orderDetail',
    meta: {
      title: i18n.t('router.orderDetail')
    },
    component: () => import(/* webpackChunkName: "result" */ '@/views/fund-account/order-detail.vue')
  },
  // suitability-matching-record
  // 合适性匹配记录
  {
    path: '/suitabilityMatchingRecord',
    name: 'suitabilityMatchingRecord',
    meta: {
      title: i18n.t('router.suitabilityMatchingRecord')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/fund-kind/suitability-matching-record.vue')
  },
  {  // 收益明细
    path: '/incomeDetails',
    name: 'incomeDetails',
    meta: {
      title: i18n.t('router.incomeDetails')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/fund-account/income-details.vue')
  },
  // 资金流水
  {
    path: '/capitalFlow',
    name: 'capitalFlow',
    meta: {
      title: i18n.t('router.capitalFlow')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/fund-account/capital-flow.vue')
  },
  { // 申购
    path: '/transactionBuy',
    name: 'transactionBuy',
    meta: {
      title: i18n.t('router.transactionBuy')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/fund-transaction/transaction-buy.vue')
  },
  { // 赎回
    path: '/transactionSell',
    name: 'transactionSell',
    meta: {
      title: i18n.t('router.transactionSell')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/fund-transaction/transaction-sell.vue')
  },
  { // 申购-赎回结果提示
    path: '/subscribeRedeem',
    name: 'subscribeRedeem',
    meta: {
      title: i18n.t('router.subscribeRedeem')
    },
    component: () => import ('@/views/fund-transaction/subscribe-redeem.vue')
  },
  { // 基金持仓
    path: '/fundPossess',
    name: 'fundPossess',
    meta: {
      title: i18n.t('router.fundPossess')
    },
    component: () => import('@/views/product-details/fund-possess.vue')
  },
  { // 万元收益
    path: '/fundEarnings',
    name: 'fundEarnings',
    meta: {
      title: i18n.t('router.fundEarnings')
    },
    component: () => import('@/views/product-details/fund-earnings.vue')
  },
  { // 历史数据
    path: '/historyPerformance',
    name: 'historyPerformance',
    meta: {
      title: i18n.t('router.historyPerformance')
    },
    component: () => import('@/views/product-details/history-performance.vue')
  },
  { // 交易规则
    path: '/dealRule',
    name: 'dealRule',
    meta: {
      title: i18n.t('router.dealRule')
    },
    component: () => import('@/views/product-details/deal-rule.vue')
  },
  { // 基金档案
    path: '/fundRecord',
    name: 'fundRecord',
    meta: {
      title: i18n.t('router.fundRecord')
    },
    component: () => import('@/views/product-details/fund-record.vue')
  },
  {
    path: '/privateEquityFund',
    name: 'privateEquityFund',
    meta: {
      title: i18n.t('router.privateEquityFund')
    },
    component: () => import('@/views/privateEquityFund/index.vue')
  },
  {
    path: '/privateEquityFundDetails',
    name: 'privateEquityFundDetails',
    meta: {
      title: i18n.t('router.privateEquityFund')
    },
    component: () => import('@/views/privateEquityFund/privateEquityFundDetails.vue')
  },
  // { // 基金销售协议
  //   path: '/salesAgreement',
  //   name: 'salesAgreement',
  //   meta: {
  //     title: i18n.t('router.salesAgreement')
  //   },
  //   component: () => import('@/views/fund-transaction/sales-agreement.vue')
  // },
  {
    path: '/agreepage',
    name: 'agreePage',
    meta: {},
    component: () => import('@/views/AgreePage.vue')
  },

  // ------------------------------------新增国籍版路由----------------------------------------
  {
    path: '/international',
    name: 'international',
    meta: {
      title: i18n.t('router.home')
    },
    component: () => import(/* webpackChunkName: "home" */ '@/views/international/Home.vue')
  },
  { // 公司列表
    path: '/international/fundCompany',
    name: 'interFundCompany',
    meta: {
      title:  i18n.t('router.fundCompany')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-company/index.vue')
  },
  { // 公司列表
    path: '/international/fundCompanyDetails',
    name: 'interFundCompanyDetails',
    meta: {
      title:  i18n.t('router.fundCompany')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-company/company-details.vue')
  },
  { // 基金排行
    path: '/international/fundKind',
    name: 'interFundKind',
    meta: {
      title: i18n.t('router.fundKind')
    },
    component: () => import(/* webpackChunkName: "fundKind" */ '@/views/international/fund-kind/index.vue')
  },
  // 基金账户
  {
    path: '/international/fundAccount',
    name: 'interFundAccount',
    meta: {
      title: i18n.t('router.fundAccount')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-account/index.vue'),
    children: [

    ]
  },
  // 收益明细
  {
    path: '/international/incomeDetails',
    name: 'interIncomeDetails',
    meta: {
      title: i18n.t('router.incomeDetails')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/international/fund-account/income-details.vue')
  },
  // 资金流水
  {
    path: '/international/capitalFlow',
    name: 'interCapitalFlow',
    meta: {
      title: i18n.t('router.capitalFlow')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/international/fund-account/capital-flow.vue')
  },
  //  订单记录
  {
    path: '/international/orderRecord',
    name: 'interOrderRecord',
    meta: {
      title: i18n.t('router.orderRecord')
    },
    component: () => import(/* webpackChunkName: "result" */ '@/views/international/fund-account/order-record.vue')
  },
  {
    path: '/international/orderDetail',
    name: 'interOrderDetail',
    meta: {
      title: i18n.t('router.orderRecord')
    },
    component: () => import(/* webpackChunkName: "result" */ '@/views/international/fund-account/order-detail.vue')
  },
  { // 产品详情
    path: "/international/productDetails",
    name: "interProductDetails",
    meta: {
      title: i18n.t('router.productDetails'),
    },
    component: () => import(/* webpackChunkName: "productDetails" */ '@/views/international/product-details/index.vue')
  },
  { // 历史数据
    path: '/international/historyPerformance',
    name: 'interHistoryPerformance',
    meta: {
      title: i18n.t('router.historyPerformance')
    },
    component: () => import('@/views/international/product-details/history-performance.vue')
  },
  // 历史净值
  {
    path: '/international/historyNetworth',
    name: 'interFistoryNetworth',
    meta: {
      title: i18n.t('router.historyNetworth')
    },
    component: () => import(/* webpackChunkName: "historyNetworth" */ '@/views/international/product-details/history-networth.vue')
  },
  // 万元收益
  {
    path: '/international/fundEarnings',
    name: 'interFundEarnings',
    meta: {
      title: i18n.t('router.fundEarnings')
    },
    component: () => import('@/views/international/product-details/fund-earnings.vue')
  },
  { // 基金持仓
    path: '/international/fundPossess',
    name: 'interFundPossess',
    meta: {
      title: i18n.t('router.fundPossess')
    },
    component: () => import('@/views/international/product-details/fund-possess.vue')
  },
  { // 交易规则
    path: '/international/dealRule',
    name: 'interDealRule',
    meta: {
      title: i18n.t('router.dealRule')
    },
    component: () => import('@/views/international/product-details/deal-rule.vue')
  },
  { // 基金档案
    path: '/international/fundRecord',
    name: 'interFundRecord',
    meta: {
      title: i18n.t('router.fundRecord')
    },
    component: () => import('@/views/international/product-details/fund-record.vue')
  },
  { // 基金风险评估
    path: '/international/riskLevelInfo',
    name: 'riskLevelInfo',
    meta: {
      title: i18n.t('router.riskLevelInfo')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/product-details/riskLevelInfo.vue'),
  },
  { // 赎回
    path: '/international/transactionSell',
    name: 'interTransactionSell',
    meta: {
      title: i18n.t('router.transactionSell')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-transaction/transaction-sell.vue')
  },
  { // 申购-赎回结果提示
    path: '/international/subscribeRedeem',
    name: 'interSubscribeRedeem',
    meta: {
      title: i18n.t('router.subscribeRedeem')
    },
    component: () => import ('@/views/international/fund-transaction/subscribe-redeem.vue')
  },
  {
    path: '/international/risk',
    name: 'interRisk',
    meta: {
      title: i18n.t('router.risk')
    },
    component: () => import(/* webpackChunkName: "interRisk" */ '@/views/international/risk/index.vue'),
    redirect: '/international/risk/tips',
    children: [
      {
        path: '/international/risk/tips',
        name: 'interRiskTips',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "interRiskTips" */ '@/views/international/risk/riskTips.vue')
      },

      {
        path: '/international/risk/assessment',
        name: 'interRiskAssessment',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "interRiskAssessment" */ '@/views/international/risk/riskAssessment.vue')
      },
      {
        path: '/international/risk/questions/:id',
        name: 'interRiskQuestions',
        meta: {
          title: i18n.t('router.risk')
        },
        component: () => import(/* webpackChunkName: "interRiskQuestions" */ '@/views/international/risk/riskQuestions.vue')
      },
      {
        path: '/international/risk/disclosure',
        name: 'interRiskDisclosure',
        meta: {
          title: i18n.t('router.riskDisclosure')
        },
        component: () => import(/* webpackChunkName: "interRiskDisclosure" */ '@/views/international/risk/riskDisclosure.vue')
      },
      {
        path: '/international/risk/result',
        name: 'interRiskResult',
        meta: {
          title: i18n.t('router.riskResult')
        },
        component: () => import(/* webpackChunkName: "interRiskResult" */ '@/views/international/risk/riskResult.vue')
      },
      {
        path: '/international/risk/level',
        name: 'interRiskLevel',
        meta: {
          title: i18n.t('router.riskLevel')
        },
        component: () => import(/* webpackChunkName: "interRiskLevel" */ '@/views/international/risk/riskLevel.vue')
      },
      {
        path: '/international/risk/agreepage',
        name: 'interAgreePage',
        meta: {},
        component: () => import(/* webpackChunkName: "interAgreePage" */'@/views/international/risk/agreePage.vue')
      },
    ]
  },
  // 合适性匹配记录---国际版
  {
    path: '/international/suitabilityMatchingRecord',
    name: 'suitabilityMatchingRecord',
    meta: {
      title: i18n.t('router.suitabilityMatchingRecord')
    },
    component: () => import( /* webpackChunkName: "about" */ '@/views/international/fund-kind/suitability-matching-record.vue')
  },
   // 基金账户----国际版
  {
    path: '/international/fundAccount',
    name: 'fundAccount',
    meta: {
      title: i18n.t('router.fundAccount')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-account/index.vue'),
    children: [

    ]
  },
  { // 申购----国际版
    path: '/international/transactionBuy',
    name: 'interTransactionBuy',
    meta: {
      title: i18n.t('router.transactionBuy')
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/international/fund-transaction/transaction-buy.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})
router.beforeEach((to, from, next) => {
  const { source, lang, } = to.query;
  if (lang) {
    store.commit('setLang', lang);
  }

  if (source) {
    store.commit('setSource', source);
  }
  // 置顶
  if (['privateEquityFund', 'privateEquityFundDetails'].includes(to.name) || ['/privateEquityFund', '/privateEquityFundDetails'].includes(to.path)) {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
    window.pageYOffset = 0
  }
  next();
})

// router.afterEach((to, from, next) => {
//   console.log('置顶')
//   window.scrollTo(0,0);
// })
export default router
