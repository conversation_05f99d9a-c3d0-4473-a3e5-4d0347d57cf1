
import axios from '@/utils/axios';
import config from '@/config';

export function getOrderRecordList(params, ctx) {
  return axios.get('/open/fund/open/customer/trade/orders', {
    params,
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}
export function getClosedDays(params, ctx, fundIsin) {
  // {
  //   params,
  //   ctx: ctx,
  //   // headers: {
  //   //   Authorization: config.HEADERS_AUTH.Authorization
  //   // }
  // }
  return axios.get('/open/fund/open/fund/market/funds/' + fundIsin + '/closeDay', params)
}
export function getFundInformation(params) {
  return axios.get('/open/fund/open/fund/market/funds/' + params)
}
export function getAccountNumber(ctx, status) {
  // let paramsObj = { ctx: ctx };
  // if (status) {
  //   paramsObj = Object.assign(paramsObj, { headers: { Authorization: config.HEADERS_AUTH.Authorization } })
  // } else {
  //   paramsObj = Object.assign(paramsObj, { headers: { Authorization: "" } })
  // }
  return axios.get('/open/account/eddid/info/new')
  // {
  //   ctx: ctx,
  //   headers: {
  //     Authorization: config.HEADERS_AUTH.Authorization
  //   }
  // }
}

// 获取当前账户--风险测评是否过期
export function fundCashRiskAssessment(tradeAccount) {
  return  axios.get(`/open/account/trade/fundCash/query/fundCashRiskAssessment/result`)
}

// 获取当前账户信息
export function getAccountMoney(params) {
  return axios.get('/open/fund/open/customer/accounts/' + params + '/balances')
}

// 获取当前账户当前基金是否有持仓
export function getAccountPosition(tradeAccount, fundIsin) {
  return axios.get('/open/fund/open/customer/accounts/' + tradeAccount + '/positions/' + fundIsin)
}
// 获取当前订单信息
export function getOrderData(params) {
  return axios.get('/open/fund/open/customer/trade/orders/' + params)
}
// 撤销订单
export function cancelTheOrder(params) {
  return axios.delete('/open/fund/open/customer/trade/orders/' + params)
}

// 申购
export function purchase(params) {
  return axios.post('/open/fund/open/customer/trade/orders', params)
}
// 关注
export function fundFocus(params) {
  return axios.post('/open/account/eddid/optional/add', params)
}
// 取消关注
export function cancelFundFocus(params, ctx) {
  return axios.get('/open/account/eddid/optional/remove', {
    params,
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}

// 查询基金自选组
export function queryOptionsGroups(params) {
  return axios.get(`/open/account/options/groups?code=${params}`)
}
export function queryNewOptionsGroups(params) {
  return axios.get(`/open/account/options/v2/groups?code=${params}`)
}

// 查询邮箱
export function getAccountEmail(ctx, status) {
  let paramsObj = { ctx: ctx };
  if (status) {
    paramsObj = Object.assign(paramsObj, { headers: { Authorization: config.HEADERS_AUTH.Authorization } })
  } else {
    paramsObj = Object.assign(paramsObj, { headers: { Authorization: "" } })
  }
  return axios.get('/open/account/eddid/info/new', paramsObj)
  // {
  //   ctx: ctx,
  //   headers: {
  //     Authorization: config.HEADERS_AUTH.Authorization
  //   }
  // }
}
// 查询自选列表
export function getOptionalList(params, ctx) {
  return axios.get('/open/account/eddid/optional/query', {
    params,
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}
// 查询收益
export function getRevenuesNum(acounter, params, ctx) {
  return axios.get('/open/fund/open/customer/accounts/' + acounter + '/revenues', {
    params,
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}
// 查询复杂产品风险等级 以及复杂产品知识
export function getAccountRiskLevel(ctx) {
  return axios.get('/open/account/trade/fundCash/query/fundCashRiskAssessment/result', {
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}

// 资金流水
export function capitalFlowsList(acounter, params, ctx) {
  return axios.get('/open/fund/open/customer/accounts/' + acounter + '/capitalFlows', {
    params,
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}