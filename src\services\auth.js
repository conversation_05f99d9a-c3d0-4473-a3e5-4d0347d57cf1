
import axios from '@/utils/axios';
import config from '@/config';

const prefix = `/open/auth`;
export function getSmsCode(params, ctx) {
  return axios.post(`${prefix}/v3/sms-code-h5`,params, {
    ctx: ctx,
    headers: {
      Authorization: config.HEADERS_AUTH.Authorization
    }
  })
}
export function login (params, ctx) {
  return axios.post(`${prefix}/v2/token`, params, { ctx: ctx, headers: config.HEADERS_AUTH })
}