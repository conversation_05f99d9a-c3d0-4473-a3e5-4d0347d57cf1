import axios from '@/utils/axios';

const prefix = `/open/account/`;

// 银行子账户
export function uploadOss (params, ctx) {
  return axios.post(`${prefix}/path/oss/business`, params , { ctx: ctx })
}

// 协议条款内容
export function agreementContent(params) {
  return axios.post('/open/account/eddid/agreement/content', params)
}

export function agreementH5(URL) {
  return axios.get(URL)
}

// 协议条款内容
export function commitShareInfo(params) {
  return axios.post(`${window.serverUrl || process.env.VUE_APP_OPERATION_API}/open/info/content/share`, params)
}

// 字典项-查询跳转链接
export function queryLink(params) {
  return axios.get(`/open/operation/system/dict/keyText`, { params })
}