import axios from '@/utils/axios';
import config from '@/config';

const prefix = `/open/fund/open/fund`;

/**
 * 基金列表
 */
export function getCompanies(params, ctx) { // 基金公司数据列表
  // console.log(params, ctx);
  return axios.get(`${prefix}/market/companies`, {
    params
  })
}


export function getCompaniesDetails_funds(params, ctx, companyId, type) { // 基金公司--详情 & 旗下基金
  return axios.get(`${prefix}/market/companies/${companyId}` + (type == 'detail' ? '' : '/funds'), {
    params
  })
}

export function fundsRankingList(params, ctx) { // 基金排行
  return axios.get(`${prefix}/market/funds`, {
    params
  })
}

/**
 * 产品详情
 */
export function fundProductFun(ID, params, ctx) { // 详细信息
  return axios.get(`${prefix}/market/funds/${ID}`, {
    params
  })
}

/**
 * /open/account/options/queryAllCodes
 * 自选表-查询所有已添加自选code
 */

export function queryAllCodes(marketTypes = 'FUND') {
  return axios.get(`/open/account/options/queryAllCodes?marketTypes=${marketTypes}`)
}

/**
 * 
 * @param {*} ID  对应基金ID
 * @param {*} ctx 
 * @param {*} url 动态接口地址
 * @returns 
 *  历史业绩-performance
 *  万元收益-ten-thousand
 *  历史净值-netValues
 *  基金休息日-closeDay
 */
export function performance(params, ID, ctx, url, type) {
  return axios.get((type == 'networth' || type == 'trend' || type == 'closeDay') ? `${prefix}/market/funds/${ID}/${url}` : `${prefix}/market/funds/${ID}/profits/${url}`, {
    params: params
  })
}

export function fundCompositions(ID, ctx) { // 基金持仓分布
  return axios.get(`${prefix}/market/funds/${ID}/compositions`)
}

export function tradeRules(ID, time, ctx) { // 交易规则
  let urlTime = time ? `?tradeTime=${time}` : ''
  return axios.get(`${prefix}/market/funds/${ID}/trade-rules` + urlTime)
}

export function fundRecord(ID, ctx) { // 基金档案
  return axios.get(`${prefix}/market/funds/${ID}/record`)
}


export function getRiskLevelInfo(params) {
  return axios.get(`/open/operation/noun`, {
    params: params
  })
}

export function getHtml (url, params) {
  return axios.get(url, { params: params })
}