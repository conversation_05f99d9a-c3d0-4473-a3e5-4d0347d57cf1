import axios from '@/utils/axios';

export function getQuestionList() {
  return axios.get('/open/account/trade/fundCash/risk/assessment/questions')
}

export function submitQuestions(params) {
  return axios.post('/open/account/trade/fundCash/submit/fundCashRiskAssessment', params)
}

export function getRiskLevel(params) {
  return axios.get('/open/account/trade/fundCash/query/fundCashRiskAssessment/result',{params})
}

export function confirmQuestions() {
  return axios.post('/open/account/trade/fundCash/submit/fundCashRiskAssessment/agreement', {
    agreement: true
  })
}

export function submitOpenAccount() {
  return axios.post('/open/account/trade/trade-account/application', {
    agreement: true,
    infoIsSame: true,
    submitSource: 'CP_H5',
    tradeAccountType: 'FUND_CASH',
  })
}

export function getOpenAccountStatus (params) {
  return axios.get('/open/account/trade/fundCash/query/fundCashStatus',{params})
}

// 专业投资者认证-题目
export function getPIQuestionList () {
  return axios.get('/open/account/professionalInvestor/pi/questions')
}

// 提交-专业投资者认证
export function submitPIQuestions (params) {
  return axios.post('/open/account/professionalInvestor/pi/submit/professionalInvestor',params)
}

// 专业投资者认证-确认结果无误
export function professionalInvestorAgreement () {
  return axios.post('/open/account/professionalInvestor/pi/submit/professionalInvestor/agreement',{agreement: true})
}