import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate'
import { setToken } from '@/utils/auth';

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    themes: ['light', 'dark', 'dodgerlight', 'futulight', 'tigerlight'],
    theme: 'light',
    locales: ['zh-hans', 'zh-hant', 'en'],
    locale: 'zh-hans',
    isInApp: false,
    source: 'pc',
    klineTheme: 'redRiseGreenFall' || localStorage.getItem('klineTheme'), // redRiseGreenFall: 红涨绿跌 (默认), greenRiseRedFall: 绿涨红跌
    isInternational: false,
    token: '',
    accountInfo: {}, // 用户、账号信息
    userInfo: {}, // 用户信息
    accountNumber: '',
    signBase64: ''
  },
  mutations: {
    setSource(state, val) {
      state.source = val;
      state.isInApp = val === 'app';
      localStorage.setItem('source', val);
    },
    setTheme(state, theme) {
      const val = state.themes.includes(theme) ? theme : 'light';
      state.theme = val
      localStorage.setItem('theme', val);
    },
    setLang(state, locale) {
      const val = state.locales.includes(locale) ? locale : 'zh-hans';
      state.locale = val;
      localStorage.setItem('lang', val);
    },
    setKlineTheme(state, data) {
      state.klineTheme = data;
    },
    setIsInternational(state, data) {
      state.isInternational = data;
      localStorage.setItem('isInternational', data)
    },
    UPDATE_TOKEN(state, token) {
      setToken(token);
      state.token = token;
    },
    setAccountInfo(state, data) {
      state.accountInfo = data
    },
    setUserInfo(state, data) {
      state.userInfo = data
    },
    setAccountNumber(state, data) {
      state.accountNumber = data
    },
    setSignBase64(state, data) {
      state.signBase64 = data
    }
  },
  actions: {},
  modules: {},
  plugins: [createPersistedState({
    storage: window.sessionStorage
  })],
})
