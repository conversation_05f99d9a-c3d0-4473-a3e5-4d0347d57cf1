export const getSystem = () => {
  let u = window.navigator.userAgent
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 // android终端
  let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
  if (isAndroid) return 'android'
  if (isiOS) return 'ios'
  return ''
}

export const transAccountNum = (val) => {
  if (val) {
    return val.substring(0,4)+' **** ****'+' '+val.slice(-4)
  }
}

// 银行卡号每4位加空格
export const transBankNum = (val) => {
  if (val) {
    return val.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ')
  }
}