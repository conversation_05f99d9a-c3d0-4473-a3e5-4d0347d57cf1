import listenRNMessage from "@/utils/app/listenRNMessage";
import sendRNMessage from "@/utils/app/sendRNMessage";
import urlParse from "./url-parse";
import eventBus from './eventBus';
// import store from 'store';

listenRNMessage();
sendRNMessage({
  command: "getAccessToken",
  body: {
    type: "B",
    callBack: "getAccessToken"
  }
});
sendRNMessage({
  command: "getAppInfo",
  body: {
    callBack: "getAppInfo"
  }
});

let source = urlParse(window.location.search).source
  ? urlParse(window.location.search).source
  : "pc";
localStorage.setItem("source", source);

eventBus.$on("appLoginCompletedEvent", async body => {
  // store.set("access_token", `Bearer ${body.phone_token}`);
  sessionStorage.setItem("access_token", `Bearer ${body.phone_token}`);
  // localStorage.setItem("expireTime", body.expireTime);
});
eventBus.$on("generateNewTokenEvent", async body => {
  // store.set("access_token", `Bearer ${body.accessToken}`);
  sessionStorage.setItem("access_token", `Bearer ${body.accessToken}`);
  // localStorage.setItem("expireTime", body.expireTime);
});