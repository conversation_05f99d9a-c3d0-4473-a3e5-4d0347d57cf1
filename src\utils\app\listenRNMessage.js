
import urlParse from './url-parse'
import eventBus from './eventBus'
export default () => {
  const accessTokenHandle = (body) => {
    
    if (body.phone_token) {
      // console.log('getTokenByIOS>>>>>>>>>', body.accessToken)
      eventBus.$emit('appLoginCompletedEvent', body)
    }
  }
  const generateNewTokenHandle = (body) => {
    
    if (body.phone_token) {
      // console.log('getTokenByIOS>>>>>>>>>', body.accessToken)
      eventBus.$emit('generateNewTokenEvent', body)
    }
  }

  const backNativeHandle = ({ isBack }) => {
    // console.log('command-----backNative', isBack)
    let value = isBack ? `${isBack}''` : '0'
    localStorage.setItem('backNative', value)
  }

  const loginCompletedHandle = (body) => {
    eventBus.$emit('appLoginCompletedEvent', body)
  }

  const faceVerificationHandle = (body) => {
    console.log('触发faceVerificationHandle>>>>>', body)
    eventBus.$emit('faceVerificationHandle', body)
  }

  const callBackImageReaderHandle = (body) => {
    console.log('callBackImageReaderHandle', body)
    eventBus.$emit('callBackImageReaderHandle', body)
  }
  const getPermissonInfo = (body) => {
    console.log('getPermissonInfo', body)
    eventBus.$emit('getPermissonInfo', body)
  }

  const getAppInfoHandle = (body) => { // body = { appVersion: 'app版本号', deviceModel: '设备机型' };
    console.log(body, 'getAppInfoHandle','---------------------------------')
    sessionStorage.setItem('appVersion', body.appVersion)
    sessionStorage.setItem('deviceId', body.deviceId)
    sessionStorage.setItem('theme', body.appTheme)
    sessionStorage.setItem('isInternational', body.isInternational === 'undefined' || body.isInternational === undefined ? false : body.isInternational)
  }
  
  const handlers = {
    getAccessToken: accessTokenHandle,
    generateNewToken: generateNewTokenHandle,
    backNative: backNativeHandle,
    // loginCompletedEvent: loginCompletedHandle,
    // faceVerificationEvent: faceVerificationHandle,
    getAppInfo: getAppInfoHandle,
    getPermissonInfo: getPermissonInfo
    // callImageReaderEvent: callBackImageReaderHandle
  }

  let handleMessage = (data) => {
    console.log('data---1.2>>>>>>>>>>>>>>>>', data)
    const { command, body } = Object.prototype.toString.call(data) === '[object Object]' ? data : JSON.parse(data)
    handlers[command](body)
  }

  const { WebViewBridge, ReactNativeWebView } = window
  const source = urlParse(window.location.search).source
  
  if (source === 'app') { // 原生跟js交互
    const arr = Object.keys(handlers)

    arr.forEach((item) => {
      window[item] = handlers[item]
    })
    console.log('window>>>>>>', window)
  } else if (ReactNativeWebView) {
    window.document.addEventListener('message', (e) => {
      handleMessage(e.data)
    })
  } else if (WebViewBridge) {
    WebViewBridge.onMessage = (e) => {
      handleMessage(e)
    }
  }
}
