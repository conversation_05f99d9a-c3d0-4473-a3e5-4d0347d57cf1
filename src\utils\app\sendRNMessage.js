import urlParse from './url-parse'
import { getSystem } from './common'

export default (data) => {
  let stringifyObj = JSON.stringify(data.body)
  const { WebViewBridge, ReactNativeWebView } = window
  const source = urlParse(window.location.search).source || localStorage.getItem('source')

  if (source === 'app') { // 原生跟js交互
    try {
      const dict = {
        android: () => {
          console.log('postMessage>>>>>', data.command, data.body)
          /* global $App:true */
          $App[data.command](JSON.stringify(data.body))
        },
        ios: () => {
          // let command = 'callIOSMethod'
          console.log(data.command,'data.command');
          let params = {
            data: data.body,
            callBack: data.body.callBack
          }
          window.webkit && window.webkit.messageHandlers[data.command].postMessage(params)
          // window.webkit && window.webkit.messageHandlers[data.command].postMessage(data.body)
        }
      }
      const system = getSystem()
      dict[system]()
    } catch (error) {
      console.log('sendRNMessage.error>>>>>', error)
    }
  } else if (WebViewBridge) {
    // WebViewBridge
    console.log('WebViewBridge：h5======>rn', stringifyObj)
    WebViewBridge.send(stringifyObj)
  } else if (ReactNativeWebView) {
    console.log('ReactNativeWebView：h5======>rn', stringifyObj)
    // android专用
    window.ReactNativeWebView.postMessage(stringifyObj)
  }
}
