/**
 * @param  {string} query 形如 location.search
 * @returns {object}
 */

function getQuertType (key) {
  if (key.endsWith('[]')) return 'ARRAY'
  if (key.endsWith('{}')) return 'JSON'
  return 'DEFAULT'
}

export default (query) => {
  if (!query) {
    return {}
  }
  query = query.replace(/^\?/, '')
  const queryArr = query.split('&')
  const result = {}
  queryArr.forEach((q) => {
    let [key, value] = q.split('=')
    try {
      value = decodeURIComponent(value || '').replace(/\+/g, ' ')
      key = decodeURIComponent(key || '').replace(/\+/g, ' ')
    } catch (e) {
      // 非法
      console.log(e)
      return
    }
    const type = getQuertType(key)
    switch (type) {
      case 'ARRAY':
        key = key.replace(/\[\]$/, '')
        if (!result[key]) {
          result[key] = [value]
        } else {
          result[key].push(value)
        }
        break
      case 'JSON':
        key = key.replace(/\{\}$/, '')
        value = JSON.parse(value)
        result.json = value
        break
      default:
        result[key] = value
    }
  })
  return result
}
