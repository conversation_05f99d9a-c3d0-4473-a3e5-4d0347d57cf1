import jsBridge from '@/utils/jsBridge';
import jwt_decode from "jwt-decode";

export const isInApp = source => {
  source = source || localStorage.getItem('source');
  return source === 'app';
}

export const getToken = () => {
  return sessionStorage.getItem("access_token");
}

export const setToken = token => {
  sessionStorage.setItem("access_token", token ? `Bearer ${token}` : '');
}

export const clearToken = () => {
  sessionStorage.setItem("access_token", '');
}

export const getAppToken = () => {
  jsBridge.run('getAccessToken', {
    type: 'B',
    callback: ({ phone_token }) => {
      console.log('获取 app token >>>', phone_token);
      setToken(phone_token);
    }
  })
}

export const isTokenExpire = token => {
  if(!token) return false;

  let expireTime = jwt_decode(token.split(" ")[1]).exp;
  let now = new Date().getTime();
  now = now / 1000;
  let isExpire = expireTime === 0 || ((expireTime - now) / 60 < 10);
  return isExpire;
}

export const refreshAppToken = () => {
  return new Promise((resolve, reject) => {
    jsBridge.run('generateNewToken', {
      callback: ({ accessToken }) => {
        console.log('刷新 app token >>>', accessToken);
        setToken(accessToken);
        resolve(accessToken);
      }
    })
  })
}

export function getEddId(token) {
  try {
    let tokenStr = token || getToken() || '';

    if (!tokenStr) return '';

    const json = atob(tokenStr.split('.')[1]);
    const info = JSON.parse(json);
    localStorage.setItem('eddId', info.sub)
    return info.sub;
  } catch (e) {
    console.log(e, 'get token error');
    return ''
  }
}