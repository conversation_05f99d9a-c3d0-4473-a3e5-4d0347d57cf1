import axios from "axios";
import { Toast } from "mand-mobile";
import $router from "@/router";
import { getToken, clearToken, isInApp, isTokenExpire, refreshAppToken, } from "@/utils/auth";
import { LangMap } from "@/utils/contants";
import Vue from 'vue';
import i18n from '@/utils/i18n';
console.log('i18n--------------', i18n);
const service = axios.create({
  baseURL: window.serverUrl || process.env.VUE_APP_SERVER_URL,
  timeout: 15000,
})

let pending = [];
let cancelToken = axios.CancelToken;
let removePending = config => {
  if (!config) {
    return;
  }
  for (let p in pending) {
    // if (pending[p].u.replace(/[\\"]/g, "") === config.url + (config.data ? JSON.stringify(config.data).replace(/[\\"]/g, "", "") : "") + "&" + config.method) {
    if ((config.url + (config.data ? JSON.stringify(config.data).replace(/[\\"]/g, "", "") : "") + "&" + config.method).includes(pending[p].u.replace(/[\\"]/g, ""))) {
      //当当前请求在数组中存在时执行函数体
      pending[p].f(); //执行取消操作
      pending.splice(p, 1);
    }
  }
  if(pending.length === 0){
    Vue.prototype.$loading.reset()
  }
};
// 请求拦截器
service.interceptors.request.use(

  async request => {

    removePending(request);
    // console.log(request,' request')
    if(!request.noLoading){
      Vue.prototype.$loading.show()
    }

    request.cancelToken = new cancelToken(c => {
      pending.push({
        u: request.url + (request.data ? JSON.stringify(request.data) : "") + "&" + request.method,
        f: c,
      }); //config.data为请求参数
    });

    let token = getToken();
    if(token) {
      const isInEddidApp = isInApp();
      const isExpire = isTokenExpire(token);
      if (isInEddidApp && isExpire) {
        // 刷新Token
        token = await refreshAppToken();
      }
    }


    // 非登录页面的接口 加 token
    if (request.url && request.url.indexOf('/v2/token') < 0) {
      token = getToken();
      if(token && token !== 'null') {
        request.headers['Authorization'] = token;
      }
    }
    let isInternational = localStorage.getItem('isInternational')
    let appVersion = ['true', true].includes(isInternational) ? 'GLOBAL' : 'CHINA'
    request.headers["accept-language"] = LangMap[localStorage.getItem("lang")] || "zh-CN";
    request.headers["appVersion"] = appVersion;

    return request;
  },
  error => {

    // Toast.failed('网络异常，稍后尝试访问')
    // return Promise.reject(error);
  }
);

service.interceptors.response.use(
  response => {
    let data = {};
    removePending(response.config);
    if (response.config && response.config.responseType === "blob") {
      data = response.data;
    } else if (response && response.data && response.data.code) {
      data = JSON.parse(JSON.stringify(response.data));
    } else {
      data = JSON.parse(JSON.stringify(response));
    }

    return data;
  },

  async error => {
    if(error.config){
      removePending(error.config);
    }
    Vue.prototype.$loading.reset()
    if(error.message && !error.response && error.stack.indexOf('Network Error') > -1){
      Toast.failed(i18n.t('common.networkAnomalyCheck'))
      return
    }else if(error.message && !error.response && error.stack.indexOf('timeout') > -1){
      Toast.failed(i18n.t('common.networkAnomalyRetry'))
      return
    }
    let errorResponse = error.response && error.response.data ? error.response.data : {};
    let status = error.response && error.response.status ? error.response.status : "";
    // console.log('errorResponse ==', error, error.response)
    //
    const isInEddidApp = isInApp();

    if (status === 401) {
      if(!isInEddidApp){
        clearToken();
        if(window.location.pathname !== '/login'){
          $router.push({ path: "/login" ,query: { type: 'code' }});
        }
      } else {
        let token = await refreshAppToken();
        if (token) {
          Toast.failed(i18n.t('common.networkAnomalyRetry'), 5000);
        } else {
          Toast.failed(i18n.t('common.networkAnomalyLogin'), 5000);
        }
      }
      return errorResponse
    }

    if (errorResponse) {
      if (!status) {
        // Toast.failed(errorResponse.msg)
      } else if (error.response) {
        Toast.failed(errorResponse.msg)
      } else {
        Toast.failed(errorResponse.msg || i18n.$t('common.networkAnomalyRetry'))
      }
    } else {
      Toast.failed(i18n.$t('common.networkAnomalyRetry'))
    }
  }
);

export default service;
