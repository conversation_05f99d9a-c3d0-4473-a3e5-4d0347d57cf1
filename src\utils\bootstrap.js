import Vue from "vue";
import {
  Swiper,
  SwiperItem,
  InputItem,
  Field,
  FieldItem,
  CellItem,
  Agree,
  Button,
  Landscape,
  NoticeBar,
  Toast,
  Steps,
  Radio,
  Icon,
  Selector,
  Amount,
  Dialog,
  TextareaItem,
} from "mand-mobile";

Vue.component(Swiper.name, Swiper);
Vue.component(SwiperItem.name, SwiperItem);
Vue.component(Button.name, Button);
Vue.component(InputItem.name, InputItem);
Vue.component(TextareaItem.name, TextareaItem);
Vue.component(Field.name, Field);
Vue.component(FieldItem.name, FieldItem);
Vue.component(CellItem.name, CellItem);
Vue.component(Agree.name, Agree);
Vue.component(Icon.name, Icon);
Vue.component(Steps.name, Steps);
Vue.component(Landscape.name, Landscape);
Vue.component(NoticeBar.name, NoticeBar);
Vue.component(Radio.name, Radio);
Vue.component(Selector.name, Selector);
Vue.component(Amount.name, Amount);
Vue.component(Dialog.name, Dialog);
