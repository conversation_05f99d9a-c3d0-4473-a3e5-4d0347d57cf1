export function riskLevel(type, data) { // 基金风险等级
  let riskLevelMsg = ''
  switch (type) {
    case 5:
      riskLevelMsg = data.HIGH;
      break;
    case 4:
      riskLevelMsg = data.MID_HIGH;
      break;
    case 3:
      riskLevelMsg = data.MIDDLE;
      break;
    case 2:
      riskLevelMsg = data.MID_LOW;
      break;
    case 1:
      riskLevelMsg = data.LOW;
      break;
    default:
      riskLevelMsg = 'empty';
      break;
  }
  return riskLevelMsg;
}
export function riskLevelList(type, data) { // 基金风险等级
  let riskLevelMsg = ''
  switch (type) {
    case 5:
      riskLevelMsg = data.HIGH;
      break;
    case 4:
      riskLevelMsg = data.MID_HIGH;
      break;
    case 3:
      riskLevelMsg = data.MIDDLE;
      break;
    case 2:
      riskLevelMsg = data.MID_LOW;
      break;
    case 1:
      riskLevelMsg = data.LOW;
      break;
    default:
      riskLevelMsg = 'empty';
      break;
  }
  return riskLevelMsg;
}

export function bondType(type, data) { // 债券类型
  // console.log('债券类型====', type, data);
  let bondTypeMsg = '';
  switch (type) {
    case 'EQUITY':
      bondTypeMsg = data.EQUITY;
      break;
    case 'FIXED_INTEREST':
      bondTypeMsg = data.FIXED_INTEREST;
      break;
    case 'MIXED_ASSET':
      bondTypeMsg = data.MIXED_ASSET;
      break;
    case 'MONEY_MARKET':
      bondTypeMsg = data.MONEY_MARKET;
      break;
    default:
      bondTypeMsg = data.EQUITY;
      break;
  }
  return bondTypeMsg;
}

export function resolveLangName(type = 'zh-hans', data = {}) {
  if (type == 'zh-hans') {
    return data && data.cn ? data.cn : '--';
  } else if (type == 'zh-hant') {
    return data && data.hk ? data.hk : '--';
  } else {
    return data && data.us ? data.us : '--';
  }
}