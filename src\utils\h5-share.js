import Vue from 'vue';
import wx from 'weixin-js-sdk';
import * as dd from "dingtalk-jsapi";
import axios from 'axios';

function wxShare (url, lang, shareInfo, success) {
  const nowTime = JSON.stringify(Math.round(new Date() / 1000));

  axios(`${window.serverUrl || process.env.VUE_APP_SERVER_URL}/open/account/wechat/business/getSignature`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=utf8',
      'Accept-Language': lang,
      "access-key": '********************************',
    },
    data: {
      nonceStr: nowTime,
      timestamp: nowTime,
      url
    }
  })
    .then(({ data }) => {
      const { appId, timestamp, nonceStr, signature } = data && data.data ? data.data : {};
      appId && wx.config({
        debug: false,
        appId: appId,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature,
        jsApiList: [
          "updateAppMessageShareData", // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容
          "updateTimelineShareData", // 自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容
          "onMenuShareWeibo", // 获取“分享到腾讯微博”按钮点击状态及自定义分享内容接口
        ]
      })

      wx.ready(function(){
        const { title, desc, link, imgUrl } = shareInfo;
        wx.updateAppMessageShareData({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: function(res){
            success && success()
          },
          fail: function(error){
            console.log("分享 wx.updateAppMessageShareData 失败", error);
          }
        });

        wx.updateTimelineShareData({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: function(res){
            success && success(res)
          },
          fail: function(error){
            console.log("分享 wx.updateTimelineShareData 失败", error);
          }
        });

        wx.onMenuShareWeibo({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: function(res){
            success && success()
          },
          fail: function(error){
            console.log("分享 wx.updateAppMessageShareData失败", error);
          }
        });
      });

      wx.error(function(res){
        console.log('wx.error ====', res);
      });
    })
    .catch(err => {
      console.log('wx getSignature error', err)
    })
}

function ddShare (url, lang, shareInfo, success) {
  if(navigator.userAgent.indexOf('DingTalk') === -1) return;

  dd.ready(function () {
    dd.biz.navigation.setRight({
      show: true, // 控制按钮显示， true 显示， false 隐藏， 默认true
      control: true, // 是否控制点击事件，true 控制，false 不控制， 默认false; 如果control为true，则onSuccess将在发生按钮点击事件被回调
      text: '...', // 控制显示文本，空字符串表示显示默认文本
      onSuccess: function (result) {
        const { title, desc, link, imgUrl } = shareInfo;
        dd.biz.util.share({
          type: 0, //分享类型，0:全部组件 默认； 1:只能分享到钉钉；2:不能分享，只有刷新按钮
          url: link,
          title,
          content: desc,
          image: imgUrl,
          onSuccess: function () { },
          onFail: function (err) { }
        })
      },
      onFail: function (err) { }
    });
  })
}

const H5Share = {
  install(Vue){
    Vue.prototype.$wxShare = wxShare;
    Vue.prototype.$ddShare = ddShare;
    Vue.prototype.$h5Share = function (url, lang, shareInfo, success) {
      wxShare(url, lang, shareInfo, success);
      ddShare(url, lang, shareInfo, success);
    }
  }
}

Vue.use(H5Share);