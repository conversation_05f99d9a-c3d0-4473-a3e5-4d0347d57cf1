import Vue from 'vue';
import VueI18n from 'vue-i18n';
import zhhansLocale from '@/locales/zh-hans/index';
import zhhantLocale from '@/locales/zh-hant/index';
import enUSLocale from '@/locales/en-us/index';
import moment from 'moment';
import storage from 'store';
import store from '@/store';
import { urlParse } from '@/utils/util';

Vue.use(VueI18n);
const lang = urlParse(window.location.search).lang || store.state.locale;
const i18n = new VueI18n({
  locale: lang,
  fallbackLocale: 'zh-hans',
  messages: {
    "zh-hans": zhhansLocale,
    "zh-hant": zhhantLocale,
    "en": enUSLocale,
  }
});

// export default ({ app, store }) => {
//   // Set i18n instance on app
//   // This way we can use it in middleware and pages asyncData/fetch
//   app.i18n.path = (link) => {
//     if (app.i18n.locale === app.i18n.fallbackLocale) {
//       return `/${link}`
//     }

//     return `/${app.i18n.locale}/${link}`
//   }
// }
export const defaultLang = 'zh-hans';


// const loadedLanguages = [defaultLang]

function setI18nLanguage(lang) {
  i18n.locale = lang
  // request.headers['Accept-Language'] = lang
  document.querySelector('html').setAttribute('lang', lang)
  return lang
}

// export function loadLanguageAsync (lang = defaultLang) {
//   return new Promise(resolve => {
//     // 缓存语言设置
//     storage.set('lang', lang)
//     if (i18n.locale !== lang) {
//       if (!loadedLanguages.includes(lang)) {
//         return import(/* webpackChunkName: "lang-[request]" */ `./lang/${lang}`).then(msg => {
//           const locale = msg.default
//           i18n.setLocaleMessage(lang, locale)
//           loadedLanguages.push(lang)
//           moment.updateLocale(locale.momentName, locale.momentLocale)
//           return setI18nLanguage(lang)
//         })
//       }
//       return resolve(setI18nLanguage(lang))
//     }
//     return resolve(lang)
//   })
// }

export function i18nRender(key) {
  return i18n.t(`${key}`)
}
export default i18n;