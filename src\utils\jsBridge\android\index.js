import { globaler, omit } from '../shared';
import { ANDROID } from '../shared/constants';
import { callErrorTips, notRegisteredTips } from '../shared/tooltip';
import { register } from '../shared/register';
import ConfigAppCallback from '../shared/setBar';

export default {
  type: ANDROID,

  isSupported (name, options = {}) {
    const apis = globaler['$App'] || {};
    const has = !!apis[name];
    if (!has) {
      notRegisteredTips(name, ANDROID, options);
    }

    return has;
  },

  run (name, options) {
    const callback = options.callback;
    const cancelCallBack = options.cancelCallBack;
    let body = {
      ...options
    };

    try {
      if (options.setNavBar) {
        new ConfigAppCallback(name, options, 'Android');
        return
      }
      if (callback) {
        const callbackName = register(callback);
        body = omit(body, 'callback');
        body.callBack = callbackName;
      }
      if (cancelCallBack) {
        const callbackName = register(cancelCallBack);
        body = omit(body, 'cancelCallBack');
        body.cancelCallBack = callbackName;
      }
      window.$App[name](JSON.stringify(body));
    } catch (error) {
      callErrorTips(name, ANDROID, options, error)
    }
  }
}