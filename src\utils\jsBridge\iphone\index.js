import { omit, isFunction } from '../shared';
import { IPHONE } from '../shared/constants';
import { callErrorTips, notRegisteredTips } from '../shared/tooltip';
import { register } from '../shared/register';
import ConfigAppCallback from '../shared/setBar';

export default {
  type: IPHONE,

  isSupported (name, options = {}) {
    let support = false;
    let errorMsg;

    try {
      const postMessage = window.webkit.messageHandlers[name].postMessage;
      if (isFunction(postMessage)) {
        support = true;
      }
    } catch(error) {
      errorMsg = error;
    }

    if (!support) {
      notRegisteredTips(name, IPHONE, options, errorMsg);
    }

    return support;
  },

  run (name, options) {
    const callback = options.callback;
    const cancelCallBack = options.cancelCallBack;
    let body = {}
    let data = {
      ...options
    }

    try {
      if (options.setNavBar) {
        new ConfigAppCallback(name, options, 'IOS');
        return
      }
      if (callback) {
        const callbackName = register(callback);
        data = omit(data, 'callback');
        body.callBack = callbackName;
      }
      if (cancelCallBack) {
        const callbackName = register(cancelCallBack);
        data = omit(data, 'cancelCallBack');
        body.cancelCallBack = callbackName;
      }
      body.data = data;

      window.webkit.messageHandlers[name].postMessage(body);
    } catch (error) {
      callErrorTips(name, IPHONE, options, error)
    }
  }
}