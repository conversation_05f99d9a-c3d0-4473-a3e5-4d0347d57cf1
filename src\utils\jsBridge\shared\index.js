import { ANDROID, IPHONE, PC } from './constants';
const call = (value) => Object.prototype.toString.call(value);

export const isArray = (value) => call(value) === '[object Array]';

export const isObject = (value) => call(value) === '[object Object]';

export const isFunction = (value) => call(value) === '[object Function]';

export const globaler = typeof window === 'object' ? window : {};

export const userAgent = globaler.navigator ? globaler.navigator.userAgent : '';

export const isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1;

export const isIphone = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

export const getPlatform = () => {
  if (isAndroid) {
    return ANDROID;
  }

  if (isIphone) {
    return IPHONE;
  }

  return PC;
}

// 删除对象中的属性，delete 性能较差
// property 只能为字符串或数组
export const omit = (object, property) => {
  if (!property) {
    return object;
  }

  const pro = isArray(property) ? property : [property];

  return Object.keys(object).reduce((newObj, value) => {
    if (!pro.includes(value)) {
      newObj[value] = object[value];
    }

    return newObj;
  }, {})
}