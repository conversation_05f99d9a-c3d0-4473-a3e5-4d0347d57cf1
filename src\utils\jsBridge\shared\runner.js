import Android from '../android';
import Iphone from '../iphone';
import { getPlatform } from './index';
import { notSupportTips } from './tooltip';
import { ANDROID, IPHONE, PC } from './constants';

const defaultRunner = {
  isSupported: (name, options = {}) => {
    notSupportTips(name, PC, { options });
    return false;
  },

  run (name, options) {
    notSupportTips(name, PC, options);
  }
}

const runner = () => {
  const runnerList = {
    [ANDROID]: Android,
    [IPHONE]: Iphone,
    [PC]: defaultRunner
  }
  const platform = getPlatform();

  return runnerList[platform] || defaultRunner;
}

export default runner();