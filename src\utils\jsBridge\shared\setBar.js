/**
 * 配置APP端接口及全局回调方法
 */
export default class ConfigAppCallback {
  uuid = 0;
  /**
   * @param {String} name app端接口名
   * @param {Object} options 包含回调函数的配置对象
   */
  constructor(name, options, type) {
    this.name = name;
    this.options = options;
    this.type = type;
    if (type === "Android") {
      this.setAndroidCallback();
    } else {
      this.setIOSCallback();
    }
  }

  /**
   * Android callback set
   */
  setAndroidCallback() {
    const { right = [], ...callbackObj } = this.options;
    right.length &&
      right.forEach((item) => {
        if (item.callback) {
          const callbackName = this.register(item.callback);
          item.callback = callbackName;
        }
      });
    Object.values(callbackObj).length &&
      Object.values(callbackObj).forEach((callback) => {
        if (typeof callback === "function") {
          const callbackName = this.register(callback);
          this.options[callback.name] = callbackName;
        }
      });
    window.$App[this.name](JSON.stringify(this.options));
  }

  /**
   * IOS callback set
   */
  setIOSCallback() {
    const { right = [], ...callbackObj } = this.options;
    const body = {
      data: this.options,
    };
    right.length &&
      right.forEach((item) => {
        if (item.callback) {
          const callbackName = this.register(item.callback);
          item.callback = callbackName;
        }
      });
    Object.values(callbackObj).length &&
      Object.values(callbackObj).forEach((callback) => {
        if (typeof callback === "function") {
          const callbackName = this.register(callback);
          body.data[callback.name] = callbackName;
        }
    });
    window.webkit.messageHandlers[this.name].postMessage(body);
  }

  /**
   * @method register 用于注册全局方法
   * @param {Function} callback 回调函数
   * @returns {String} 已注册的全局方法名
   */
  register(callback) {
    const dataString = Date.now().toString(32);
    const randString = Math.random().toString(32).slice(2);
    const name = `__CALLBACK__${++this.uuid}_${dataString}_${randString}`;
    window[name] = (body) => callback(body);
    return name;
  }
}
