// 一般在非移动端提示
export const notSupportTips = (name, platrform, options = {}, error = {}) => {
  console.warn(`[Not Supported] 方法 "${name}" 不存在; 详情请查看: `, { api: name, platrform, options, error });
};

// 一般在移动端，方法未注册提示
export const notRegisteredTips = (name, platrform, options = {}, error = {}) => {
  console.warn(`[Not registered] 方法 "${name}" 未注册; 详情请查看: `, { api: name, platrform, options, error });
};

// 执行方法出错提示
export const callErrorTips = (name, platrform, options = {}, error = {}) => {
  console.warn(`[call Error] 方法 "${name}" 执行出错; 详情请查看: `, { api: name, platrform, options, error });
};