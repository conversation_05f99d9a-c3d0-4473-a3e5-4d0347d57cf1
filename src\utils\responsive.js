(function (window, document) {
  function resize() {
    var ww = window.innerWidth
    if (ww > window.screen.width) {
      window.requestAnimationFrame(resize)
    } else {
      if (ww > 750) {
        ww = 750
      }
      document.documentElement.style.fontSize = '50px'
    }
  }

  resize()
  //去掉looding
  setTimeout(function () {
    if (document.getElementById('nuxt-loading')) {
      document.getElementById('nuxt-loading').style.display = 'none'
    }
    // document.getElementsByTagName("body")[0].setAttribute("style","background-color:red ")
  }, 30)
  window.addEventListener('resize', resize)
})(window, document)
