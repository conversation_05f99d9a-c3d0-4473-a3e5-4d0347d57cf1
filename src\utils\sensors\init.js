const sensors = require('sa-sdk-javascript');

// sensors.init({
//   server_url: process.env.VUE_APP_SONSORS_URL,
//   show_log: process.env._ENV !== "production",
//   name: 'sensors',
//   app_js_bridge: true, // 打通 App 与 H5
//   is_track_single_page: true, // 单页面浏览事件采集
//   heatmap: {
//     clickmap: 'not_collect',
//     scroll_notice_map: 'default'
//   }
// })

// 注册公共属性
// sensors.registerPage({
//   current_url: location.href,
//   referrer: document.referrer,
//   platform_type: 'H5',
// })

// sensors.quick('autoTrack'); //用于采集 $pageview 事件。

export default sensors;
