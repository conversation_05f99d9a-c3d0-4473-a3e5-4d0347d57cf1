import sensors from '../init';

const channel = {
  Wechat: '微信好友',
  WechatMoments: '朋友圈',
  SinaWeibo: '新浪微博',
  QQ: 'QQ好友',
  QZone: 'QQ空间',
  Dingding: '钉钉',
  systemShare: '系统分享'
}

const type = {
  '0': '分享链接',
  '1': '分享图片'
}

export const contentShare = ({
  content_type = '基金详情分享', // 内容类型
  content_title = '', // 内容标题
  activity_url = '', // 推广页地址
  share_channel = '' // 分享渠道
} = {}) => {
  setTimeout(() => {
    sensors.track('contentShare', {
      content_type,
      content_title,
      activity_url,
      share_channel: channel[share_channel] || ''
    });
  }, 0)
};

export const $WebPageLeave = ({
  from_pagetype = '',
  share_id = '',
  share_channel = '',
  share_type = '', // 分享类型：图片分享，链接分享
  share_content_title = '', // 分享内容标题
}) => {
  setTimeout(() => {
    sensors.track('$WebPageLeave', {
      from_pagetype,
      share_id: share_id || '',
      share_channel: channel[share_channel] || '',
      share_type: type[share_type] || '',
      share_content_title,
    })
  })
}
