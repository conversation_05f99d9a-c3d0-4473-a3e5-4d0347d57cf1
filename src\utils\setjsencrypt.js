import JSEncrypt from 'jsencrypt'

const crypt = new JSEncrypt()
const key = 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDcExuSlYxazgvdk9aV29GaWh6UGJMWFNZNgpxOGg3bkxicHpUK3BnZUtpMHorU1BzWElZR3dvTHkxUDdrck9peGd5a01xSWdWcGF0SHdZUFduWk5jMnk3MEhTCkhBYW5hSUY5Z1crUlpCTGdZQUc1WXkyemFVNEFXSlg1NnEzUExZd1JTNmZmSmxtNkR5RWFEZngrc3dmVGVibFIKOGJlK3dBKzlYN3dhUHlDUmR3SURBUUFCCi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ=='
const publicKey = window.atob(key)
crypt.setPublicKey(publicKey)
const encodeStr = str => crypt.encrypt(str)
export default encodeStr
