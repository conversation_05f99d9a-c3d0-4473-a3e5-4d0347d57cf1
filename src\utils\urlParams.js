/**
* 过滤url指定字段参数
* @param {url} 链接，默认window.location.href
* @returns {paramKey} 需要过滤的字段参数
*/
export const delParam = ({ url=window.location.href, paramKey=[] }) =>{
    const substrIndex = url.indexOf('?');
    if(substrIndex === -1){
      return url;
    }
  
    const beforeUrl = url.substr(0, substrIndex);   //页面主地址（参数之前地址）
    const urlParam = url.substr(substrIndex+1) || '';   //页面参数
    let nextUrl = "";
  
    const arr = new Array();
    if (urlParam != "") {
        const urlParamArr = urlParam.split("&"); //将参数按照&符分成数组
        for (let i = 0; i < urlParamArr.length; i++) {
            const paramArr = urlParamArr[i].split("="); //将参数键，值拆开
            //如果键雨要删除的不一致，则加入到参数中
            if (!paramKey.includes(paramArr[0])) {
                arr.push(urlParamArr[i]);
            }
        }
    }
    if (arr.length > 0) {
        nextUrl = "?" + arr.join("&");
    }
    url = beforeUrl + nextUrl;
    return url;
  }