/**
 * @param  {string} query 形如 location.search
 * @returns {object}
 */

function getQueryType(key) {
  if (key.endsWith("[]")) return "ARRAY";
  if (key.endsWith("{}")) return "JSON";
  return "DEFAULT";
}

export function urlParse(query) {
  if (!query) {
    return {};
  }
  query = query.replace(/^\?/, "");
  const queryArr = query.split("&");
  const result = {};
  queryArr.forEach((q) => {
    let [key, value] = q.split("=");
    try {
      value = decodeURIComponent(value || "").replace(/\+/g, " ");
      key = decodeURIComponent(key || "").replace(/\+/g, " ");
    } catch (e) {
      // 非法
      console.log(e);
      return;
    }
    const type = getQueryType(key);
    switch (type) {
      case "ARRAY":
        key = key.replace(/\[\]$/, "");
        if (!result[key]) {
          result[key] = [value];
        } else {
          result[key].push(value);
        }
        break;
      case "JSON":
        key = key.replace(/\{\}$/, "");
        value = JSON.parse(value);
        result.json = value;
        break;
      default:
        result[key] = value;
    }
  });
  return result;
}

export function formatCurrency(num) {
  if (!num) {
    return "0.00";
  }
  // eslint-disable-next-line no-useless-escape
  num = num.toString().replace(/\$|\,/g, "");
  if (isNaN(num)) {
    num = "0.00";
  }
  // eslint-disable-next-line eqeqeq
  var sign = num == (num = Math.abs(num));
  num = Math.floor(num * 100 + 0.50000000001);
  // num = Math.floor(num * 100)
  var cents = num % 100;
  num = Math.floor(num / 100).toString();
  if (cents < 10) {
    cents = "0" + cents;
  }
  for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++) {
    num =
      num.substring(0, num.length - (4 * i + 3)) +
      "," +
      num.substring(num.length - (4 * i + 3));
  }
  return (sign ? "" : "-") + num + "." + cents;
}

// function formatCurrency(num) {
//   if (isNaN(num)) {
//     num = '0.00'
//   }
//   let strVal = String(num)
//   if (strVal.indexOf('.') != -1) {
//       return `${Number(
//           String(Number(strVal).toFixed(2)).split('.')[0]
//       ).toLocaleString()}.${String(Number(strVal).toFixed(2)).split('.')[1]}`
//   } else {
//       return `${Number(strVal).toLocaleString()}.00`
//   }
// }

export const getAccountType = (type) => {
  if (
    [
      "SECURITIES_MARGIN",
      "SECURITIES_CASH",
      "SECURITIES_AYERS_CASH",
      "SECURITIES_DAY_TRADE_MARGIN",
    ].includes(type)
  ) {
    return "SECURITIES";
  }
  if (
    [
      "FUTURES_MARGIN",
      "FUTURES_DAYTRADING_MARGIN",
      "STOCK_OPTIONS_CASH",
    ].includes(type)
  ) {
    return "FUTURES";
  }

  if (["BULLION_MARGIN"].includes(type)) {
    return "BULLION";
  }

  if (["MT5", "MF"].includes(type)) {
    return "FOREIGN";
  }

  if (["STOCK_OPTIONS_CASH"].includes(type)) {
    return "OPTIONS";
  }

  if (["INTERACTIVE_BROKER_INTEGRATED_MARGIN"].includes(type)) {
    return "EDDIDPRO";
  }
};

// 防抖函数
export const debounce = function (func, wait, immediate, clear) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = Date.now() - timestamp;

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    // if(clear){
    //   timeout = null
    //   clearTimeout(timeout)
    // }
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };
  return function () {
    context = this;
    args = arguments;
    timestamp = Date.now();
    if (clear) {
      timeout = null;
    }
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    console.log(immediate, clear, timeout, callNow, "callNowcallNow");
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }
    return result;
  };
};

// 拨打客服电话
export const callCSUrl = () => {
  let url = "";
  if (localStorage.getItem("source") === "app") {
    // APP
    url = "tel://+852 3896 6333";
  } else {
    // H5
    url = "tel:+852 3896 6333";
  }
  return url;
};
// 转换金融数字
export const fmoney = (s, n) => {
  if (s == 0) {
    return "0.00";
  }
  // s = parseFloat((s + "").replace(/[^\d.-]/g, "")).toFixed(n) + "";
  if (n != 0) {
    n = (n > 0 && n <= 20 ? n : 2);
    s = parseFloat((s + "").replace(/[^\d.-]/g, "")).toFixed(n) + ""
  } else { // 不做小数位截取操作
    s = s.replace("\"", "")
  }
	// var l = s.split('.')[0].split('').reverse(),
	// 	r = s.split('.')[1];
	let [m, r] = s.split('.');
	const f = m < 0;
	f && (m = m.replace('-', ''));
	let l = m.split('').reverse();
  let t = "";
  for (let i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? "," : "");
  }
  return (f ? '-' : '') + t.split("").reverse().join("") + "." + r;
};

//获取当前日期 t+4 年月日函数
export const getNowFormatDate = () => {
  var date = new Date().getTime() + 24 * 4 * 60 * 60 * 1000;
  date = new Date(parseInt(date)).toLocaleString().replace(/:\d{1,2}$/, " ");
  date = date.split(" ")[0].split("/");

  var seperator1 = "-";
  var year = date[0];
  var month = date[1];
  var strDate = date[2];
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  var currentdate = year + seperator1 + month + seperator1 + strDate;
  return currentdate;
};

// 时间戳转标准日期格式
export const getFormatTimer = (num) => {
  var date = "";
  date = new Date(parseInt(num)).toLocaleString().replace(/:\d{1,2}$/, " ");
  date = date.split(" ")[0].split("/");

  var seperator1 = "-";
  var year = date[0];
  var month = date[1];
  var strDate = date[2];
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  var currentdate = year + seperator1 + month + seperator1 + strDate;
  return currentdate;
};

/**
 * @description 图片转换为base64
 * @example
 * const imgUrl = require("@/assets/images/icon_share.png");
 * const Base64 = new ImgToBase64(imgUrl).imgUrl;
 */
export class ImgToBase64 {
  base64 = "";
  constructor(imgUrl) {
    this.imgUrl = imgUrl;
    this.getImgBase64();
  }

  createBase64DataUrl(img) {
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext("2d");
    ctx.drawImage(img, 0, 0, img.width, img.height);
    const dataURL = canvas.toDataURL("image/png");
    return dataURL;
  }

  getImgBase64() {
    const img = new Image();
    img.src = this.imgUrl;
    const that = this;
    img.onload = function () {
      that.base64 = that.createBase64DataUrl(img);
    };
  }
}

// 版本比较 version1 是否不小于 version2
export const compareVersion = (version1, version2) => {
  if (!version1) return false;
  if (!version2 || version1 === version2) return true;

  const version1Arr = version1.split('.');
  const version2Arr = version2.split('.');
  let maxLen = Math.max(version1Arr.length, version2Arr.length);

  for (let i = 0; i < maxLen; i++) {
    const a = version1Arr[i];
    const b = version2Arr[i];
    if (a !== b) {
      if (a && !b) return true;
      if (!a && b) return false;
      return parseInt(a || 0) > parseInt(b || 0);
    }
  }
  return false;
}

// 辅助函数，用于获取基于 locale 和 managerData 的 URL
function getUrl(managerData, document, locale, locales) {
  const keys = locales[locale];
  return keys.reduce(
    (acc, key) => acc ?? managerData[document]?.[key],
    undefined
  );
}

export const getPdfDocumentUrl = (managerData) => {
  const locales = {
    'zh-hans': ['cn', 'hk', 'us'],
    'zh-hant': ['hk', 'cn', 'us'],
    en: ['us', 'hk', 'cn'],
  };
  const documents = ['prospectusUrl', 'semiAnnualManagerReportUrl', 'annualManagerReportUrl', 'keyFactStatementUrl', 'providerFactSheetTUrl'];
  // 构建 data 对象
  let data = {};
  for (const [locale, keys] of Object.entries(locales)) {
    data[locale] = {};
    for (const document of documents) {
      data[locale][document] = getUrl(
        managerData,
        document,
        locale,
        locales
      );
    }
  }
  console.log('data', data);
  return data
};

// String secretKey = "your_signature_secret";// 为服务端提供的签名密钥
// String path = "/api/v1/currencies"; 
// String timestamp = String.valueOf(System.currentTimeMillis()); 
// String nonce = UUID.randomUUID().toString();
// byte[] requestBody = ...; // 请求体的原始字节
// // 拼接待签名数据，并计算 HMAC-SHA256
// String payload = path + "|" + timestamp + "|" + nonce + "|" +
// Base64.getEncoder().encodeToString(requestBody);
// Mac mac = null; 
// try { 
//  mac = Mac.getInstance("HmacSHA256"); 
//  mac.init(new SecretKeySpec(secretKey.getBytes(), "HmacSHA256")); 
// } catch (NoSuchAlgorithmException | InvalidKeyException e) { 
//  throw new RuntimeException(e); 
// } 
// // Base64 编码,同时 将 signature 填⼊ X-Signature 请求头
// // timestamp 填⼊ Timestamp，nonce 填⼊ X-Nonce
// String signature =
// Base64.getEncoder().encodeToString(mac.doFinal(payload.getBytes()));
export const getSignature = () => {
  //  <path> + "|" + <timestamp> + "|" + <nonce> + "|" + <body_bytes></body_bytes>
  const encoder = new TextEncoder('utf-8');
  let secretKey = "j0+qz7t+5THpAPM4zgkk8TTTt8AC9uMtbz3fnEW+XX0="
  console.log("body_bytes", secretKey);
  // const path = window.location.pathname;
  const path = '/open/account/member/wallet/info';
  // const timestamp = new Date().getTime();
  const timestamp = *************;
  // const nonce = Math.random().toString(36).substring(2);
  const nonce = 'zo4i66ikpdi';
  const payload =  path + "|" + timestamp + "|" + nonce
  // const payload =  path + "|" + timestamp + "|" + nonce + "|" + body_bytes
  const byteArray = encoder.encode(payload);
  const bytes = Array.from(byteArray);
  const signature = CryptoJS.HmacSHA256(bytes, secretKey);
  const base64Signature = CryptoJS.enc.Base64.stringify(signature);
  console.log(base64Signature)
  return {
    timestamp,
    nonce,
    signature: base64Signature
  };
}