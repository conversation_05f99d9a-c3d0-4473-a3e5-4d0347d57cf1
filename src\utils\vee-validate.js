import Vue from 'vue'
import VeeValidate, { Validator } from 'vee-validate' // 引入 vee-validate
import zhCN from 'vee-validate/dist/locale/zh_CN' // 引入本地化语言
import VueI18n from 'vue-i18n'
import zhhansLocale from '@/locales/zh-hans/index';
import zhhantLocale from '@/locales/zh-hant/index';
import enUSLocale from '@/locales/en-us/index';
import store from '@/store/index'
import { formatCurrency } from "@/utils/util";

Vue.use(VueI18n);
// 覆盖默认错误信息提示
zhCN.messages.required = (field) => `${field}`;
// console.log(VeeValidate, 'VeeValidate')
Vue.use(VeeValidate)
// 本地化
Validator.localize('zh_CN', zhCN);
const config = {
  locale: 'zh_CN',
  fieldsBagName: 'fields',
  errorBagName: 'errors'
}

const i18n = new VueI18n({
  locale: sessionStorage.getItem('lang') || store.state.locale || 'zh-hans',
  messages: {
    "zh-hans": zhhansLocale,
    "zh-hant": zhhantLocale,
    "en": enUSLocale,
  }
})

// 自定义校验
const dictionary = {
  zh_CN: {
    messages: {
      required: (field) => {
        i18n.locale = sessionStorage.getItem('lang') || store.state.locale || 'zh-hans';

        if (field === 'riskUsername') {
          return `${i18n.t('risk.placeholderUsername')}`;
        } else if (field === 'riskIDCard') {
          return `${i18n.t('risk.placeholderIDCard')}`;
        } else if (field === 'riskQuestion') {
          return `${i18n.t('risk.choose')}`;
        }

        const filterArr = [
          // i18n.t('mybank.error.bankNamePlaceholder'),
          // i18n.t('deposit.pleaseSelectBank'),
          // i18n.t('deposit.pleaseSelectEddid'),
          // i18n.t('deposit.commit.tradeAccountName'),
          // i18n.t('deposit.pleaseSelectAccount'),
          // i18n.t('deposit.pleaseSelectToAccount'),
          // i18n.t('deposit.pleaseSelectOtherBank'),
          // i18n.t('withdrawal.pleaseSelectAccount'),
          // i18n.t('withdrawal.pleaseSelectbankAccount'),
          // i18n.t('withdrawal.pleaseSelectOtherBank'),
          // i18n.t('exchange.pleaseSelectAccount'),
          // i18n.t('transfer.tradeAccountNumber'),
          // i18n.t('transferinstock.Transferoutbroker'),
          // i18n.t('transferinstock.BrokerNumber'),
          // i18n.t('deposit.remitterBankaccount'),
          // i18n.t('tradingaccount.errorExperience'),
        ]
        const newFilterArr = filterArr.filter(item => field === item);
        if (newFilterArr.length > 0) {
          return `${i18n.t('common.pleaseSelect')}${field}`;
        } else {
          return `${i18n.t('common.pleaseEnter')}${field}`;
        }
      }
    }
  }
}
Validator.localize(dictionary, config)

Validator.extend('money', {
  getMessage: field => field + i18n.t('common.money'),
  validate: value => {
    return value >= 0.01
  }
})
Validator.extend('noMore', {
  getMessage: (field, arg) => {
    if (arg[1] === 'true') {
      return i18n.t('common.noMore')
    } else {
      return i18n.t('common.noMore') + arg[0]
    }
  },
  validate: (value, arg) => {
    return value - arg[0] <= 0
  }
})
Validator.extend('noOverAmout', {
  getMessage: (field, arg) => {
    return i18n.t('common.noOverAmout') + formatCurrency(arg[0]) + arg[1]
  },
  validate: (value, arg) => {
    return value - arg[0] <= 0
  }
})
Validator.extend('noMoreExchange', {
  getMessage: (field, arg) => {
    return i18n.t('common.noMoreExchange') + arg[0]
  },
  validate: (value, arg) => {
    return value - arg[0] <= 0
  }
})
Validator.extend('noMoreTransfer', {
  getMessage: (field, arg) => {
    if (arg[1] === 'true') {
      return i18n.t('common.noMoreTransferMt5')
    } else {
      return i18n.t('common.noMoreTransfer') + arg[0]
    }
  },
  validate: (value, arg) => {
    return value - arg[0] <= 0
  }
})

// 两位小数
Validator.extend('twoPoint', {
  getMessage: field => field + i18n.t('common.twoPoint'),
  validate: value => {
    return /^\d+(\.\d{0,2})?$/.test(value)
  }
})

// 整数不能以0开头
Validator.extend('noZreoFirst', {
  getMessage: field => `${field}${i18n.t('common.noZreoFirst')}`,
  validate: value => {
    return /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/.test(value)
  }
})

// 4位字符
Validator.extend('fourWord', {
  getMessage: field => i18n.t('common.fourWord'),
  validate: value => {
    return value.length >= 4
  }
})

// 不能输入汉字
Validator.extend('noZh', {
  getMessage: (field, arg) => {
    return i18n.t('common.noZh')
  },
  validate: (value, arg) => {
    return !/.*[\u4e00-\u9fa5]+.*$/.test(value)
  }
})

// 整数倍
Validator.extend('intMultiple', {
  getMessage: (field, arg) => {
    return i18n.t('transferinstock.intMultiple')
  },
  validate: (value, arg) => {
    return Number(value) !== 0 && value % arg[0] === 0
  }
})

//数字 字母，及其组合
Validator.extend('chinaId', {
  getMessage: (field, arg) => {
    return i18n.t('deposit.idNot')
  },
  validate: (value, arg) => {
    return !/[^\w]/g.test(value)
  }
})

//数字 字母，括号及其组合 香港身份证
Validator.extend('hkId', {
  getMessage: (field, arg) => {
    return i18n.t('deposit.idNot')
  },
  validate: (value, arg) => {
    return /^[A-Za-z]{1,2}\d{6}[（(][\dA][）)]$/.test(value)
  }
})

//数字 字母，括号及其组合 香港身份证
Validator.extend('amId', {
  getMessage: (field, arg) => {
    return i18n.t('deposit.idNot')
  },
  validate: (value, arg) => {
    return /^[0-9a-zA-Z()（）/]+$/.test(value)
  }
})


//最大最小值限制
Validator.extend('minMax', {
  getMessage: (field, arg) => {
    return i18n.t('transferinstock.notRange')
  },
  validate: (value, arg) => {
    return Number(arg[0]) <= Number(value) && Number(value) <= Number(arg[1])
  }
})

// 正整数
Validator.extend('integer', {
  getMessage: (field, arg) => {
    return i18n.t('common.integer')
  },
  validate: (value, arg) => {
    return /^[+]{0,1}(\d+)$/.test(value)
  }
})
// 名称限制
Validator.extend('nameLimit', {
  getMessage: (field, arg) => {
    return i18n.t('common.pleaseEnterRight') + field
  },
  validate: (val, arg) => {
    // console.log(/^[\u4E00-\u9FA5]{3,}|^[A-z]{4,}/.test(value), 'companyName')
    let value = val.replace(/(^\s+)|(\s+$)/g, "")
    if (arg[2] && arg[2] === 'address') {
      value = value.replace(/[省]|[市]|[区]/g, "")
    }
    let flag = false
    if (/[\u4E00-\u9FA5]/.test(value)) {
      return value.length >= arg[0]
    } else if (/[A-z]/.test(value)) {
      return value.length >= arg[1]
    } else {
      return false
    }
  }
})
