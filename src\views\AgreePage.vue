<template>
  <div class="container-agree">
    <nav-header :title="agreeName" />
    <div v-html="agree" style="padding: 0 30px;"></div>
  </div>
</template>

<script>
import NavHeader from '@/components/NavHeader.vue';
import { agreementH5 } from '@/services/common';

export default {
  name: 'AgreePage',
  components: { NavHeader },
  data() {
    const { name, path } = this.$route.query;
    this.agreePath = path;
    return {
      agreeName: name,
      agree: ''
    }
  },
  beforeMount() {
    document.title = this.agreeName;
    this.getAgreeContent();
  },
  methods: {
    getAgreeContent() {
      const { path } = this.$route.query;
      path && agreementH5(path).then(res => {
        if(res.status !== 200) return;

        this.agree = res.data;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container-agree {
  padding-top: 118px;
  @include font_color(text-color);
}
</style>