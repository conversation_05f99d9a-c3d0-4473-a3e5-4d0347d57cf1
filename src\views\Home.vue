<template>
  <div class="index-page">
    <div class="menu">
      <ul>
        <li @click="$router.push('/login')">登录</li>
        <li @click="$router.push('/risk')">风险测评</li>
        <li @click="$router.push('/result')">结果页</li>
        <!-- <li @click="$router.push('/fundSearch')">基金筛选</li> -->
        <li @click="$router.push('/fundKind')">基金排行</li>
        <li @click="$router.push('/fundCompany')">基金公司</li>
        <li @click="$router.push('/fundAccount')">基金账户</li>
        <li @click="$router.push('/international')">国际版</li>
        <li @click="$router.push('/privateEquityFund')">私募基金</li>
      </ul>
    </div>
    <md-button type="primary" @click="openLink">链接</md-button>
    <md-button @click="onRefresh" style="margin-top: 20px">刷新token</md-button>
    <md-input-item type="text" style="margin-top: 20px" placeholder="请输入XXXXXXXX"></md-input-item>
    <!-- <div class="ed-border test">33333</div> -->
  </div>
</template>

<script>
import { setToken } from "@/utils/auth";

export default {
  name: 'App',
  data() {
    return {
      requestData: [],
    }
  },
  mounted() {},
  methods: {
    openLink(){
      window.open('https://eddid-news-h5-tmp.eddidapp.com')
    },
    onRefresh() {
      this.$jsBridge.run('generateNewToken', {
        callback: body => {
          setToken(body.accessToken);
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.index-page {
  height: 100%;
  @include themeify {
    background: themed('background-color');
    color: themed('text-color');

    .menu {
      padding: 30px;

      li {
        width: 50%;
        float: left;
        height: 100px;
        line-height: 60px;
        color: $primary-color;
      }
    }
  }
}
li, ol, ul {
  list-style: none;
}
.test {
  width: 300px;
  height: 50px;
  margin: 30px;
}
</style>
