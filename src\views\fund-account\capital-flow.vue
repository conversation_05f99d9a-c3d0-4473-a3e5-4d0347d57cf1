<template>
  <div class="index-page">
    <div class="fixed">
      <div class="income-card">
        <img
          class="show-pic-left"
          src="../../assets/images/img_securities.png"
          alt="pic"
        />
        <span class="padding-20">{{
          $t("myAccount.transaction") + "(" + parameter.currency + ")"
        }}</span>
        <img
          class="show-pic-right"
          :class="[currencyShow ? 'show' : '']"
          :src="require(`@/assets/images/${theme}/com_ico_arrow.png`)"
          alt="pic"
          @click="currencyShow = !currencyShow"
        />
      </div>
      <div v-if="currencyShow" class="currencyPopup">
        <div
          v-for="(item, index) in currencyList"
          :key="index"
          @click="currencyChoose(item.key)"
          :class="[parameter.currency === item.key ? 'active' : 'currencyItem']"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="select-card">
        <span @click="timeClick"
          >{{ datePickerValue ? datePickerValue : $t("myAccount.allDates")
          }}<img
            class="show-pic-left"
            :class="[isPopupShow ? 'show' : '']"
            :src="require(`@/assets/images/${theme}/com_ico_arrow.png`)"
            alt="pic"
        /></span>
        <span class="reset" @click="resetClick" v-if="datePickerValue">{{
          $t("common.btns.reset")
        }}</span>
      </div>
    </div>
    <div class="list-Item">
      <div v-for="(item, index) in accountdata" :key="index">
        <div class="time-flow-card">
          <div class="date">{{ moment(item.month).format("yyyy-MM") }}</div>
          <div class="text">
            <span>{{ $t("myAccount.intoFlow") }}：</span>
            <span class="padding-50">{{
              fmoney(Number(item.inflow), 2) + parameter.currency
            }}</span>
            <span>{{ $t("myAccount.outFlow") }}：</span>
            <span>{{
              fmoney(Number(item.outflow), 2) + parameter.currency
            }}</span>
          </div>
        </div>
        <div
          class="card-text"
          v-for="(item2, index2) in item.flows"
          :key="index2"
        >
          <div class="title-num">
            <span class="title-left">{{ fundsNameFun(item2.typeDesc) }}</span>
            <span class="num-right"
              >{{ item2.direction === "INFLOW" ? "+" : "-"
              }}{{ fmoney(Number(item2.amount), 2) }}</span
            >
          </div>
          <div class="describe">
            {{
              item2.typeDesc.cn === "入金" || item2.typeDesc.cn === "出金"
                ? "-"
                : ""
            }}{{ fundsNameFun(item2.description) }}
          </div>
          <div class="date">
            {{ moment(item2.transTime).format("MM-DD HH:mm") }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!accountdata.length" class="fund-product-null">
      <img
        :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
        alt="pic"
      />
    </div>
    <md-popup v-model="isPopupShow" position="bottom">
      <div class="timeCheck">
        <span class="timeCheck-left" @click="isPopupShow = false">{{
          $t("common.btns.cancel")
        }}</span>
        <span class="timeCheck-right" @click="hidePopUp()">{{
          $t("common.btns.affirm")
        }}</span>
      </div>
      <div>
        <md-date-picker
          ref="datePicker"
          type="custom"
          :min-date="minDate"
          :max-date="maxDate"
          :textRender='textRender'
          :custom-types="['yyyy', 'MM']"
          :default-date="currentDate"
          is-view
          keep-index
        ></md-date-picker>
      </div>
    </md-popup>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { capitalFlowsList } from "@/services/account";
import { fmoney } from "@/utils/util";
import moment from "moment";
import { Popup, PopupTitleBar, Button, DatePicker, Toast } from "mand-mobile";
import { getAccountNumber } from "@/services/account";
export default {
  name: "fundDetails",
  components: {
    [Popup.name]: Popup,
    [PopupTitleBar.name]: PopupTitleBar,
    [Button.name]: Button,
    [DatePicker.name]: DatePicker,
  },
  data() {
    return {
      inflow: 0,
      flowOut: 0,
      isPopupShow: false,
      currencyShow: false,
      datePickerValue: "",
      minDate: new Date(new Date().getTime() - 24 * 365 * 60 * 60 * 1000 * 2),
      maxDate: new Date(),
      currentDate: "",
      currencyType: "myAccount.financialAccount",
      currencyList: [
        { text: this.$t("fundMessage.currencyType.HKD"), key: "HKD" },
        { text: this.$t("fundMessage.currencyType.USD"), key: "USD" },
      ],
      accountdata: [],
      parameter: {
        currency: "HKD",
      },
      monthMap: {
        1: 'January',
        2: 'February',
        3: 'March',
        4: 'April',
        5: 'May',
        6: 'June',
        7: 'July',
        8: 'August',
        9: 'September',
        10: 'October',
        11: 'November',
        12: 'December',
      },
    };
  },
  created() {
    getAccountNumber(this, this.$jsBridge.isSupported("getAppInfo")).then(
      (res) => {
        let object = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        this.capitalFlow();
      }
    );
    this.currentDate = new Date();
  },
  computed: {
    ...mapState(["theme", "accountNumber", "locale"]),
  },
  methods: {
    moment,
    fmoney,
    timeClick() {
      this.isPopupShow = true;
      this.currentDate = new Date();
    },
    currencyChoose(str) {
      this.parameter.currency = str;
      this.currencyShow = false;
      this.capitalFlow();
    },
    hidePopUp() {
      this.isPopupShow = false;
      this.datePickerValue = this.$refs.datePicker.getFormatDate("yyyy年MM月");
      const year = this.$refs.datePicker.getFormatDate("yyyy");
      const month = this.$refs.datePicker.getFormatDate("MM");
      const day = this.getDaysInMonth(year, month);
      this.parameter.transDateRangeStart = year + "-" + month + "-" + "01";
      this.parameter.transDateRangeEnd = year + "-" + month + "-" + day;
      this.capitalFlow();
    },
    getDaysInMonth(year, month) {
      month = parseInt(month, 10); //parseInt(number,type)这个函数后面如果不跟第2个参数来表示进制的话，默认是10进制。
      var temp = new Date(year, month, 0);
      return temp.getDate();
    },
    resetClick() {
      delete this.parameter.transDateRangeStart;
      delete this.parameter.transDateRangeEnd;
      this.datePickerValue = "";
      this.capitalFlow();
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data && data.cn ? data.cn : "-";
      } else if (this.locale == "zh-hant") {
        return data && data.hk ? data.hk : "-";
      } else {
        return data && data.us ? data.us : "-";
      }
    },
    async capitalFlow() {
      this.accountdata = [];
      console.log("this.accountNumber", this.accountNumber);
      const res = await capitalFlowsList(
        this.accountNumber,
        this.parameter,
        this
      );
      if (res.code === "200") {
        if (!res.data.monthlyFlows.length) {
          return Toast.info(this.$t("fundMessage.notMessage"));
        } else {
          this.accountdata = res.data.monthlyFlows;
        }
        this.inflow =
          this.fmoney(Number(res.data.inflow), 2) + this.parameter.currency;
        this.flowOut =
          this.fmoney(Number(res.data.outflow), 2) + this.parameter.currency;
      }
    },
    textRender() {
      // eslint-disable-next-line prefer-rest-params
      const args = Array.prototype.slice.call(arguments);
      const typeFormat = args[0]; // 类型
      const column0Value = args[1]; // 第1列选中值
      const column1Value = args[2]; // 第2列选中值
      const column2Value = args[3]; // 第3列选中值
      if (typeFormat === 'yyyy') {
        return `${column0Value}${this.$t('common.year')}`;
      }
      if (typeFormat === 'MM') {
        if (this.locale === 'en') {
          return `${this.monthMap[column1Value]}`;
        }
        return `${column1Value}${this.$t('common.month')}`;
      }
      if (typeFormat === 'dd') {
        return `${column2Value}${this.$t('common.day')}`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: Medium;
    .return {
      width: 16px;
      height: 30px;
      display: inline-block;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .income-card {
    height: 70px;
    line-height: 28px;
    width: calc(100% - 60px);
    margin: 0 30px;
    padding: 25px 0 17px;
    position: relative;
    text-align: left;
    font-size: 28px;
    .padding-20 {
      padding-left: 40px;
    }
    .show-pic-left {
      width: 28px;
      height: 28px;
      position: absolute;
      top: 50%;
      margin-top: -10px;
      left: 0px;
    }
    .show-pic-right {
      width: 18px;
      height: 10px;
      position: absolute;
      top: 50%;
      margin-top: -5px;
      right: 0px;
    }
    .show {
      transform: rotate(180deg);
    }
  }
  .select-card {
    height: 108px;
    line-height: 28px;
    width: calc(100% - 60px);
    margin: 0 30px;
    padding: 40px 0;
    font-size: 28px;
    position: relative;
    display: flex;
    justify-content: space-between;
    .show-pic-left {
      width: 18px;
      height: 10px;
      margin: 0 0 4px 20px;
    }
    .show {
      transform: rotate(180deg);
    }
    .reset {
      display: inline-block;
      width: 120px;
      height: 50px;
      border-radius: 25px;
      border: 1px solid;
      @include border_color(line-list);
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      line-height: 48px;
      text-align: center;
    }
  }
  .time-flow-card {
    @include themeify {
      background: themed("list-bg");
    }
    width: 100%;
    padding: 24px 30px;
    .date {
      height: 32px;
      line-height: 32px;
      text-align: left;
      font-size: 32px;
      margin-bottom: 20px;
    }
    .text {
      height: 24px;
      line-height: 24px;
      text-align: left;
      font-size: 24px;
      .padding-50 {
        padding-right: 50px;
      }
    }
  }
  .card-text {
    @include themeify {
      border-bottom-color: themed("line-color");
    }
    width: calc(100% - 60px);
    margin: 0px 30px;
    border-bottom: 1px solid;
    .title-num {
      padding-top: 35px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;

      .title-left {
        text-align: left;
        display: inline-block;
        height: 28px;
        line-height: 28px;
        font-size: 28px;
        font-weight: 400;
      }
      .num-right {
        font-weight: 500;
        font-family: DINPro-Medium, DINPro;
        text-align: right;
        display: inline-block;
        height: 32px;
        font-size: 32px;
        line-height: 32px;
      }
    }
    .describe {
      @include themeify {
        color: themed("second-text");
      }
      height: 24px;
      line-height: 24px;
      text-align: left;
      font-size: 24px;
      margin-bottom: 20px;
    }
    .date {
      @include themeify {
        color: themed("second-text");
      }
      height: 24px;
      line-height: 24px;
      font-size: 24px;
      text-align: left;
      margin-bottom: 35px;
    }
  }
  .timeCheck {
    @include themeify {
      background-color: themed("bg-color");
    }
    display: flex;
    justify-content: space-between;
    padding: 50px 30px;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    .timeCheck-left {
      @include themeify {
        color: themed("second-text");
      }
    }
    .timeCheck-right {
      @include themeify {
        color: themed("text-color");
      }
    }
  }
  .currencyPopup {
    width: 30%;
    position: absolute;
    width: 204px;
    height: 196px;
    top: 70px;
    right: 0;
    z-index: 99;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    box-shadow: 0px 2px 10px 0px #ecf1f8;
    box-sizing: border-box;
    @include themeify {
      background-color: themed("bg-color");
    }
    .currencyItem {
      text-align: center;
    }
    .active {
      text-align: center;
      @include themeify {
        color: themed("primary-color");
      }
      margin: 48px 0 40px;
    }
  }
  ::v-deep .datePicker {
    @include themeify {
      background-color: themed("bg-color");
      color: themed("text-color");
    }
    .md-picker {
      @include themeify {
        background-color: themed("bg-color");
        color: themed("text-color");
      }
      .md-picker-column {
        @include themeify {
          background-color: themed("bg-color");
          color: themed("second-text");
        }
      }
    }
  }
  .fund-product-null {
    text-align: center;
    margin-top: 137px;
    img {
      width: 347px;
      height: 200px;
    }
  }
  .fixed {
    position: fixed;
    top: 0;
    width: 100%;
    @include themeify {
      background-color: themed("bg-color");
    }
  }
  .list-Item {
    padding-top: 178px;
  }
}
</style>
