<template>
  <div class="index-page">
    <!-- <div class="page-title">
      <img class="return" @click="goBack" src="../../assets/images/icon_back_new.png" alt="pic">
      <span>{{$t("myAccount.incomeDetails")}}</span>
    </div> -->
    <div v-show="isScrollSwitch" class="income-card">
      <p class="text-line1" @click="currencyShow = !currencyShow">
        <!-- <span class="padding-20">{{$t("myAccount.cumulativeIncome")}}</span> -->
        <span class="padding-20">{{
          $t("myAccount.transaction") + "(" + parameter.currency + ")"
        }}</span>
        <img
          class="show-pic"
          :class="[currencyShow ? 'show' : '']"
          :src="require(`@/assets/images/${theme}/com_ico_arrow.png`)"
          alt="pic"
        />
      </p>
      <p
        class="text-line2"
        :class="
          data.isPositive === 2
            ? 'positive'
            : data.isPositive === 0
            ? 'negative'
            : 'second-text'
        "
      >
        {{ data.totalReturn }}
      </p>
    </div>
    <div class="income-card-programme1">
      <p class="text-line1" @click="currencyShow = !currencyShow">
        <!-- <span class="padding-20">{{$t("myAccount.cumulativeIncome")}}</span> -->
        <span class="padding-20">{{
          $t("myAccount.transaction") + "(" + parameter.currency + ")"
        }}</span>
        <img
          class="show-pic"
          :class="[currencyShow ? 'show' : '']"
          :src="require(`@/assets/images/${theme}/com_ico_arrow.png`)"
          alt="pic"
        />
      </p>
      <!-- :class="data.isPositive === 2 ? 'positive' : data.isPositive === 0 ? 'negative' : 'second-text'" -->
      <p
        class="text-line2"
        :class="
          klineTheme === 'redRiseGreenFall'
            ? data.isPositive === 2
              ? 'positive'
              : data.isPositive === 0
              ? 'negative'
              : 'second-text'
            : data.isPositive === 2
            ? 'negative'
            : data.isPositive === 0
            ? 'positive'
            : 'second-text'
        "
      >
        {{ data.totalReturn }}
      </p>
    </div>
    <div
      v-if="currencyShow"
      :class="isScrollSwitch ? 'currencyPopup fixed' : 'currencyPopup right424'"
    >
      <div
        v-for="(item, index) in currencyList"
        :key="index"
        @click="currencyChoose(item.key)"
        :class="[parameter.currency === item.key ? 'active' : 'currencyItem']"
      >
        {{ item.text }}
      </div>
    </div>
    <div v-show="currencyShow" class="dialog-mask" @click="closeDialog"></div>

    <div v-show="isScrollSwitch" class="margin-90"></div>
    <template
      v-if="data.dailyRevenues && data.dailyRevenues.length && accountStatus"
    >
      <div
        class="card-text"
        v-for="(item, index) in data.dailyRevenues"
        :key="index"
      >
        <span class="text-left">{{ item.revenueDate }}</span>
        <!-- :class="item.isPositive === 2 ? 'positive' : (item.isPositive === 0 ? 'negative' : 'second-text')" -->
        <span
          v-if="item.tradeDate"
          :class="
            klineTheme === 'redRiseGreenFall'
              ? item.isPositive === 2
                ? 'positive'
                : item.isPositive === 0
                ? 'negative'
                : 'second-text'
              : item.isPositive === 2
              ? 'negative'
              : item.isPositive === 0
              ? 'positive'
              : 'second-text'
          "
          >{{ item.gains }}</span
        >
      </div>
    </template>
    <template v-else>
      <div class="fund-account-empty">
        <img
          :src="require('@/assets/images/fund_company_' + theme + '.png')"
          alt=""
        />
        <p>{{ $t("fundMessage.notMessage") }}</p>
      </div>
    </template>
    <loading v-if="isLoading"></loading>
  </div>
</template>


<script>
import { mapState } from "vuex";
import { fmoney } from "@/utils/util.js";
import { getRevenuesNum, getAccountNumber } from "@/services/account.js";
import { Toast } from "mand-mobile";
import loading from "@/components/loading/index";

export default {
  name: "incomeDetails",
  data() {
    return {
      isScrollSwitch: false,
      parameter: {
        currency: "HKD",
      },
      currencyShow: false,
      data: {},
      currencyList: [
        { text: this.$t('myAccount.HKDollar'), key: "HKD" },
        { text: this.$t('myAccount.dollar'), key: "USD" },
      ],
      isLoading: true,
      size: 15,
      isGetData: true,
      accountStatus: false,
    };
  },
  computed: {
    ...mapState(["accountNumber", "theme", "klineTheme"]),
  },
  components: { loading },
  created() {
    this.isLoading = true;
    getAccountNumber(this).then((res) => {
      console.log(res);
      let object = res.data.tradingAccountList.find((item) => {
        return item.type === "FUND";
      });
      console.log("==========", object);
      if (object) {
        this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        this.getdata();
      } else {
        this.accountStatus = false;
      }
    });
  },
  mounted() {
    window.addEventListener("scroll", this.dataScrollIncome, true);
  },
  destroyed() {
    if (document.body) {
      document.body.scrollTop = 0;
    }
    if (document.documentElement) {
      document.documentElement.scrollTop = 0;
    }
    window.removeEventListener("scroll", this.dataScrollIncome, true);
  },
  methods: {
    closeDialog() {
      this.currencyShow = false;
    },
    currencyChoose(str) {
      this.parameter.currency = str;
      this.size = 15;
      this.getdata();
      this.currencyShow = false;
      // this.capitalFlow()
    },
    dataScrollIncome() {
      let ScrollHeight = this.getScrollHeight();
      let DocumentTop = this.getDocumentTop();
      let WindowHeight = this.getWindowHeight();
      if (ScrollHeight == DocumentTop + WindowHeight && !this.isGetData) {
        this.isGetData = true;
        this.isLoading = true;
        this.size = this.size + 15;
        this.getdata();
      }
      if (DocumentTop > 98 && !this.isScrollSwitch) {
        this.isScrollSwitch = true;
      } else if (DocumentTop <= 98 && this.isScrollSwitch) {
        this.isScrollSwitch = false;
      }
    },
    //文档高度
    getDocumentTop() {
      var scrollTop = 0,
        bodyScrollTop = 0,
        documentScrollTop = 0;
      if (document.body) {
        bodyScrollTop = document.body.scrollTop;
      }
      if (document.documentElement) {
        documentScrollTop = document.documentElement.scrollTop;
      }
      scrollTop =
        bodyScrollTop - documentScrollTop > 0
          ? bodyScrollTop
          : documentScrollTop;
      return scrollTop;
    },
    //可视窗口高度
    getWindowHeight() {
      var windowHeight = 0;
      if (document.compatMode == "CSS1Compat") {
        windowHeight = document.documentElement.clientHeight;
      } else {
        windowHeight = document.body.clientHeight;
      }
      return windowHeight;
    },

    //滚动条滚动高度
    getScrollHeight() {
      var scrollHeight = 0,
        bodyScrollHeight = 0,
        documentScrollHeight = 0;
      if (document.body) {
        bodyScrollHeight = document.body.scrollHeight;
      }
      if (document.documentElement) {
        documentScrollHeight = document.documentElement.scrollHeight;
      }
      scrollHeight =
        bodyScrollHeight - documentScrollHeight > 0
          ? bodyScrollHeight
          : documentScrollHeight;
      return scrollHeight;
    },

    goBack() {
      this.$router.go(-1);
    },
    getdata() {
      let params = {
        size: this.size,
        currency: this.parameter.currency,
      };
      getRevenuesNum(this.accountNumber, params, this).then((res) => {
        if (res.code == "200") {
          this.accountStatus = true;
          this.data = res.data;
          this.processingData();
        }
      });
    },
    processingData() {
      this.data.totalReturn = Number(this.data.totalReturn);
      if (this.data.totalReturn > 0) {
        this.data.isPositive = 2;
        this.data.totalReturn = "+" + fmoney(this.data.totalReturn, 2);
      } else if (Number(this.data.totalReturn) === 0) {
        this.data.isPositive = 1;
        this.data.totalReturn = fmoney(this.data.totalReturn, 2);
      } else {
        this.data.isPositive = 0;
        this.data.totalReturn =
          "-" + fmoney(Math.abs(this.data.totalReturn), 2);
      }

      this.data.dailyRevenues.forEach((item, index) => {
        if (!item.tradeDate) {
          this.data.dailyRevenues[index].revenueDate =
            this.data.dailyRevenues[index].revenueDate +
            this.$t("myAccount.nonTradingDay");
          return;
        }
        item.gains = Number(item.gains);
        if (item.gains > 0) {
          this.data.dailyRevenues[index].isPositive = 2;
          this.data.dailyRevenues[index].gains = "+" + fmoney(item.gains, 2);
        } else if (item.gains === 0) {
          this.data.dailyRevenues[index].isPositive = 1;
          this.data.dailyRevenues[index].gains = fmoney(item.gains, 2);
        } else {
          this.data.dailyRevenues[index].isPositive = 0;
          this.data.dailyRevenues[index].num = "-" + fmoney(-item.num, 2);
        }
      });
      this.isLoading = false;
      this.isGetData = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  .dialog-mask {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    z-index: 2800;
  }
  position: relative;
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  .currencyPopup {
    position: absolute;
    width: 204px;
    height: 196px;
    top: 0px;
    left: 30px;
    z-index: 2999;
    font-size: 28px;
    border-radius: 4px;
    @include themeify {
      background-color: themed("bg-color");
    }
    @include box_shadow(shadow-color);
    .currencyItem {
      text-align: center;
      margin: 48px 0 40px;
    }

    .active {
      text-align: center;
      margin: 48px 0 40px;
      @include themeify {
        color: themed("primary-color");
      }
    }
  }
  .fixed {
    position: fixed;
    top: 90px;
    left: 30px;
  }
  .right424 {
    top: 100px;
    left: 424px;
  }
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: Medium;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .income-card {
    // padding: 44px 0px;
    @include themeify {
      background: themed("list-bg");
      color: themed("text-color");
    }
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0px 30px;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 1000;
    .text-line1 {
      line-height: 90px;
      text-align: left;
      font-size: 24px;
      position: relative;
      .show-pic {
        width: 18px;
        height: 10px;
        vertical-align: middle;
        margin-left: 12px;
        margin-bottom: 5px;
      }
      .show {
        transform: rotate(180deg);
      }
    }
    .text-line2 {
      line-height: 90px;
      font-size: 42px;
      text-align: right;
    }
    // .positive {
    //   @include themeify {
    //     color: themed("k-line-red");
    //   }
    // }
    // .negative {
    //   @include themeify {
    //     color: themed("k-line-green");
    //   }
    // }
  }
  .income-card-programme1 {
    @include themeify {
      color: themed("text-color");
    }
    width: 100%;
    padding: 40px 30px 50px 30px;
    z-index: 1000;
    .text-line1 {
      line-height: 44px;
      text-align: center;
      font-size: 24px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      .show-pic {
        margin-left: 20px;
        width: 18px;
        height: 10px;
        // position: absolute;
        // top: 50%;
        // margin-top: -5px;
        // right: 196px;
      }
      .show {
        transform: rotate(180deg);
      }
    }
    .text-line2 {
      line-height: 62px;
      font-size: 42px;
      text-align: center;
    }
    .positive {
      @include themeify {
        color: themed("k-line-red");
      }
    }
    .negative {
      @include themeify {
        color: themed("k-line-green");
      }
    }
    .second-text {
      @include font_color(second-text);
    }
  }
  .margin-90 {
    margin-top: 90px;
  }
  .card-text {
    @include themeify {
      border-bottom-color: themed("line-color");
    }
    height: 100px;
    line-height: 100px;
    width: calc(100% - 60px);
    margin: 0px 30px;
    border-bottom: 1px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28px;
    .positive {
      @include themeify {
        color: themed("k-line-red");
      }
    }
    .negative {
      @include themeify {
        color: themed("k-line-green");
      }
    }
    .second-text {
      @include font_color(second-text);
    }
  }
  .fund-account-empty {
    width: 334px;
    height: 197px;
    text-align: center;
    margin: 30px auto 0;
    img {
      width: 100%;
    }
    p {
      @include font_color(second-text);
      height: 28px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #868e9e;
      line-height: 28px;
    }
  }
}
</style>
