<template>
  <div class="subscribe-redeem-state">
    <div class="subscribe-redeem-state-text">
      <span>{{ $t("myAccount." + orderData.orderType) }}</span>
      <span v-if="orderData.orderType == 'Sell'">{{ $t("myAccount.shareSingle") }}</span>
      <span v-else>{{ format(orderData.currency) }}</span>
      <span v-show="isCancellButtonShow" @click="cancelOrder" class="cancel-order">{{ $t("myAccount.CANCELL") }}</span>
    </div>
    <div v-if="orderData.orderType == 'Sell'" class="account-num">{{ orderData.quantity }}</div>
    <div v-else class="account-num">{{ orderData.amount }}</div>
    <div v-if="orderData.orderType != 'Dividend'" class="order-status">{{ $t("myAccount." + orderData.status) }}</div>
    <div v-else class="order-status-line"></div>
    <div class="order-state-content">
      <div v-if="orderData.orderType != 'Dividend'" class="order-type">
        <img v-if="orderData.orderType == 'Sell'" class="sell" :src="require(`../../assets/images/fund_submit_estimate_sell_${theme}.png`)" alt="pic"/>
        <img v-else class="buy" :src="require(`../../assets/images/fund_submit_estimate_${theme}.png`)" alt="pic"/>
        <!-- <img class="buy" :src="require(`../../assets/images/fund_submit_estimate_${theme}.png`)" alt="pic"/> -->
        <div class="order-message order-message1">
          <!-- <p>{{ $t("myAccount.orderSubmitted") }}</p>
          <p>{{
              orderData.orderType == "Sell"
                ? $t("myAccount.expectedT") +
                  tradeRulesData.redemptionPlusDay +
                  $t("myAccount.redemptionDescriptionlabelEnd")
                : $t("myAccount.expectedT") +
                  tradeRulesData.confirmationPlusDay +
                  $t("myAccount.orderDetailBuy")
            }}
          </p>
          <p v-if="orderData.orderType == 'Buy'">
            {{ $t("myAccount.expectedT") }}{{ tradeRulesData.moneyConfirmedPlusDay }}{{ $t("myAccount.checkYield") }}
          </p> -->
          <div>
            <p>{{ $t("myAccount.orderSubmitted") }}</p>
            <p>{{ orderData.orderTime && moment(orderData.orderTime).format("YYYY-MM-DD HH:mm") }}</p>
          </div>
          <div v-if="orderData.orderType == 'Buy'">
            <p>{{ $t("myAccount.ConfirmShare") }}</p>
            <!-- $t("fundMessage.TDayBeforeTen", { time: endOfTradingDay }) -->
            <p>
              {{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.shareConfirmedDay && moment(tradeRulesData.shareConfirmedDay).endOf('day').format("YYYY-MM-DD HH:mm") }) }}
              {{ $t("myAccount.ConfirmedWithNAV", {time: tradeRulesData.navConfirmedDay && moment(tradeRulesData.navConfirmedDay).format("YYYY-MM-DD")}) }}
            </p>
          </div>
          <!--  v-if="orderData.orderType == 'Buy'" -->
          <div>
            <p>{{ orderData.orderType == "Sell" ? $t("myAccount.FundPosted") : $t("myAccount.StartViewingEarning") }}</p>
            <p>
              {{ 
                $t("myAccount.ExpectedBefore", {time: (orderData.orderType == "Sell" ?
                tradeRulesData.redemptionArriveDay && moment(tradeRulesData.redemptionArriveDay).endOf('day').format("YYYY-MM-DD HH:mm") :
                tradeRulesData.profitArriveDay && moment(tradeRulesData.profitArriveDay).endOf('day').format("YYYY-MM-DD HH:mm"))
                })
              }}
            </p>
          </div>
        </div>
      </div>
      <div v-else :class="orderData.orderType != 'Dividend' ? 'order-type' : 'order-type order-type-line'"></div>
      <ul class="order-state-ul">
        <li>
          <label>{{ $t("myAccount.productName") }}</label>
          <div>{{ fundsNameFun(orderData) }}</div>
        </li>
        <li>
          <label>{{
            orderData.orderType == "Buy"
              ? $t("myAccount.paymentAccount")
              : orderData.orderType == "Sell"
              ? $t("myAccount.redeemTo")
              : $t("myAccount.shareOutBonus")
          }}</label>
          <div>{{ $t("myAccount.paymentAccountContent") }}</div>
        </li>
        <li>
          <label>{{ orderData.orderType == "Buy" ? $t("myAccount.subscriptionAmount") : $t("myAccount.redemptionShare")}}</label>
          <div>{{ orderData.orderType === "Sell" ? fmoney(orderData.quantity, 4) : orderData.currency + " " + orderData.amount }}</div>
        </li>
      </ul>
    </div>
    <div class="dialog-main" v-show="isDialogShow">
      <div class="dialog">
        <div class="title">
          <span class="text">{{ $t("myAccount.popUpTitle") }}</span>
        </div>
        <div class="password">
          <input
            id="password"
            v-model="passwordListNew"
            @input="setFontColor"
            class="input-box"
            type="password"
            :placeholder="$t('myAccount.passwordPrompt')"
          />
          <div class="eye-div" id="openEye" @click="openEye" style="display: none">
            <img src="@/assets/images/transaction/open_eyes.png" class="eye-passord" alt="pic"/>
          </div>
          <div class="eye-div" id="closeEye" @click="closeEye">
            <img src="@/assets/images/transaction/close_eyes.png" class="eye-passord" alt="pic"/>
          </div>
        </div>
        <div class="password-prompt">
          <span v-show="isPasswordError" class="password-err">{{ $t("myAccount.passwordMistake") }}</span>
          <span class="password-forget" @click="submitResetPassword">{{ $t("myAccount.forgotPassword") }}</span>
        </div>
        <div class="bounced-operation">
          <div class="cancel" @click="closeDialog('center')">{{ $t("common.btns.cancel") }}</div>
          <div v-show="!passwordLegal" class="confim-disabled">{{ $t("myAccount.determine") }}</div>
          <div v-show="passwordLegal" class="confim" @click="submitPassword">{{ $t("myAccount.determine") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { fmoney } from "@/utils/util.js";
import moment from "moment";
import { getFundInformation } from "@/services/account.js";
import { getOrderData, cancelTheOrder } from "@/services/account.js";
import { Toast } from "mand-mobile";
import { tradeRules } from "@/services/fund";
export default {
  computed: {
    ...mapState(["theme", "locale", "accountNumber"]),
  },
  data() {
    return {
      fmoney,
      isCancellButtonShow: false,
      passwordLegal: false,
      isPasswordError: false,
      orderData: {},
      orderDescribe: "default",
      isDialogShow: false,
      passwordListNew: "",
      tradeRulesData: Object
    };
  },

  created() {
    this.getdata();
    // 判断订单类型   申购赎回订单  查询交易规则
    // if()
  },
  methods: {
    moment,
    submitResetPassword() {
      this.$jsBridge.run("resetPassword", {
        accountType: "funding",
        account: this.accountNumber,
      });
    },
    format(type) {
      if (type == "HKD") {
        return "(" + this.$t("fundMessage.currencyType.HKD") + ")";
      } else if (type == "USD") {
        return "(" + this.$t("fundMessage.currencyType.USD") + ")";
      }
    },
    closeDialog(e) {
      this.passwordListNew = "";
      this.isDialogShow = false;
      this.isPasswordError = false;
    },
    submitPassword() {
      this.$jsBridge.run("startLogin", {
        type: "D",
        password: this.passwordListNew,
        callback: ({ login_state, account_type }) => {
          if (login_state) {
            this.cancelOrderSubmit();
            this.isPasswordError = false;
            this.isDialogShow = false;
            Toast.info(this.$t("myAccount.itsSuccess"));
          } else {
            this.isPasswordError = true;
          }
        },
      });
    },
    setFontColor() {
      this.isPasswordError = false;
      if (
        this.passwordListNew.length >= 6 &&
        this.passwordListNew.length < 16
      ) {
        this.passwordLegal = true;
      } else {
        this.passwordLegal = false;
      }
      if (
        this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "light"
      ) {
        document.getElementById("password").style.color = "#121C32";
      } else if (
        this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "dark"
      ) {
        document.getElementById("password").style.color = "#E5E5E5";
      } else if (
        !this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "light"
      ) {
        document.getElementById("password").style.color = "#BDC0D0";
      } else {
        document.getElementById("password").style.color = "#5F616A";
      }
    },
    openEye() {
      document.getElementById("password").type = "password";
      document.getElementById("openEye").style.display = "none";
      document.getElementById("closeEye").style.display = "block";
    },
    closeEye() {
      document.getElementById("password").type = "text";
      document.getElementById("openEye").style.display = "block";
      document.getElementById("closeEye").style.display = "none";
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data.productName && data.productName.cn
          ? data.productName.cn
          : "-";
      } else if (this.locale == "zh-hant") {
        return data.productName && data.productName.hk
          ? data.productName.hk
          : "-";
      } else {
        return data.productName && data.productName.us
          ? data.productName.us
          : "-";
      }
    },
    cancelOrder() {
      // this.getdata();
      // if()
      // 判断当前时间是否可以撤销  时间戳判断
      this.isDialogShow = true;
    },
    cancelOrderSubmit() {
      let timestamp = Date.parse(new Date());
      let cancellTimer = new Date(this.orderData.cancellationDeadline);
      cancellTimer = cancellTimer.getTime();
      if (timestamp >= cancellTimer) {
        Toast.info(this.$t("myAccount.orderInProcess"));
        this.getdata();
        return;
      }
      cancelTheOrder(this.$route.query.id).then((res) => {
        if (res.code === "200") {
          this.getdata();
        }
      });
    },
    getdata() {
      let timestamp = Date.parse(new Date());
      getOrderData(this.$route.query.id).then((res) => {
        this.orderData = res.data;
        if (this.orderData.orderTime) {
          this.orderData.orderTime = moment(this.orderData.orderTime).format("YYYY-MM-DD HH:mm:ss");
        }
        let cancellTimer = new Date(this.orderData.cancellationDeadline);
        cancellTimer = cancellTimer.getTime();
        if (this.orderData.status === "SUBMITTED" && timestamp < cancellTimer) {
          this.isCancellButtonShow = true;
        } else {
          this.isCancellButtonShow = false;
        }
        if (this.orderData.orderType === "Buy") {
          this.orderData.amount = fmoney(Number(this.orderData.amount), 2);
          this.gettradeRules(this.orderData.fundIsin, this.orderData.orderTime);
        } else if (this.orderData.orderType === "Sell") {
          this.orderData.quantity = fmoney(Number(this.orderData.quantity), 4);
          this.gettradeRules(this.orderData.fundIsin, this.orderData.orderTime);
        }
      });
    },
    gettradeRules(fundIsin, orderTime) {
      let actualTime = orderTime && (moment(orderTime).format('YYYY-MM-DD') + 'T' + moment(orderTime).format('HH:mm:ss'));
      tradeRules(fundIsin, actualTime).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
        }
      });
    },
    getFundInformationRules() {
      getFundInformation(this.$route.query.fundIsin).then((res) => {
        this.rulesDatas = res.data;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    expectedBefore(time, num) {
      /**
       * time: 日期
       * num: 需要加上的天数
       */
      let newTime = moment(time).add(num, 'days')
      return moment(newTime).format('YYYY-MM-DD HH:mm')
    }
  },
};
</script>
<style>
.md-button-inner {
  font-size: 28px !important;
}
</style>

<style lang="scss" scoped>
.subscribe-redeem-state {
  height: 100%;
  .dialog-main {
    background-color: rgba(0, 0, 0, 0.28);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;

    .dialog {
      position: absolute;
      top: 235px;
      left: 30px;
      @include themeify {
        background: themed("bg-color");
        color: themed("text-color");
      }
      border-radius: 4px;
      width: calc(100% - 60px);
      font-size: 32px;

      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        .text {
          line-height: 132px;
        }
      }
      .password {
        width: calc(100% - 100px);
        margin: 40px 50px 0px 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        .item {
          @include themeify {
            background-color: themed("lam-bg");
          }
          width: 80px;
          height: 80px;
          display: flex;
          justify-content: center;
          align-items: center;
          .circular {
            @include themeify {
              background-color: themed("text-color");
            }
            width: 18px;
            height: 18px;
            border-radius: 50% 50%;
          }
        }
        .eye-passord {
          position: absolute;
          right: 33px;
          top: 35px;
          width: 41px;
          height: 20px;
        }
        .eye-div {
          @include themeify {
            background-color: themed("bg-color");
          }
          position: absolute;
          right: 1px;
          top: 1px;
          width: 68px;
          height: 85px;
          .eye-passord {
            position: absolute;
            right: 32px;
            top: 34px;
            width: 41px;
            height: 20px;
          }
        }
        .input-box {
          @include themeify {
            background: themed("bg-color");
            color: themed("tip-text");
            border-color: themed("input-line");
          }
          -moz-appearance: button; /* Firefox */
          -webkit-appearance: button; /* Safari 和 Chrome */
          appearance: button;
          border: 1px solid;
          outline: none;
          font-size: 28px;
          padding-left: 30px;
          width: 100%;
          border-radius: 2px;
          padding: 26px 80px 26px 30px;
          &:focus {
            @include themeify {
              color: themed("text-color");
              border-color: themed("primary-color");
            }
          }
        }
      }
      .password-prompt {
        width: calc(100% - 100px);
        margin: 0 50px;
        line-height: 64px;
        font-size: 28px;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .password-err {
          position: absolute;
          top: 0px;
          left: 0px;
          @include themeify {
            color: themed("error-color");
          }
        }
        .password-forget {
          @include themeify {
            color: themed("primary-color");
          }
        }
      }
      .bounced-operation {
        margin-top: 28px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        div {
          width: 50%;
          line-height: 90px;
          text-align: center;
          font-size: 28px;
        }
        .cancel {
          @include themeify {
            background-color: themed("bg-btn-default");
            color: themed("second-text");
          }
          border-radius: 0px 0px 0px 4px;
        }
        .cancel {
          @include themeify {
            background-color: themed("bg-btn-default");
            color: themed("second-text");
          }
          border-radius: 0px 0px 0px 4px;
        }
        .confim-disabled {
          @include themeify {
            background-color: themed("btn-second");
            color: themed("white-text");
          }
          border-radius: 4px 0px 0px 0px;
        }
        .confim {
          @include themeify {
            background-color: themed("primary-color");
            color: themed("white-text");
          }
          border-radius: 4px 0px 0px 0px;
        }
      }
    }
  }
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: 500;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  text-align: center;
  .subscribe-redeem-state-text {
    position: relative;
    font-size: 30px;
    line-height: 70px;
    font-weight: 400;
    margin-top: 34px;
    .cancel-order {
      @include themeify {
        color: themed("primary-color");
      }
      position: absolute;
      top: 0px;
      height: 80px;
      line-height: 80px;
      font-size: 28px;
      font-weight: 400;
      right: 30px;
    }
  }
  .account-num {
    font-weight: bold;
    font-size: 42px;
    line-height: 62px;
  }
  .order-status {
    @include themeify {
      color: themed("tag-text-yellow");
      border-bottom-color: themed("line-color");
    }
    width: calc(100% - 60px);
    margin: 0 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid;
    font-size: 24px;
    line-height: 44px;
  }
  .order-status-line {
    @include themeify {
      color: themed("line-high");
      border-bottom-color: themed("bg-btn-default");
    }
    width: calc(100% - 60px);
    margin: 0 30px;
    padding-top: 70px;
    border-bottom: 1px solid;
    font-size: 24px;
  }
  .order-state-content {
    .order-type {
      padding: 0 105px;
      display: flex;
      font-size: 26px;
      margin: 80px 0px 100px 0px;
      .buy {
        width: 40px;
        height: 334px;
      }
      .sell {
        width: 40px;
        height: 154px;
      }
      .order-message {
        text-align: left;
        margin-left: 40px;
        // div {
        //   &:last-child {
        //     margin-top: 38px;
        //   }
        //   p {
        //     &:last-child {
        //       font-size: 22px;
        //       margin-top: 20px;
        //       @include font_color(second-text);
        //     }
        //   }
        // }
      }
      .order-message1 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        div {
          margin-top: 40px;
          &:first-child {
            margin-top: 0;
          }
          p {
            &:last-child {
              @include font_color(second-text);
              margin-top: 19px;
            }
          }
        }
      }
    }
    .order-type-line {
      margin-top: 0px;
    }
    .order-state-ul {
      padding: 0 30px;
      font-size: 28px;
      margin-bottom: 70px;
      li {
        padding: 30px 0;
        // height: 100px;
        // line-height: 100px;
        display: flex;
        justify-content: space-between;
        align-items: start;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border-bottom: 0;
        }
        div {
          width: 470px;
          text-align: right;
        }
      }
    }
  }
}
</style>