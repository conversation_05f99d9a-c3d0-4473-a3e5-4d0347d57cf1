<template>
  <div class="company-details-box">
    <div
      v-if="companyDetail && companyDetail.pictureUrl"
      class="company-header-backImage"
    >
      <img
        :src="
          companyDetail && companyDetail.pictureUrl
            ? companyDetail.pictureUrl
            : ''
        "
        alt=""
      />
      <div
        v-if="companyDetail && companyDetail.logoUrl"
        class="company-details-header"
      >
        <img
          :src="
            companyDetail && companyDetail.logoUrl ? companyDetail.logoUrl : ''
          "
          alt=""
        />
        <div class="company-details-message">
          <p class="company-details-title">
            {{
              companyDetail.name[localeType[locale]]
                ? companyDetail.name[localeType[locale]]
                : "-"
            }}
          </p>
        </div>
      </div>
    </div>
    <div class="company-synopsis-product">
      <div class="company-synopsis-box">
        <div
          :class="
            synopsisShowAll
              ? 'company-synopsis-content-close'
              : 'company-synopsis-content'
          "
        >
          {{
            companyDetail && companyDetail.introduce
              ? companyDetail.introduce
              : "-"
          }}
        </div>
        <div class="company-synopsis-label">
          <span @click="synopsisShowAll = !synopsisShowAll">{{
            synopsisShowAll ? $t('fundMessage.packUp') : $t('fundMessage.more')
          }}</span>
        </div>
      </div>
      <!-- 旗下产品数据 -->
      <div class="company-synopsis-list-box">
        <div class="company-synopsis-border">
          <div class="company-synopsis-border-left"></div>
          {{ $t('fundMessage.subordinateFund') }}
        </div>
        <!-- 数据列表 -->
        <ul
          class="fund-debenture"
          v-if="companyFunsList && companyFunsList.length > 0"
        >
          <li class="fund-debenture-list">
            <div
              v-for="(item, index) in companyFunsList"
              :key="index"
              class="fund-debenture-details"
              @click="fundDetails(item)"
            >
              <div class="fund-debenture-header">
                <p class="fund-debenture-name">
                  {{
                    item.name[localeType[locale]]
                      ? item.name[localeType[locale]]
                      : "-"
                  }}
                </p>
                <p class="fund-debenture-incomeRatio">
                  <!-- <span
                    v-if="item.type == 'MONEY_MARKET'"
                    :class="{ 'fund-depreciate': item.annualised7Day < 0 }"
                  >
                    {{
                      item.annualised7Day > 0
                        ? '+' + (item.annualised7Day*100).toFixed(2)
                        : (item.annualised7Day*100).toFixed(2)
                    }}%
                  </span> -->
                  <span
                    :class="{
                      'fund-depreciate': item.cumulative1Y == 0,
                      'fund-depreciate-green':
                        (klineTheme === 'greenRiseRedFall' &&
                          item.cumulative1Y > 0) ||
                        (klineTheme === 'redRiseGreenFall' &&
                          item.cumulative1Y < 0),
                      'fund-depreciate-red':
                        (klineTheme === 'redRiseGreenFall' &&
                          item.cumulative1Y > 0) ||
                        (klineTheme === 'greenRiseRedFall' &&
                          item.cumulative1Y < 0),
                    }"
                  >
                    {{
                      item.cumulative1Y > 0
                        ? "+" + (item.cumulative1Y * 100).toFixed(2)
                        : (item.cumulative1Y * 100).toFixed(2)
                    }}%
                  </span>
                </p>
              </div>
              <div class="fund-debenture-header">
                <div class="fund-debenture-property">
                  <p>{{ item.currencyType }}</p>
                  <p>{{ bondType(item.type, $t("fundMessage.bondType")) }}</p>
                  <p>
                    {{
                      riskLevel(item.riskLevel, $t("fundMessage.fundRiskLevel"))
                    }}
                  </p>
                </div>
                <div class="fund-debenture-amount">
                  {{ $t("fundMessage.cumulative1Y") }}
                </div>
              </div>
            </div>
          </li>
        </ul>
        <ul v-else>
          <FundNotHave
            v-if="companyFunsList.length <= 0"
            :subordinateImage="true"
            :synopsisShowAll="synopsisShowAll"
          ></FundNotHave>
        </ul>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { Toast } from "mand-mobile";
import { getCompaniesDetails_funds } from "@/services/fund";
import FundNotHave from "@/components/fund-not-have";
import Loading from "@/components/loading/index";
import { riskLevel, bondType } from "@/utils/fund-type";
import { getAccountNumber } from "@/services/account";
export default {
  components: {
    FundNotHave,
    Loading,
  },
  data() {
    return {
      companyUrl: "", // 获取当前企业传递数据
      companyDetail: null, // 获取当前企业详情
      synopsisShowAll: false,
      companyFunsList: [], // 债券基金
      isLoading: true,
      fundType: null,
      pageNum: 0,
      pageSize: 15,
      localeType: {
        "zh-hans": "cn",
        "zh-hant": "hk",
        en: "us",
      },
    };
  },
  mounted() {
    this.companyUrl = this.$route.query;
    this.companiesFunds("detail"); // 详情
    this.companiesFunds("funds"); // 旗下基金
    this.getAccountNumber();
  },
  computed: {
    ...mapState(["locale", "klineTheme"]),
  },
  methods: {
    async getAccountNumber() {
      try {
        await getAccountNumber(
          this,
          this.$jsBridge.isSupported("getAppInfo")
        ).then((res) => {
          let object = res.data.tradingAccountList.find((item) => {
            return item.type === "FUND";
          });
          this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    companiesFunds(type) {
      // 基金公司--详情 & 旗下基金
      this.isLoading = true;
      var params = {};
      if (type == "detail") {
        params = {};
      } else {
        params = {
          size: this.pageSize,
          page: this.pageNum,
        };
      }
      getCompaniesDetails_funds(params, "", this.companyUrl.companyId, type)
        .then((res) => {
          // console.log(res);
          this.isLoading = false;
          if (res.code == 200) {
            if (type == "detail") {
              this.companyDetail = res.data;
            } else {
              this.companyFunsList = res.data.content;
            }
          } else {
            Toast({ content: res.msg });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    fundDetails(data) {
      // console.log(data)
      // data = Object.assign(data, { currentType: 2 })
      this.$router.push({
        path: "/productDetails",
        query: { fundIsin: data.fundIsin, currentType: "all" },
      });
    },
    bondType(type, data) {
      // 基金类型
      let newText = "-";
      newText = bondType(type, data);
      return newText;
    },
    riskLevel(type, data) {
      // 基金风险等级
      let newText = "-";
      newText = riskLevel(type, data);
      return newText;
    },
  },
};
</script>

<style lang="scss" scoped>
.company-details-box {
  height: 100%;
  overflow: auto;
  @include font_color(second-text);
  .company-header-backImage {
    // padding: 60px 30px;
    position: relative;
    height: 324px;
    // background: url(../../assets/images/market_fund_co_bg_dark.png)no-repeat;
    // background-size: 100% 100%;
    opacity: 0.8;
    img {
      width: 100%;
      height: 100%;
    }
    .company-details-header {
      // padding: 0 30px;
      height: 128px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: absolute;
      top: 60px;
      left: 30px;
      right: 30px;
      img {
        width: 214px;
        height: 100%;
        border-radius: 8px;
        margin-right: 21px;
      }
      .company-details-image {
        width: 214px;
        height: 128px;
        border-radius: 8px;
        overflow: hidden;
        @include background_color(bg-color);
      }
      .company-details-message {
        // width: 446px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .company-details-title {
          font-size: 28px;
          font-weight: 600;
          @include font_color(white-text);
        }
        .company-details-introduce {
          font-size: 22px;
          margin-top: 20px;
        }
      }
    }
  }

  // .company-synopsis-bg {
  //   // @include background_color(fund-bg);
  // }
  .company-synopsis-product {
    position: absolute;
    top: 248px;
    left: 0;
    right: 0;
    padding: 0 30px;
    @include background_color(bg-color);
    border-radius: 40px 40px 0px 0px;
    @include font_color(text-color);
    .company-synopsis-box {
      // width: 690px;
      // height: 197px;
      padding: 40px 0;
      font-size: 26px;
      .company-synopsis-content {
        // height: 100px;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .company-synopsis-content,
      .company-synopsis-content-close {
        text-indent: 50px;
        line-height: 39px;
      }
      .company-synopsis-label {
        // margin-top: 10px;
        @include font_color(fund-bg);
        text-align: right;
      }
    }
    .company-synopsis-list-box {
      font-size: 34px;
      font-weight: 500;
      @include font_color(text-color);
      .company-synopsis-border {
        display: flex;
        justify-content: start;
        align-items: center;
        .company-synopsis-border-left {
          width: 6px;
          height: 20px;
          @include background_color(fund-bg);
          border-radius: 3px;
          margin-right: 10px;
        }
      }
      .fund-debenture {
        // padding: 30px;
        @include box_shadow(shadow-color);
        // box-shadow: 0px 3px 12px 0px #ECF1F8;
        .fund-debenture-list {
          // padding: 0 30px;
          margin-top: 35px;
          // height: 494px;
          // overflow-y: auto;
          @include background_color(second-bg);
          .fund-debenture-details {
            padding: 40px 30px;
            border-bottom: 1px solid;
            border-radius: 4px;
            @include border_color(line-color);
            &:last-child {
              border-bottom: 0;
            }
            .fund-debenture-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .fund-debenture-name {
                font-size: 28px;
                width: 430px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .fund-debenture-incomeRatio {
                font-size: 40px;
                font-weight: normal;
                .fund-depreciate {
                  @include font_color(second-text);
                }
                .fund-depreciate-green {
                  @include font_color(k-line-green);
                }
                .fund-depreciate-red {
                  @include font_color(k-line-red);
                }
              }
              .fund-debenture-property {
                display: flex;
                align-items: center;
                margin-top: 12px;
                font-size: 22px;
                @include font_color(btn-color);
                flex-wrap: wrap;
                p {
                  @include background_color(lam-bg);
                  height: 38px;
                  line-height: 38px;
                  margin-right: 20px;
                  padding: 0 14px;
                  border-radius: 2px;
                  margin-top: 10px;
                  margin-bottom: 10px;
                }
                p:last-child {
                  margin-right: 0;
                  @include font_color(tag-text-yellow);
                  @include background_color(warn-color);
                }
              }
              .fund-debenture-amount {
                font-size: 22px;
                margin-top: 22px;
                @include font_color(second-text);
              }
            }
          }
        }
      }
    }
  }
}
</style>
