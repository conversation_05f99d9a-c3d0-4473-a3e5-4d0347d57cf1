<template>
  <div
    class="fund-kind-box"
    :class="{ 'find-kind-box-position': isScreeningConditions }"
  >
    <md-scroll-view
        :scrolling-x="false"
        @endReached="onEndReached"
        ref="scrollView"
        class="fund-scroll-view"
        @refreshing="onRefresh"
      >
        <md-scroll-view-refresh
          slot="refresh"
          slot-scope="{ scrollTop, isRefreshActive, isRefreshing }"
          :scrolling-x="false"
          :scroll-top="scrollTop"
          :is-refreshing="isRefreshing"
          :is-refresh-active="isRefreshActive"
          :refresh-text="$t('common.pullfresh')"
          :refresh-active-text="$t('common.releaseRefresh')"
          :refreshing-text="$t('common.refreshing')"
        >
        </md-scroll-view-refresh>
      <ul class="fund-type-tab">
        <li
          v-for="(item, index) in $t('fundMessage.fundTypeTabs')"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: currentTypeTab == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: currentTypeTab == item.name }"
          ></div>
        </li>
      </ul>
      <ul class="fund-kind-search">
        <li class="fund-kind-left">
          <div
            v-for="(item, index) in $t('fundMessage.fundSearchTabs')"
            :key="index"
            @click="searchArguments(item)"
            class="fund-kind-left-div"
          >
            <div class="fund-kind-select">
              {{ item.name == "LAST_MONTH" ? timeTile : item.label }}
            </div>
            <div class="fund-kind-sort" v-if="item.name != 4">
              <img
                v-if="item.name == 'LAST_MONTH'"
                :src="
                  checkBtn
                    ? require(`../../assets/images/icon_sorting_down_select.png`)
                    : require(`../../assets/images/below_icon_px.png`)
                "
                alt=""
              />
              <template v-if="item.name == 2">
                <img
                  :src="
                    checkSelect1 == 'DESC'
                      ? require(`../../assets/images/icon_sorting_up_select.png`)
                      : require(`../../assets/images/up_icon_px.png`)
                  "
                  alt=""
                />
                <img
                  :src="
                    checkSelect1 == 'ASC'
                      ? require(`../../assets/images/icon_sorting_down_select.png`)
                      : require(`../../assets/images/below_icon_px.png`)
                  "
                  alt=""
                />
              </template>
              <template v-if="item.name == 3">
                <img
                  :src="
                    checkSelect2 == 'DESC'
                      ? require(`../../assets/images/icon_sorting_up_select.png`)
                      : require(`../../assets/images/up_icon_px.png`)
                  "
                  alt=""
                />
                <img
                  :src="
                    checkSelect2 == 'ASC'
                      ? require(`../../assets/images/icon_sorting_down_select.png`)
                      : require(`../../assets/images/below_icon_px.png`)
                  "
                  alt=""
                />
              </template>
            </div>
            <div class="fund-kind-more-search" v-else>
              <img src="../../assets/images/icon_sorting_more.png" alt="" />
            </div>
          </div>
        </li>
        <!-- 自定义下拉选项 -->
        <div v-if="showTimeSelect" class="condition-time-box" @click="closeMask">
          <div class="condition-time">
            <div
              v-for="(child, index1) in $t('fundMessage.fundSearchTime')"
              :key="index1"
              @click="timeSelect(child)"
            >
              <p :class="{ checkSelect: childrenSelect == child.key }">
                {{ child.label }}
              </p>
            </div>
          </div>
        </div>
      </ul>
      <!-- 基金排行数据 -->
      
        
      <ul class="fund-product-box" v-if="fundProductList.length > 0">
        <li
          v-for="(item, index) in fundProductList"
          :key="index"
          class="fund-product-list"
        >
          <div class="fund-product-list-box">
            <div class="fund-product-list-top" @click="fundDetails(item)">
              <!-- charts -->
              <div class="list-top-left" v-if="childrenSelect!='SINCE_LAUNCH'">
                <canvas :id="'container' + index"></canvas>
              </div>
              <div class="list-top-right" :class="{'list-right-width': childrenSelect=='SINCE_LAUNCH'}">
                <p>{{ fundsNameFun(item) }}</p>
                <div class="list-type">
                  <span>{{ item.currencyType ? item.currencyType : "" }}</span>
                  <span>{{
                    bondType(item.assetClassType, $t("fundMessage.bondType"))
                  }}</span>
                  <span>{{
                    riskLevel(item.riskLevel, $t("fundMessage.fundRiskLevel"))
                  }}</span>
                </div>
              </div>
            </div>
            <div class="fund-product-list-bottom">
              <div class="list-bottom-left" @click="fundDetails(item)">
                <!-- <template
                  v-if="
                    (currentTypeTab == 'all' &&
                      item.assetClassType == 'MONEY_MARKET') ||
                    currentTypeTab == 'MONEY_MARKET'
                  "
                >
                  <p
                    :class="
                      klineTheme === 'redRiseGreenFall'
                        ? Number(item.annualised7Day) < 0
                          ? 'fund-type-price'
                          : Number(item.annualised7Day)
                          ? 'current-type'
                          : 'current-type1'
                        : Number(item.annualised7Day) < 0
                        ? 'current-type'
                        : Number(item.annualised7Day)
                        ? 'fund-type-price'
                        : 'current-type1'
                    "
                  >
                    {{
                      Number(item.annualised7Day) > 0
                        ? "+" + Number(item.annualised7Day * 100).toFixed(2)
                        : Number(item.annualised7Day)
                        ? (item.annualised7Day * 100).toFixed(2)
                        : "0.00"
                    }}%
                  </p>
                  <p class="set-font-size">{{ $t("fundMessage.annualYield") }}</p>
                </template> -->
                <template>
                  <p
                    :class="
                      klineTheme === 'redRiseGreenFall'
                        ? Number(item.change) < 0
                          ? 'fund-type-price'
                          : Number(item.change)
                          ? 'current-type'
                          : 'current-type1'
                        : Number(item.change) < 0
                        ? 'current-type'
                        : Number(item.change)
                        ? 'fund-type-price'
                        : 'current-type1'
                    "
                  >
                    {{
                      Number(item.change) > 0
                        ? "+" + Number(item.change * 100).toFixed(2)
                        : Number(item.change)
                        ? Number(item.change * 100).toFixed(2)
                        : "0.00"
                    }}%
                  </p>
                  <p class="set-font-size">{{ timeTile }}<span v-if="locale != 'en'">{{ $t("fundMessage.expressive") }}</span></p>
                </template>
              </div>
              <div class="list-bottom-right" :class="{'list-right-width': childrenSelect!='SINCE_LAUNCH'}">
                <div @click="fundDetails(item)">
                  <p>{{ Number(item.netAssetValue).toFixed(4) }}</p>
                  <p class="set-font-size">{{ $t("fundMessage.netAssetValue") }}</p>
                </div>
                <!-- 立即申购按钮 -->
                <div @click="goTotTansaction(item)">
                  <div class="buy-button">
                    {{ $t("fundMessage.fundBtnText") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="fund-product-list-border"></div>
        </li>
        <md-scroll-view-more
          slot="more"
          :is-finished="!hasMore"
          :is-loading="isLoading"
          :finished-text="$t('common.allLoaded')"
          :loading-text="$t('common.loading')"
        >
        </md-scroll-view-more>
      </ul>
        
        
      <div v-else class="fund-product-null">
        <img
          :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
          alt=""
        />
      </div>
      <!-- footer -->
      
      <fundIntroduce></fundIntroduce>
      

      <loading v-if="isLoading"></loading>
      <div class="dialog-main" v-show="isRisklevel">
        <div class="dialog">
          <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
          <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
          <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
          <div class="button-bottom-risk">
            <div class="first-class" @click="isRisklevel = false">
              {{ $t("common.btns.cancel") }}
            </div>
            <div @click="toMeasure">{{ $t("myAccount.reEvaluation") }}</div>
          </div>
        </div>
      </div>
      <div class="dialog-main" v-show="isComplexProductQualified">
        <div class="dialog">
          <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
          <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
          <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
          <div class="button-bottom-risk">
            <div class="first-class" @click="isComplexProductQualified = false">
              {{ $t("common.btns.cancel") }}
            </div>
            <div @click="toSuitabilityMatchingRecord">
              {{ $t("common.btns.examinecause") }}
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-main" v-show="isComplexProduct">
        <div class="dialog">
          <p class="content-describe">
            {{ $t("myAccount.riskDescriptionComplexProduct1") }}
          </p>
          <p class="content-describe">
            {{ $t("myAccount.riskDescriptionComplexProduct2") }}
          </p>
          <p class="content-describe">
            {{ $t("myAccount.riskDescriptionComplexProduct3") }}
          </p>
          <p class="content-describe">
            {{ $t("myAccount.riskDescriptionComplexProduct4") }}
          </p>
          <div class="button-bottom-risk">
            <div class="first-class" @click="isComplexProduct = false">
              {{ $t("common.btns.cancel") }}
            </div>
            <div @click="toBuy">{{ $t("myAccount.goOn") }}</div>
          </div>
        </div>
      </div>
      <!-- 风险测评 dialog -->
      <div class="dialog-main" v-if="ExpiredShow">
        <div class="dialog">
          <p class="content-describe">{{  expired_ExpiringSoon }}</p>
          <div class="button-bottom-risk">
            <div class="first-class" @click="onBasicCancel">{{ ExpiredBtn === 'later' ? this.$t('common.Later') : $t('common.btns.cancel') }}</div>
            <div @click="onBasicConfirm">{{ $t('common.SubmitQuestionnaire') }}</div>
          </div>
        </div>
      </div>
    
    </md-scroll-view>
    <screening-conditions
      v-show="isScreeningConditions"
      @closeScreeningConditions="closeScreeningConditions"
      :isScreeningConditions="isScreeningConditions"
      :currentTypeTab="currentTypeTab"
      class="screening-conditions"
    ></screening-conditions>
  </div>
</template>

<script>
import { mapState } from "vuex";
import F2 from "@antv/f2";
import fundIntroduce from "@/components/footer/fund-introduce.vue";
import { getAccountNumber, getAccountRiskLevel, getFundInformation, fundCashRiskAssessment } from "@/services/account";
import screeningConditions from "./screening-conditions";
import loading from "@/components/loading/index";
import { riskLevelList, bondType } from "@/utils/fund-type";
import { fundsRankingList } from "@/services/fund";
// import { getAccountPosition, getAccountRiskLevel } from "@/services/account";
import { Toast, ScrollView, ScrollViewRefresh, ScrollViewMore } from "mand-mobile";
import { tradeRules } from "@/services/fund";
import { refreshAppToken } from "@/utils/auth";
// import { getFundInformation } from "@/services/account.js";
import moment from 'moment'
export default {
  components: {
    fundIntroduce,
    screeningConditions,
    loading,
    [ScrollView.name]: ScrollView,
    [ScrollViewRefresh.name]: ScrollViewRefresh,
    [ScrollViewMore.name]: ScrollViewMore,
  },
  data() {
    return {
      currentData: {},
      isComplexProductQualified: false,
      isComplexProduct: false,
      isRisklevel: false,
      tradeAccountStatus: "",
      isScreeningConditions: false,
      currentTypeTab: "all",
      timeTile: this.$t("fundMessage.timeTile"),
      // childrenSelect: "LAST_YEAR",
      childrenSelect: "SINCE_LAUNCH",
      showTimeSelect: false,
      // checkSelect: 'LAST_YEAR',
      checkSelect1: "DESC",
      checkSelect2: "",
      checkBtn: false,
      checkBtn1: true,
      checkBtn2: true,
      moreSelect: {},
      chartDatas: [],
      fundProductList: [],
      pageNum: 0,
      pageSize: 20, // 改成20
      isLoading: true,
      tradeRulesData: "",
      ExpiredBtn: '',
      ExpiredShow: false,
      riskFundIsin: "",
      expired_ExpiringSoon: '',
      riskData: null,
      riskResData: null,
      hasMore: true, // 是否还有更多数据
      // 添加缓存对象
      fundTypeCache: {}, // 缓存不同基金类型的数据
    };
  },
  computed: {
    ...mapState(["theme", "token", "locale", "klineTheme"]),
  },
  destroyed() {
    // 保存当前类型的数据到缓存
    if (this.fundProductList.length > 0) {
      this.saveFundTypeCache(this.currentTypeTab)
    }
    if (document.body) {
      document.body.scrollTop = 0;
    }
    if (document.documentElement) {
      document.documentElement.scrollTop = 0;
    }
  },
  mounted() {
    this.timeTile = this.$t("fundMessage.timeTile");
    if (this.$route.query.name) {
      let object = {};
      object.name = this.$route.query.name;
      this.checkType(object);
    } else {
      this.searchFundsList();
    }
    this.$jsBridge.run("getLoginState", {
      callback: ({ loginStatus }) => {
        if (loginStatus) {
          this.getAccountNumber();
        }
      },
    });
  },
  methods: {
    toMeasure() {
      this.isRisklevel = false;
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/risk/tips?nav=0`,
        callback: () => {},
      });
    },
    toBuy() {
      this.isComplexProductQualified = false;
      this.testTransication(this.currentData);
    },
    toSuitabilityMatchingRecord() {
      this.isComplexProduct = false;
      this.$router.push({
        path: "/suitabilityMatchingRecord",
        query: { id: this.currentData.fundIsin },
      });
    },
    searchFundsList(isLoadMore = false) {
      // 基金排行--数据列表
      if (!isLoadMore) {
        this.isLoading = true;
        console.log(3333333)
      }
      
      let { currencyType, riskLevel, dividend, fundCompanyIds } = this.moreSelect;
      let params = {
        type: this.currentTypeTab == "all" ? "" : this.currentTypeTab,
        timeInterval: this.childrenSelect,
        yield: this.checkSelect1,
        netAssetValue: this.checkSelect2,
        page: this.pageNum,
        size: this.pageSize, // 使用20
        currencyType: currencyType && currencyType.length > 0 ? currencyType.join(",") : "",
        riskLevel: riskLevel && riskLevel.length > 0 ? riskLevel.join(",") : "",
        dividend: dividend,
        fundCompanyIds: fundCompanyIds && fundCompanyIds.length > 0 ? fundCompanyIds.join(",") : "",
      };
      
      fundsRankingList(params, "")
        .then((res) => {
          if (res.code == 200) {
            if (isLoadMore) {
              this.fundProductList = [...this.fundProductList, ...res.data.content];
            } else {
              this.fundProductList = res.data.content;
            }
            
            // 判断是否还有更多数据
            this.hasMore = res.data.totalPages - 1 > this.pageNum;
            
            // 处理图表数据
            if (this.childrenSelect != 'SINCE_LAUNCH') {
              this.handleChartData(res.data.content, isLoadMore);
            }
            
            this.isLoading = false;
            this.$nextTick(()=>{
              this.$refs.scrollView.finishRefresh()
              if (isLoadMore) {
                this.$refs.scrollView.finishLoadMore()
              }
            })
          }
        })
        .catch((err) => {
          console.log(err);
          this.isLoading = false;
          // 错误时也要完成滚动组件状态
          this.$refs.scrollView.finishRefresh()
          this.$refs.scrollView.finishLoadMore()
        });
    },
    closeScreeningConditions(item) {
      this.isScreeningConditions = item.isShow;
      this.moreSelect = item.requestDetails;
      if (JSON.stringify(item.requestDetails) != "{}") {
        this.searchFundsList();
      }
    },
    checkType(data) {
      // console.log('切换基金种类', data);
      const newTypeTab = data.name
       console.log(this.currentTypeTab === newTypeTab, this.currentTypeTab, newTypeTab)
      if (this.currentTypeTab === newTypeTab) {
        return
      }

      // 先保存当前类型的数据到缓存
      if (this.fundProductList.length > 0) {
        this.saveFundTypeCache(this.currentTypeTab)
      }

      // 切换到新类型
      this.currentTypeTab = newTypeTab
      this.timeTile = this.$t("fundMessage.timeTile");
      // this.childrenSelect = "LAST_YEAR";
      this.childrenSelect = "SINCE_LAUNCH";
      this.checkSelect1 = "DESC";
      this.checkSelect2 = "";
      this.checkBtn = false;
      this.checkBtn1 = true;
      this.checkBtn2 = true;
      this.moreSelect = {};
      // this.pageNum = 0; // 重置页码
      // this.hasMore = true; // 重置加载状态
      // this.searchFundsList();
      // 检查缓存中是否有该类型的数据
      if (this.loadFundTypeCache(newTypeTab)) {
        // 有缓存数据，直接使用
        // console.log('从缓存加载数据:', newTypeTab)
        return
      }

      // 没有缓存数据，重新加载
      // console.log('重新加载数据:', newTypeTab)
      this.onRefresh()  
    },
    // 保存当前基金类型的数据到缓存
    saveFundTypeCache(typeTab) {
      if (!typeTab || this.fundProductList.length === 0) {
        return
      }

      const cacheKey = this.generateCacheKey(typeTab)
      this.fundTypeCache[cacheKey] = {
        fundProductList: [...this.fundProductList],
        pageNum: this.pageNum,
        hasMore: this.hasMore,
        timeTile: this.timeTile,
        childrenSelect: this.childrenSelect,
        checkSelect1: this.checkSelect1,
        checkSelect2: this.checkSelect2,
        moreSelect: { ...this.moreSelect },
        timestamp: Date.now(), // 添加时间戳，可用于缓存过期判断
      }

      // console.log('保存缓存数据:', typeTab, this.fundTypeCache[cacheKey])
    },
    loadFundTypeCache(typeTab) {
      const cacheKey = this.generateCacheKey(typeTab)
      const cachedData = this.fundTypeCache[cacheKey]

      if (!cachedData) {
        return false
      }

      // 检查缓存是否过期（可选：设置缓存有效期，比如5分钟）
      const cacheExpireTime = 5 * 60 * 1000 // 5分钟
      if (Date.now() - cachedData.timestamp > cacheExpireTime) {
        // 缓存已过期，删除缓存
        delete this.fundTypeCache[cacheKey]
        return false
      }

      // 恢复缓存的数据
      this.fundProductList = [...cachedData.fundProductList]
      this.pageNum = cachedData.pageNum
      this.hasMore = cachedData.hasMore
      this.timeTile = cachedData.timeTile
      this.childrenSelect = cachedData.childrenSelect
      this.checkSelect1 = cachedData.checkSelect1
      this.checkSelect2 = cachedData.checkSelect2
      this.moreSelect = { ...cachedData.moreSelect }

      // 使用 $nextTick 确保 DOM 更新后再处理滚动组件状态
      this.$nextTick(() => {
        // 重新绘制图表（如果需要）
        if (this.childrenSelect !== 'SINCE_LAUNCH' && this.fundProductList.length > 0) {
          this.handleChartData(this.fundProductList, false)
        }

        // 重置滚动组件状态，解决上拉加载和底部空白问题
        this.resetScrollViewState()
      })

      // console.log('从缓存加载数据:', typeTab, cachedData)
      return true
    },

    // 生成缓存键
    generateCacheKey(typeTab) {
      // 可以根据需要包含更多参数来生成更精确的缓存键
      return `${typeTab}_${this.locale}`
    },

    // 清除当前类型的缓存
    clearCurrentTypeCache() {
      const cacheKey = this.generateCacheKey(this.currentTypeTab)
      if (this.fundTypeCache[cacheKey]) {
        delete this.fundTypeCache[cacheKey]
        // console.log('清除缓存:', this.currentTypeTab)
      }
    },

    // 清除所有缓存
    clearAllCache() {
      this.fundTypeCache = {}
      console.log('清除所有缓存')
    },

    // 重置滚动组件状态
    resetScrollViewState() {
      if (this.$refs.scrollView) {
        // 确保滚动组件状态正确
        this.$refs.scrollView.finishRefresh()
        this.$refs.scrollView.finishLoadMore()

        // 延迟刷新滚动区域计算
        setTimeout(() => {
          if (this.$refs.scrollView && this.$refs.scrollView.refresh) {
            this.$refs.scrollView.refresh()
          }
        }, 100)
      }
    },
    closeMask() {
      this.showTimeSelect = false;
      // console.log(this.timeTile, this.childrenSelect)
    },
    timeSelect(data) {
      // 时间段
      // console.log('最新数据====', data, this.childrenSelect)
      this.timeTile = data.label;
      this.childrenSelect = data.key;
      this.showTimeSelect = false;
      this.checkBtn = false;
      // 清除当前类型的缓存，因为搜索条件改变了
      this.clearCurrentTypeCache()
      // this.searchFundsList();
      this.onRefresh() 
    },
    searchArguments(data) {
      switch (
        data.name // 弹出时间段下拉列表
      ) {
        case "LAST_MONTH":
          // this.checkSelect = data.name;
          this.showTimeSelect = true;
          this.checkBtn = true;
          break;
        case 2:
          // this.checkBtn1 = !this.checkBtn1
          this.checkSelect1 =
            this.checkSelect1 == "" || this.checkSelect1 != "DESC"
              ? "DESC"
              : "ASC";
          this.checkSelect2 = "";
          break;
        case 3:
          // this.checkBtn2 = !this.checkBtn2
          this.checkSelect2 =
            this.checkSelect2 == "" || this.checkSelect2 != "DESC"
              ? "DESC"
              : "ASC";
          this.checkSelect1 = "";
          break;
        case 4:
          this.isScreeningConditions = true;
          break;
        default:
          break;
      }
      if (data.name != "LAST_MONTH") {
        // 清除当前类型的缓存，因为搜索条件改变了
        this.clearCurrentTypeCache()
        // this.searchFundsList();
        this.onRefresh()
      }
    },
    bondType(type, data) {
      // 基金类型
      let newText = "";
      newText = bondType(type, data);
      return newText;
    },
    riskLevel(type, data) {
      // 基金风险等级
      let newText = "";
      newText = riskLevelList(type, data);
      return newText;
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data.name && data.name.cn ? data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        return data.name && data.name.hk ? data.name.hk : "-";
      } else {
        return data.name && data.name.us ? data.name.us : "-";
      }
    },
    /**
     *  index: 下标
     *  dataArr: 当前数据 -- 数组
     *  changePercentNum: 涨幅数据
     *  day7num: 七日年化
     *  changeNum: 其他
     */
    drawCharts(index, dataArr, changePercentNum, day7num, changeNum) {
      var chartName = new F2.Chart({
        id: "container" + index,
        pixelRatio: window.devicePixelRatio,
        padding: 0,
      });
      chartName.source(dataArr, {
        date: {
          // type: 'timeCat',
          // tickCount: 3,
          range: [0, 1],
        },
        // changePercent: {
        //   tickCount: 5,
        //   min: 0,
        // },
      });

      chartName.axis("date", {
        label: null,
        line: null,
        tickLine: null,
        grid: null,
      });
      chartName.axis("changePercent", {
        label: null,
        line: null,
        tickLine: null,
        grid: null,
      });
      chartName.tooltip(
        false
        // {
        //   custom: true, // 圆点, 没有辅助信息数据框
        //   // showCrosshairs: true,
        // }
      );
      let newColor, newColor1, createColor;
      if (this.theme == "dark") {
        createColor = "#21232D";
      } else {
        createColor = "#ffffff";
      }
      /**
       * #F13E3D: 红色
       * #0ACCA0: 绿色
       * #868E9E: 灰色
       * 一、
       *    1. klineTheme == 'redRiseGreenFall' ==> 红涨绿跌
       *    2. klineTheme == 'greenRiseRedFall' ==> 绿涨红跌
       *    3. day7num > 0 ==> #F13E3D, day7num < 0 ==> #0ACCA0, day7num 值为 false ==> #868E9E
       *    4. day7num > 0 ==> #0ACCA0, day7num < 0 ==> #F13E3D, day7num 值为false ==> #868E9E
       * 二、
       *    1. klineTheme == 'redRiseGreenFall' ==> 红涨绿跌
       *    2. klineTheme == 'greenRiseRedFall' ==> 绿涨红跌
       *    3. changeNum > 0 ==>  #F13E3D, changeNum < 0 ==> #0ACCA0, changeNum 值为 false ==> #868E9E
       *    4. changeNum > 0 ==> #0ACCA0, changeNum < 0 ==> #F13E3D, changeNum 值为false ==> #868E9E
       */
      if (day7num) {
        newColor = this.chartsColor(
          day7num,
          newColor,
          newColor1,
          createColor
        ).color;
        newColor1 = this.chartsColor(
          day7num,
          newColor,
          newColor1,
          createColor
        ).color1;
      } else {
        newColor = this.chartsColor(
          changeNum,
          newColor,
          newColor1,
          createColor
        ).color;
        newColor1 = this.chartsColor(
          changeNum,
          newColor,
          newColor1,
          createColor
        ).color1;
      }

      // if (Number(changePercentNum) > 0) {
      //   newColor = 'l(90) 0:#F13E3D 1:' + createColor
      //   newColor1 = 'l(90) 0:#F13E3D 1:#F13E3D'
      // } else {
      //   newColor = 'l(90) 0:#0ACCA0 1:' + createColor
      //   newColor1 = 'l(90) 0:#0ACCA0 1:#0ACCA0'
      // }
      // chartName.axis(false); // 隐藏坐标轴
      chartName.area().position("date*changePercent").color(newColor).size(1);
      chartName
        .line()
        .position("date*changePercent")
        .color(newColor1)
        .size(0.5);
      chartName.render();
    },
    chartsColor(num, color, color1, createColor) {
      if (this.klineTheme === "redRiseGreenFall") {
        // 红涨绿跌
        if (num) {
          color = "l(90) 0:#F13E3D 1:" + createColor;
          color1 = "l(90) 0:#F13E3D 1:#F13E3D";
          if (num < 0) {
            color = "l(90) 0:#0ACCA0 1:" + createColor;
            color1 = "l(90) 0:#0ACCA0 1:#0ACCA0";
          }
        } else {
          // false
          color = "l(90) 0:#868E9E 1:" + createColor;
          color1 = "l(90) 0:#868E9E 1:#868E9E";
        }
      } else {
        // 绿涨红跌
        if (num) {
          color = "l(90) 0:#0ACCA0 1:" + createColor;
          color1 = "l(90) 0:#0ACCA0 1:#0ACCA0";
          if (num < 0) {
            color = "l(90) 0:#F13E3D 1:" + createColor;
            color1 = "l(90) 0:#F13E3D 1:#F13E3D";
          }
        } else {
          // false
          color = "l(90) 0:#868E9E 1:" + createColor;
          color1 = "l(90) 0:#868E9E 1:#868E9E";
        }
      }
      return { color, color1 };
    },
    fundDetails(data) {
      // 数据列表点击
      this.$router.push({
        path: "/productDetails",
        query: { fundIsin: data.fundIsin, currentType: data.assetClassType },
      });
    },

    async getAccountNumber() {
      try {
        await getAccountNumber(this).then((res) => {
          let object = res.data.tradingAccountList.find((item) => {
            return item.type === "FUND";
          });
          this.tradeAccountStatus = object.status;
          this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    goTotTansaction(data) {
      // this.routerRun(data)
      this.riskFundIsin = data.fundIsin
      console.log(data);
      // 判断登陆状态
      this.$jsBridge.run("getLoginState", {
        callback: ({ loginStatus }) => {
          if (loginStatus) {
            this.routerRun(data);
          } else {
            this.loginFund();
          }
        },
      });
    },
    onBasicConfirm() { // 跳转风险评测
      this.ExpiredShow = false
      // this.$router.push({
      //   path: "/risk/tips"
      // })
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/risk/tips?nav=0&isExpired=yes`,
        callback: () => {},
      });
    },
    onBasicCancel() { // 跳转基金申购页面
      console.log('取消按钮');
      this.ExpiredShow = false
      if (this.ExpiredBtn === 'later') {
        this.accountAttribute()
        // 申购页面
        // this.$router.push({
        //   path: "/transactionBuy",
        //   query: {
        //     fundIsin: this.riskFundIsin,
        //   },
        // })
      }
    },
    routerRun(data) {
      this.riskData = data
      // 判断基金是否开户
      getAccountNumber(this).then((res) => {
        this.riskResData = res
        if (res.data.tradingAccountList && res.data.tradingAccountList.length) {
          let object = res.data.tradingAccountList.find((item) => {
            return item.type === "FUND";
          });
          if (object && object.tradeAccountNumber) {
            // 有开过基金账户

            /*
               风险测评日期有效状态 --> 风险测评结果已过期，则不允许用户申购
              （1）如有登录校验、基金开户校验，则放在登录校验、基金开户校验后面。
              （2）如没有登录校验、基金开户校验，则放在第一步校验
            */

            fundCashRiskAssessment(object.tradeAccountNumber).then(riskStatus => {
              this.tradeAccountStatus = object.status;
              this.$store.commit("setAccountNumber", object.tradeAccountNumber);
              if (riskStatus.data.assessmentStatus === 'Expired') {
                this.ExpiredShow = true
                this.ExpiredBtn = 'cancel'
                this.expired_ExpiringSoon = this.$t('common.Expired')
                return
              } else if (riskStatus.data.assessmentStatus === 'ExpiringSoon') {
                this.ExpiredShow = true
                this.ExpiredBtn = 'later'
                this.expired_ExpiringSoon = this.$t('common.ExpiringSoon', { time: moment(riskStatus.data.expiryDate).format("YYYY-MM-DD") })
                return
              } else { // 已登录 && 已开户 && 风险测评未过期
                this.accountAttribute()
              }
            })
          } else {
            // 有开过户 没有开基金账户
            this.$jsBridge.run("toPage", {
              jumpType: "NATIVE",
              loginState: "JUDGMENT",
              openAccountState: "JUDGMENT_FUND",
              navigationContentCode: "FUND_ACCOUNT",
              navigationUri: "FUND_ACCOUNT",
              titleDisplay: "DISPALY",
            });
          }
        } else {
          // 什么户都没开
          this.$jsBridge.run("toPage", {
            jumpType: "NATIVE",
            loginState: "JUDGMENT",
            openAccountState: "JUDGMENT",
            navigationContentCode: "FUND_ACCOUNT",
            navigationUri: "FUND_ACCOUNT",
            titleDisplay: "DISPALY",
          });
        }
      });
    },

    judgeTheRiskLevelComplex(data) {
      // 判断当前账号风险等级是否可以购买当前基金
      getAccountRiskLevel(this).then((res) => {
        if (
          data.riskLevel <= Number(res.data.result[1]) &&
          res.data.isDerivative
        ) {
          this.isComplexProduct = true;
          this.currentData = data;
          return;
        } else {
          this.isComplexProductQualified = true;
          this.currentData = data;
        }
      });
    },
    judgeTheRiskLevel(data) {
      // 判断当前账号风险等级是否可以购买当前基金
      getAccountRiskLevel(this).then((res) => {
        if (Number(res.data.result[1]) < data.riskLevel) {
          this.isRisklevel = true;
          return;
        }
        this.testTransication(data);
      });
    },
    accountAttribute() {
      // 判定当前基金账户是否可用
      if (this.tradeAccountStatus != "OPEN") {
        Toast.info(this.$t("myAccount.abnormalAccount"));
        return;
      }
      // 判断当前基金能不能交易
      if (!this.riskData.buyFlag) {
        Toast.info(this.$t("myAccount.subscriptionIsNotSupported"));
        return;
      }
      // 用户国籍是否在基金可申购地区范围 data.supportRegions, 该基金是否专业投资者才能申购
      if (this.riskData.supportRegions && this.riskData.supportRegions.length > 0) { // 地区
        if (!this.riskData.supportRegions.includes(this.riskResData.data.user.issuingCountry)) {
          Toast.info(this.$t("myAccount.regionIsNotSupported"));
          return;
        }
      }
      /**
       * @params
       * professionalInvestorFlag: true (仅限专业投资者), false (不做专业投资者限制)
       * isProfessionalInvestor: 'Y' (当前账户为专业投资者), 'N' (当前账户为非专业投资者)
       */
      if (this.riskResData.data.user.isProfessionalInvestor !== 'Y' && this.riskData.professionalInvestorFlag) { // 非专业投资者
        Toast.info(this.$t("myAccount.specialtyInvestor"));
        return;
      }
      // 判断是否是复杂产品
      getFundInformation(this.riskData.fundIsin).then((res) => {
        if (res.data.complexProductFlag) {
          // 复杂产品流程
          this.judgeTheRiskLevelComplex(this.riskData);
        } else {
          this.judgeTheRiskLevel(this.riskData);
        }
      });
    },
    testTransication(data) {
      tradeRules(data.fundIsin).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          this.routerRunLast(data);
        }
      });
    },
    routerRunLast(data) {
      // // 判断当前基金能不能交易
      // if (!this.tradeRulesData.buyFlag) {
      //   Toast.info(this.$t("myAccount.subscriptionIsNotSupported"));
      //   return;
      // }
      this.$router.push({
        path: "/transactionBuy",
        query: {
          fundIsin: data.fundIsin,
        },
      });
    },
    loginFund() {
      this.$jsBridge.run("startLogin", {
        type: "1A",
        callback: ({ login_state }) => {
          if (login_state) {
            refreshAppToken();
          }
        },
      });
    },
    // 下拉刷新
    onRefresh() {
      // console.log('刷新')
      // 下拉刷新时清除当前类型的缓存
      this.clearCurrentTypeCache()
      this.pageNum = 0;
      this.hasMore = true;
      this.$refs.scrollView.finishLoadMore()
      this.searchFundsList();
    },

    // 加载更多
    onEndReached() {
      console.log('加载更多', this.hasMore && !this.isLoading)
      if (this.hasMore && !this.isLoading) {
        this.pageNum++;
        this.searchFundsList(true);
      }
    },

    // 处理图表数据
    handleChartData(dataContent, isLoadMore) {
      let newArr = [];
      let startIndex = isLoadMore ? this.fundProductList.length - dataContent.length : 0;
      
      dataContent.forEach((ele, i) => {
        newArr.push([]);
        ele.trendChart.content.forEach((x) => {
          newArr[i].push({
            date: x[0],
            price: Number(x[1]),
            changePercent: this.currentTypeTab == "MONEY_MARKET" ? Number(x[2]) : Number(x[3]),
            annualised7Day: ele.annualised7Day,
            change: ele.change,
          });
        });
      });
      
      for (let i = 0; i < newArr.length; i++) {
        this.$nextTick(() => {
          this.drawCharts(
            startIndex + i,
            newArr[i],
            newArr[i][0].changePercent,
            newArr[i][0].annualised7Day,
            newArr[i][0].change
          );
        });
      }
    },
  },
};
</script>
<style scoped>
.md-button {
  width: 150px !important;
  height: 60px !important;
  padding: 14px 26px !important;
  font-size: 22px !important;
  font-weight: 500 !important;
}
</style>
<style lang="scss" scoped>

.find-kind-box-position {
  position: fixed;
  top: 0;
}
.fund-kind-box {
  height: calc(100% - 1px);
  overflow: hidden;
  .fund-scroll-view{
    overflow: hidden;
    height: calc(100vh - 1px);
  }
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  .dialog-main {
    background-color: rgba(0, 0, 0, 0.28);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog {
      width: 70%;
      @include themeify {
        background: themed("bg-color");
        color: themed("text-color");
      }
      padding: 50px 30px 140px 30px;
      border-radius: 4px;
      font-size: 28px;
      position: relative;
      .content-describe {
        text-align: center;
        line-height: 45px;
      }
      .button-bottom-risk {
        position: absolute;
        bottom: 0px;
        left: 0px;
        display: flex;

        div {
          border-radius: 4px 0px 0px 0px;
          @include themeify {
            background: themed("primary-color");
            color: themed("white-text");
          }
          width: 50%;
          text-align: center;
        }

        .first-class {
          border-radius: 0px 0px 0px 4px;
          @include themeify {
            background: themed("bg-btn-default");
            color: themed("second-text");
          }
        }
        width: 100%;
        line-height: 90px;
      }
    }
  }
  .screening-conditions {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1302;
  }
  .fund-type-tab {
    padding: 26px 30px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 28px;
    @include font_color(second-text);
    li {
      margin-right: 50px;
      &:last-child {
        margin-right: 0;
      }
    }
    .bottom-border {
      margin: 8px auto 0;
      width: 16px;
      height: 5px;
      border-radius: 3px;
    }
    .activeType {
      text-align: center;
      @include font_color(text-color);
      .activeBorder {
        @include background_color(fund-bg);
      }
    }
  }
  .fund-kind-search {
    padding: 0 30px 18px;
    @include background_color(second-bg);
    .fund-kind-left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .fund-kind-left-div {
        min-width: 120px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 24px;
      }
      .fund-kind-select {
        margin-right: 5px;
      }
      .fund-kind-sort {
        img {
          display: block;
          width: 11px;
          height: 8px;
        }
        .litreActive,
        .dropActive {
          background: red;
        }
      }
      .fund-kind-more-search {
        img {
          width: 17px;
          height: 16px;
        }
      }
    }
    .condition-time-box {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      @include background_color(mask-color);
      .condition-time {
        position: absolute;
        top: 165px;
        left: 30px;
        padding: 0 50px;
        font-size: 24px;
        border: 1px solid;
        border-radius: 2px;
        @include font_color(text-color);
        @include border_color(line-color);
        @include background_color(second-bg);
        p {
          padding: 20px 0;
        }
        .checkSelect {
          @include font_color(fund-bg);
        }
      }
    }
  }
  .fund-product-box {
    .fund-product-list {
      padding: 40px 30px 0 30px;
      @include background_color(second-bg);
      &:last-child {
        border: 0;
      }
      // height: 274px;
      .fund-product-list-box {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .fund-product-list-border {
        border-bottom: 1px solid;
        @include border_color(line-color);
        margin-top: 40px;
      }
      .fund-product-list-top,
      .fund-product-list-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .fund-product-list-top {
        margin-bottom: 45px;
        .list-top-left {
          // width: 169px;
          width: 25%;
          height: 83px;
          // overflow: hidden;
          canvas {
            width: 100% !important;
            height: 100% !important;
          }
        }
        .list-top-right {
          // width: 480px;
          width: 73%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          p {
            width: 100%;
            @include font_color(text-color);
            font-size: 28px;
            line-height: 34px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list-type {
            @include font_color(btn-color);
            font-size: 22px;
            display: flex;
            flex-wrap: wrap;
            margin-top: 20px;
            span {
              @include background_color(lam-bg);
              margin-right: 14px;
              padding: 8px 14px;
              border-radius: 2px;
              margin-bottom: 8px;
            }
            span {
              &:last-child {
                margin-right: 0;
                @include font_color(tag-text-yellow);
                @include background_color(warn-color);
              }
            }
          }
        }
        .list-right-width {
          width: 100%
        }
      }
      .fund-product-list-bottom {
        @include font_color(second-text);
        font-size: 22px;
        .list-bottom-left {
          // width: 169px;
          min-width: 25%;
          margin-right:10px;
          p {
            &:first-child {
              font-size: 32px;
              line-height: 32px;
              font-weight: normal;
            }
            &:last-child {
              // line-height: 24px;
              margin-top: 9px;
            }
          }
          .current-type {
            @include font_color(k-line-red);
          }
          .current-type1 {
            @include font_color(second-text);
          }
          .fund-type-price {
            @include font_color(k-line-green);
          }
        }
        .list-bottom-right {
          // width: 480px;
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          div {
            p {
              &:first-child {
                @include font_color(text-color);
                font-size: 32px;
                line-height: 32px;
              }
            }
            &:first-child {
              p {
                &:last-child {
                  margin-top: 9px;
                }
              }
            }
            .buy-button {
              @include background-color(primary-color);
              @include font_color(white-text);
              font-size: 22px;
              width: 140px;
              height: 50px;
              line-height: 50px;
              text-align: center;
              border-radius: 2px;
            }
          }
        }
        // .list-right-width {
        //   width: 100%
        // }
      }
    }
  }
  .fund-product-null {
    text-align: center;
    margin-top: 137px;
    img {
      width: 347px;
      height: 200px;
    }
  }
}
</style>
