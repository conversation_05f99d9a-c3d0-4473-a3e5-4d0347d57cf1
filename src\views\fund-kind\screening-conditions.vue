<template>
  <div class="index-page">
    <div class="backgroun-opacity"></div>
    <div class="search-box">
      <div class="title header-close-title">
        <span></span>
        <span class="title-content">{{ $t('select.screen') }}</span>
        <div class="close-png" @click="closeScreeningConditions">
          <img src="../../assets/images/search_close.png" alt="">
        </div>
        <!-- <md-icon
          class="close-png"
          @click="closeScreeningConditions"
          size="24px"
          name="close"
        ></md-icon> -->
      </div>
      <ul class="main">
        <!-- 交易货币 -->
        <li>
          <p class="title">{{ $t('select.transactionCurrency') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in transactionCurrency"
              :key="index"
              class="piece"
              :class="
                requestDetails.currencyType.includes(item.type)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectTransactionCurrency(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <!-- 风险等级 -->
        <li>
          <p class="title">{{ $t('select.riskLevel') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in riskLevel"
              :key="index"
              class="piece"
              :class="
                requestDetails.riskLevel.includes(item.type)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectRiskLevel(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <li>
          <p class="title">{{ $t('select.whetherToPayDividends') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in whetherToPayDividends"
              :key="index"
              class="piece"
              :class="
                requestDetails.dividend === item.type
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectWhetherToPayDividends(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <li>
          <p class="title">{{ $t('select.fundCompany') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in fundCompany"
              :key="index"
              class="piece"
              :class="
                requestDetails.fundCompanyIds.includes(item.companyId)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectFundCompany(item)"
            >
              {{fundsNameFun(item, 'name')}}
            </div>
          </div>
        </li>
      </ul>
      <div class="button-line">
        <div class="reset" @click="closeScreeningConditions('clear')">{{ $t('common.btns.reset') }}</div>
        <div class="confirm" @click="closeScreeningConditions('confirm')">
          {{ $t('fundMessage.affirmBtn') }}
        </div>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCompanies } from '@/services/fund'
import Loading from '@/components/loading/index.vue'
export default {
  name: 'screeningConditions',
  components: {
    Loading,
  },
  props: {
    isScreeningConditions: {
      type: Boolean,
    },
    currentTypeTab: {
      type: String,
    },
  },
  data() {
    return {
      transactionCurrency: [
        { type: 'HKD', content: 'hongKongDollar' },
        { type: 'USD', content: 'dollar' },
      ],
      riskLevel: [
        { type: 1, content: 'lowRisk' },
        { type: 2, content: 'mediumAndLowRisk' },
        { type: 3, content: 'mediumRisk' },
        { type: 4, content: 'mediumAndHighRisk' },
        { type: 5, content: 'highRisk' },
      ],
      whetherToPayDividends: [
        { type: true, content: 'dividend' },
        { type: false, content: 'noDividend' },
      ],
      fundCompany: [],
      requestDetails: {
        currencyType: [], // 交易货币
        riskLevel: [], // 风险等级
        dividend: '', // 是否派息
        fundCompanyIds: [],
      },
      isLoading: false,
    }
  },
  watch: {
    isScreeningConditions(newV, oldV) {
      if (newV) {
        this.isLoading = true
        getCompanies({
          page: 0,
          size: 999,
          exitsFunds: true,
        })
          .then((res) => {
            // console.log('基金公司===', res)
            if (res.code == 200) {
              this.fundCompany = res.data.content
            }
          })
          .catch((err) => {
            console.log(err)
          })
        this.isLoading = false
      }
    },
    currentTypeTab(newV, oldV) {
      // console.log('tab切换=====', newV, oldV)
      this.requestDetails = {
        currencyType: [],
        riskLevel: [],
        dividend: '',
        fundCompanyIds: [],
      }
    },
  },
  computed: {
    ...mapState(['locale']),
  },
  methods: {
    closeScreeningConditions(data) {
      switch (data) {
        case 'confirm':
          this.$emit('closeScreeningConditions', {
            isShow: false,
            requestDetails: this.requestDetails,
          })
          break
        case 'clear':
          this.requestDetails = {
            currencyType: [],
            riskLevel: [],
            dividend: '',
            fundCompanyIds: [],
          }
          break
        default:
          this.requestDetails = {
            currencyType: [],
            riskLevel: [],
            dividend: '',
            fundCompanyIds: [],
          }
          this.$emit('closeScreeningConditions', {
            isShow: false,
            requestDetails: this.requestDetails,
          })
          break
      }
    },
    selectTransactionCurrency(data) {
      // 交易货币
      // console.log(data);
      this.screenData(this.requestDetails.currencyType, data.type)
    },
    selectRiskLevel(data) {
      // 风险等级
      // console.log(data);
      this.screenData(this.requestDetails.riskLevel, data.type)
    },
    selectWhetherToPayDividends(data) {
      // 是否派息
      // console.log(data);
      this.requestDetails.dividend = data.type
    },
    selectFundCompany(data) {
      // 基金公司
      // console.log(data);
      this.screenData(this.requestDetails.fundCompanyIds, data.companyId)
    },

    // 筛选条件选择
    screenData(arr, data) {
      if (arr.includes(data)) {
        // 存在则删除
        if (arr.length > 1) {
          // 至少保留一个
          arr.forEach((ele, i) => {
            if (ele == data) {
              arr.splice(i, 1)
            }
          })
        }
      } else {
        // 不存在则添加
        arr.push(data)
      }
    },
    fundsNameFun(data) {
      if (this.locale == 'zh-hans') {
        return data.name && data.name.cn ? data.name.cn : '-'
      } else if (this.locale == 'zh-hant') {
        return data.name && data.name.hk ? data.name.hk : '-'
      } else {
        return data.name && data.name.us ? data.name.us : '-'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.index-page {
  @include themeify {
    // background-color: themed("bg-color");
    color: themed('text-color');
  }
  .backgroun-opacity {
    // height: 100px;
    height: 8%;
    background: #000;
    opacity: 0.3;
  }
  .search-box {
    // height: 92.5%;
    height: 92%;
    // overflow: auto;
    position: absolute;
    top: 8%;
    @include background_color(bg-color);
    .header-close-title {
      padding: 0 30px;
    }
    .title {
      text-align: center;
      line-height: 76px;
      font-size: 36px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .close-png {
        line-height: 1;
        @include themeify {
          color: themed('second-text');
        }
        display: inline-block;
        width: 30px;
        height: 30px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .main {
      height: 80%;
      overflow: auto;
      padding: 0px 30px;
      @include background_color(bg-color);
      li {
        padding-top: 24px;
        .title {
          @include themeify {
            color: themed('text-color');
          }
          font-weight: 400;
          line-height: 42px;
          text-align: left;
          font-size: 30px;
        }
        .content-button {
          display: flex;
          justify-content: left;
          flex-wrap: wrap;
          .piece {
            border: none;
            height: 72px;
            line-height: 72px;
            font-size: 24px;
            text-align: center;
            padding: 0px 50px;
            margin-right: 30px;
            margin-top: 30px;
            border-radius: 2px;
            max-width: calc(100% - 96px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .iselect-piece {
            @include themeify {
              background-color: themed('btn-color');
              color: themed('white-text');
            }
          }
          .noselect-piece {
            @include themeify {
              background-color: themed('btn-gray');
              color: themed('text-color');
            }
          }
        }
      }
    }
    .button-line {
      width: calc(100% - 60px);
      margin: 20px 30px;
      position: absolute;
      bottom: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // padding-bottom: 40px;
      // padding-top: 120px;
      @include background_color(bg-color);
      .reset {
        @include themeify {
          background-color: themed('list-bg');
          border-color: themed('primary-color');
          color: themed('primary-color');
        }
        border: 1px solid;
        width: 330px;
        height: 90px;
        text-align: center;
        line-height: 90px;
      }
      .confirm {
        @include themeify {
          background-color: themed('primary-color');
        }
        color: #fff;
        width: 330px;
        height: 90px;
        text-align: center;
        line-height: 90px;
      }
    }
  }
}
</style>
