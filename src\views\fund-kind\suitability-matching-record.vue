<template>
  <div class="container-level">
    <div class="bg-level bg-type">
      <h2>{{ !isMatched ? $t('myAccount.mismatch') : $t('myAccount.matched') }}</h2>
      <p class="name">{{ $t('myAccount.productName') }}</p>
      <p class="description">{{ productName }}</p>
    </div>
    <ul class="content-info">
      <li class="row-item">
        <div class="column1"></div>
        <div class="text border-right">{{ $t('risk.rowName2') }}</div>
        <div class="text">{{ $t('risk.rowName6') }}</div>
      </li>
      <li class="row-title">
        <div class="column1">{{ $t('risk.product') }}</div>
        <div class="text border-right">{{ format(this.currentLevel) }}</div>
        <div class="text">{{ $t('risk.need') }}</div>
      </li>
      <li class="row-item" v-if="myselfrisklevle">
        <div class="column1">{{ $t('risk.me') }}</div>
        <div class="text border-right">{{ format(this.myselfrisklevle) }}</div>
        <div v-if="isDerivativeKnowledge" class="text">{{ $t('risk.yes') }}</div>
        <div v-else class="text">{{ $t('risk.no') }}</div>
      </li>
      <li class="row-item borde-top" v-if="myselfrisklevle">
        <div class="column1">{{ $t('risk.match') }}</div>
        <div class="text border-right flex-center">
          <img v-if="this.myselfrisklevle < this.currentLevel" src="@/assets/images/match_no.png" alt="pic">
          <img v-else src="@/assets/images/match_yes.png" alt="">
        </div>
        <div class="text flex-center">
          <img v-if="!this.isDerivativeKnowledge" src="@/assets/images/match_no.png" alt="pic">
          <img v-else src="@/assets/images/match_yes.png" alt="">
        </div>
      </li>
    </ul>
    <div class="footer">
      <md-button @click="handleOpenDerivativeKnowledge" type="primary" v-if="!isDerivativeKnowledge">{{
        $t('risk.openStructured') }}</md-button>
      <md-button @click="onRestart" type="primary" v-else-if="this.myselfrisklevle < this.currentLevel">{{
        $t('risk.btnReEvaluate') }}</md-button>
      <md-button @click="onBack" type="primary" v-else>{{ $t('common.btns.back') }}</md-button>
    </div>

  </div>
</template>

<script>
// import RiskFooter from "../risk/component/Footer.vue";
import { mapState } from "vuex";
import { getFundInformation, getAccountRiskLevel } from "@/services/account.js";

export default {
  // components: { RiskFooter },
  data() {
    return {
      productName: '',
      showFooter: false,
      formValue: {},
      accountStatus: '',
      currentLevel: '',
      myselfrisklevle: false,
      isDerivativeKnowledge: false,
    }
  },
  computed: {
    isMatched() {
      return this.isDerivativeKnowledge && this.myselfrisklevle >= this.currentLevel
    }
  },
  destroyed() {
    document.removeEventListener('visibilitychange', this.handler)
  },
  mounted() {
    // this.$jsBridge.run('navBackByAppSelf', { isClosePage: true })
    this.handler()
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.handler()
      }
    });
  },
  methods: {
    handler() {
      this.getFundInformation()
      this.getMyselfrisklevle()
    },
    handleOpenDerivativeKnowledge() {
      // 跳转至开通结构性产品页面
      if (this.$jsBridge.isSupported('enterBusiness')) {
        this.$jsBridge.run('enterBusiness', {
          destination: 'structured'
        })
      }
    },
    onBack() {
      this.$jsBridge.isSupported('navBack') ? this.$jsBridge.run('navBack', { isClosePage: false }) : this.$router.go(-1);
    },
    format(num) {
      let text
      switch (num) {
        case 1:
          text = this.$t('risk.level1')
          break
        case 2:
          text = this.$t('risk.level2')
          break
        case 3:
          text = this.$t('risk.level3')
          break
        case 4:
          text = this.$t('risk.level4')
          break
        case 5:
          text = this.$t('risk.level5')
          break
        default:
          break
      }
      return text
    },
    onOpenAccount() {
      this.$router.push(`/risk/disclosure`);
    },
    onRestart() {
      this.$jsBridge.run('startEdWeb', {
        url:
          window.location.origin +
          `/risk/tips?isComplexProduct=1&id=${this.$route.query.id}&nav=0`,
        callback: () => { },
      })
    },
    getFundInformation() {
      getFundInformation(this.$route.query.id).then(res => {
        this.currentLevel = res.data.riskLevel;
        this.productName = localStorage.getItem("lang") == 'zh-hans' ? res.data.name.cn : (localStorage.getItem("lang") == 'zh-hant' ? res.data.name.hk : res.data.name.us);
      })
    },
    getMyselfrisklevle() {
      getAccountRiskLevel(this).then((res) => {
        this.myselfrisklevle = Number(res.data.result[1]);
        this.isDerivativeKnowledge = res.data.isDerivative;
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.container-level {
  padding: 0px 0px 108px 0px;
}

.bg-level {
  width: 100vw;
  height: 320px;
  overflow: hidden;
  background: url(~@/assets/images/match_bg.jpg) no-repeat center;
  background-size: 100%;
  box-sizing: border-box;
  padding-left: 60px;
  color: hsl(0, 0%, 100%);
  text-align: left;

  h2 {
    font-size: 46px;
    margin-top: 80px;
  }

  .name {
    margin-top: 20px;
    font-size: 28px;
  }

  .description {
    margin-top: 20px;
    line-height: 42px;
    font-size: 28px;
  }
}

.content-info {
  @include background_color(bg-color);
  margin: 30px 30px 0px 30px;
  border-radius: 4px;
  border: 1px solid;

  @include themeify {
    border-color: themed("line-color");
  }

  .row-title {
    @include background_color(list-bg);
    @extend .x-between-y-center;

    .column1 {
      @include themeify {
        color: themed("second-text");
        border-color: themed("line-color");
      }

      border-right: 1px solid;
      height: 78px;
      line-height: 78px;
      width: 152px;
      font-size: 22px;
      text-align: center;
    }

    .text {
      width: 270px;
      height: 78px;
      line-height: 78px;
      font-size: 26px;
      text-align: center;
      @include font_color(text-color);
      font: 400 DINPro-Regular, DINPro;
    }

    .border-right {
      @include themeify {
        border-color: themed("line-color");
      }

      border-right: 1px solid;
    }
  }

  .borde-top {
    @include themeify {
      border-color: themed("line-color");
    }

    border-top: 1px solid;
  }

  .row-item {
    @extend .x-between-y-center;

    .column1 {
      @include themeify {
        color: themed("second-text");
        border-color: themed("line-color");
      }

      border-right: 1px solid;
      height: 78px;
      line-height: 78px;
      width: 152px;
      text-align: center;
      font-size: 22px;
    }

    .border-right {
      @include themeify {
        border-color: themed("line-color");
      }

      border-right: 1px solid;
    }

    .flex-center {
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .text {
      width: 250px;
      height: 78px;
      line-height: 78px;
      font-size: 26px;
      text-align: center;
      @include font_color(text-color);
      font: 400 DINPro-Regular, DINPro;
    }
    .text:last-child{
      width: 300px;
    }
  }

}

.footer {
  width: calc(100% - 60px);
  margin: 80px 30px;
}
</style>