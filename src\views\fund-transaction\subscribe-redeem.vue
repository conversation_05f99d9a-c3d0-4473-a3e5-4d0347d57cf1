<template>
  <div class="subscribe-redeem-box">
    <subscribe-redeem
      v-if="isLoad"
      :orderData="orderData"
      :tradeRulesData="tradeRulesData"
    >
    </subscribe-redeem>
  </div>
</template>

<script>
import subscribeRedeem from "@/components/fundComponent/subscribe-redeem-state.vue";
import { getOrderData } from "@/services/account.js";
import { tradeRules } from "@/services/fund";
export default {
  components: {
    subscribeRedeem,
  },
  data() {
    return {
      isLoad: false,
      orderData: {},
      tradeRulesData: {},
    };
  },
  created() {
    this.getdata();
  },
  methods: {
    async getdata() {
      try {
        await getOrderData(this.$route.query.orderId).then((res) => {
          this.orderData = res.data;
          this.gettradeRules(this.orderData.fundIsin);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    gettradeRules(fundIsin) {
      tradeRules(fundIsin).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          this.isLoad = true;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.subscribe-redeem-box {
  height: 100%;
  // overflow: auto;
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  font-size: 28px;
}
</style>