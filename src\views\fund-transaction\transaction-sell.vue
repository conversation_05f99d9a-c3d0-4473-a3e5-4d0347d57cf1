<template>
  <div class="index-page">
    <template v-if="!isShowSalesAgreement">
      <loading v-if="isLoading"></loading>
      <ed-row
        :contentLeft="enumeration.accountName"
        :contentRight="accountContent"
      >
        <span>{{ $t(accountContent) }}</span>
      </ed-row>
      <ed-row :contentLeft="enumeration.productName">
        <span>{{ !productNameContent ? "--" : productNameContent }}</span>
      </ed-row>
      <ed-row :contentLeft="enumeration.yieldInRecentYear" style="border: none">
        <span>{{ yieldInRecentYearContent }}</span>
      </ed-row>
      <div class="bg-row"></div>
      <div class="row-money-page">
        <span>{{ $t("myAccount." + enumeration.tradingTitle) }}</span>
      </div>
      <!-- 申购赎回 输入金额 -->
      <div class="row-num-page">
        <span class="left">
          <md-input-item
            ref="input11"
            class="amout"
            :placeholder="
              isPlaceholderShow && !inputValue.length ? placeholder : ''
            "
            is-virtual-keyboard
            v-model="inputValue"
            virtual-keyboard-vm="myNumberKeyBoard"
          ></md-input-item>
          <span v-show="isKeyBoardShow" class="masker"></span>
        </span>
        <span class="content-right" @click="buyOrSell()">{{ $t("myAccount." + buyOrSellNum) }}</span>
      </div>
      <div
        class="
          md-example-child
          md-example-child-number-keyboard
          md-example-child-number-keyboard-1
        "
      >
        <md-number-keyboard
          ref="myNumberKeyBoard"
          v-model="isKeyBoardShow"
          @enter="onNumberEnter"
          @delete="onNumberDelete"
        >
        </md-number-keyboard>
      </div>

      <div class="row-num2-page">
        <span class="title">
          <span>{{ $t("myAccount." + currentQuantity) }}</span>
          <span>{{ MaximumRedemptionValue }}</span>
          <span>{{ company === "" ? "" : $t("myAccount." + company) }}</span>
        </span>
      </div>
      <div class="row-tip">
        <div>
          <span>
            {{ $t("myAccount.redemptionDescriptionlabel1")}}
            <!-- {{ tradeRulesData.confirmationPlusDay}} -->
            {{ tradeRulesData.navConfirmedDay }}
            {{ $t("myAccount.redemptionDescriptionlabelAfter1") }}
            <!-- {{ tradeRulesData.redemptionPlusDay}} -->
            {{ tradeRulesData.redemptionArriveDay}}
            {{ $t("myAccount.redemptionDescriptionlabelEnd1") }}
          </span>
        </div>
        <div>
          <span>{{ $t("myAccount.redemptionRate") }}</span>
          <span v-if="tradeRulesData.redemptionRatePreferential" class="red">{{
            (tradeRulesData.redemptionRatePreferential * 100).toFixed(2) + "%"
          }}</span>
          <span v-else class="red">{{
            (tradeRulesData.redemptionRate * 100).toFixed(2) + "%"
          }}</span>
        </div>
      </div>
      <div class="row-agree">
        <img
          :src="
            isAgree
              ? require(`../../assets/images/checkbox-checked.png`)
              : require(`../../assets/images/${theme}/checkbox.png`)
          "
          alt="pic"
          @click="agreeAgreement"
        />
        <span class="title">
          <span>{{ $t("myAccount.protocolDescription") }}</span>
        </span>
        <span class="content-right" @click="goToAalesAgreement">{{
          $t("myAccount.agreement")
        }}</span>
      </div>
      <div class="padding-20">
        <div @click="showPopUp('center')" class="sell-button">
          {{ $t("myAccount." + enumeration.title) }}
        </div>
      </div>
      <div class="page-title">
        <span class="text-right" @click="goToRules">{{
          $t("myAccount." + enumeration.rules)
        }}</span>
      </div>
      <BottomTips />
      <div class="dialog-main" v-show="isDialogShow">
        <div class="dialog">
          <div class="title">
            <!-- <img :src='require(`../../assets/images/${theme}/icon-close.png`)' alt="pic" @click="closeDialog('center')" /> -->
            <span class="text">{{ $t("myAccount.popUpTitle") }}</span>
          </div>
          <div class="num">
            <span class="text2">{{ submitShare }}</span>
            <span class="text1">{{ $t("myAccount.share") }}</span>
          </div>
          <div class="account">
            <span class="text1">{{ $t("myAccount.redeemTo") }}</span>
            <span class="text2">{{
              $t("myAccount.paymentAccountContent")
            }}</span>
          </div>
          <div class="product-name">
            <span class="text1">{{ $t("myAccount.productName") }}</span>
            <span class="text2">{{ productNameContent }}</span>
          </div>
          <div class="tips">
            <span>
              {{ $t("myAccount.redemptionBouncedExpectedT1")}}
              <!-- {{ tradeRulesData.redemptionPlusDay}} -->
              {{ tradeRulesData.redemptionArriveDay }}
              {{ $t("myAccount.redemptionBouncedExpectedTAfter1") }}
            </span>
          </div>
          <div class="password">
            <input
              id="password"
              v-model="passwordListNew"
              @input="setFontColor"
              class="input-box"
              type="password"
              :placeholder="$t('myAccount.passwordPrompt')"
            />
            <div
              class="eye-div"
              id="openEye"
              @click="openEye"
              style="display: none"
            >
              <img
                src="@/assets/images/transaction/open_eyes.png"
                class="eye-passord"
                alt="pic"
              />
            </div>
            <div class="eye-div" id="closeEye" @click="closeEye">
              <img
                src="@/assets/images/transaction/close_eyes.png"
                class="eye-passord"
                alt="pic"
              />
            </div>
          </div>
          <div class="password-prompt">
            <span v-show="isPasswordError" class="password-err">{{
              $t("myAccount.passwordMistake")
            }}</span>
            <span @click="submitResetPassword" class="password-forget">{{
              $t("myAccount.forgotPassword")
            }}</span>
          </div>
          <div class="bounced-operation">
            <div class="cancel" @click="closeDialog('center')">
              {{ $t("common.btns.cancel") }}
            </div>
            <div v-show="!passwordLegal" class="confim-disabled">
              {{ $t("myAccount.determine") }}
            </div>
            <div v-show="passwordLegal" class="confim" @click="submitPassword">
              {{ $t("myAccount.determine") }}
            </div>
          </div>
          <!-- <div class="md-example-child md-example-child-number-keyboard md-example-child-number-keyboard-1">
            <md-number-keyboard v-model="isKeyPasswordBoardShow" type="simple" @enter="onNumber2Enter"
                                @delete="onNumber2Delete"></md-number-keyboard>
          </div> -->
        </div>
      </div>
    </template>
    <!-- <div class="dialog-main-last" v-show="isDialogResetPassword">
      <div class="dialog">
        <div class="title">
          <span class="text">{{ $t("myAccount.resetPassword") }}</span>
        </div>
        <div class="font-line1">重置后的密码将发送至</div>
        <div class="font-line1">您的登记邮箱</div>
        <div class="font-line2">请在点击确认按钮5分钟后前往查看邮件{{userEmail}}</div>
        <div class="bounced-operation">
          <div class="cancel" @click="isDialogResetPassword=false">取消</div>
          <div class="confim" @click="submitResetPassword">确定</div>
        </div>
      </div>
    </div> -->
    <salesAgreement
      v-show="isShowSalesAgreement"
      :salesData="$route.query"
      @showAgreement="emitData"
    ></salesAgreement>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
import edRow from "../../components/common-row.vue";
import BottomTips from "@/components/BottomTips.vue";
import {
  NumberKeyboard,
  Button,
  InputItem,
  Codebox,
  Popup,
  PopupTitleBar,
  Icon,
} from "mand-mobile";
import {
  getFundInformation,
  getAccountMoney,
  purchase,
  getAccountNumber,
  getAccountEmail,
} from "@/services/account.js";
import { tradeRules } from "@/services/fund";
import { fmoney, getNowFormatDate } from "@/utils/util.js";
import { Toast } from "mand-mobile";
import loading from "@/components/loading/index";
import salesAgreement from "@/views/fund-transaction/sales-agreement";
export default {
  name: "orderRecord",
  computed: {
    ...mapState(["theme", "locale", "accountNumber"]),
  },
  data() {
    return {
      submitShare: 0,
      isLoading: false,
      passwordLegal: false,
      userEmail: "",
      isDialogResetPassword: false,
      isPasswordError: false,
      netRecognitionDate: "",
      tradeRulesData: {},
      minimumAmount: 0,
      isPlaceholderShow: true,
      redemptionConfirmationDescription: "",
      funddata: {},
      accountBalance: 600,
      MaximumRedemptionValue: 0,
      enumeration: {
        title: "",
        rules: "",
        accountName: "",
        productName: "myAccount.productName",
        tradingTitle: "",
        yieldInRecentYear: "myAccount.yieldInRecentYear",
      },
      placeholder: "",
      buyOrSellNum: "",
      currentQuantity: "",
      company: "",
      isAgree: false,
      isKeyPasswordBoardShow: true,
      dialogPassword: "",
      passwordList: [
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
      ],
      passwordListNew: "",
      isDialogShow: false,
      code: "",
      isPopupShow: {},
      isKeyBoardShow: false,
      accountContent: "myAccount.paymentAccountContent",
      productNameContent: "",
      yieldInRecentYearContent: "",
      inputValue: "",
      accountData: "",
      isShowSalesAgreement: false, // 协议组件
      newAccountNuber: "",
    };
  },
  components: {
    loading,
    salesAgreement,
    edRow,
    BottomTips,
    [NumberKeyboard.name]: NumberKeyboard,
    [InputItem.name]: InputItem,
    [Popup.name]: Popup,
    [PopupTitleBar.name]: PopupTitleBar,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Codebox.name]: Codebox,
  },

  async created() {
    let res = {};

    this.initializeData();
    if (!this.accountNumber) {
      res = await getAccountNumber(
        this,
        this.$jsBridge.isSupported("getAppInfo")
      );
      if (res.data.tradingAccountList && res.data.tradingAccountList.length) {
        let object = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.newAccountNuber = object.tradeAccountNumber;
      }
    } else {
      this.newAccountNuber = this.accountNumber;
    }
    this.getTradeRules();

    // 申购赎回 都查基金详情接口
  },
  methods: {
    submitResetPassword() {
      this.$jsBridge.run("resetPassword", {
        accountType: "funding",
        account: this.accountData.tradeAccountNumber,
        // callback: () => {
        //   Toast.info(this.$t('myAccount.'))
        // }
      });
    },
    forgetPassword() {
      getAccountEmail(this).then((res) => {
        this.accountData = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.userEmail = res.data.user.email;
        this.isDialogResetPassword = true;
      });
    },
    submitPassword() {
      this.isLoading = true;
      this.$jsBridge.run("startLogin", {
        type: "D",
        password: this.passwordListNew,
        callback: ({ login_state, account_type }) => {
          if (login_state) {
            this.submitOrder();
            this.isPasswordError = false;
          } else {
            this.isLoading = false;
            this.isPasswordError = true;
          }
        },
      });
    },
    pageJump() {
      this.$jsBridge.run("toPage", {
        jumpType: window.$App ? "H5" : "NATIVE",
        loginState: "JUDGMENT",
        openAccountState: "JUDGMENT_FUND",
        navigationContentCode: "BUSINESS_DEPOSIT",
        navigationUri: "BUSINESS_DEPOSIT",
        titleDisplay: "DISPALY",
      });
    },
    setFontColor() {
      this.isPasswordError = false;
      if (
        this.passwordListNew.length >= 6 &&
        this.passwordListNew.length < 16
      ) {
        this.passwordLegal = true;
      } else {
        this.passwordLegal = false;
      }
      if (
        this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "light"
      ) {
        document.getElementById("password").style.color = "#121C32";
      } else if (
        this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "dark"
      ) {
        document.getElementById("password").style.color = "#E5E5E5";
      } else if (
        !this.passwordListNew.length &&
        window.localStorage.getItem("theme") === "light"
      ) {
        document.getElementById("password").style.color = "#BDC0D0";
      } else {
        document.getElementById("password").style.color = "#5F616A";
      }
    },
    openEye() {
      document.getElementById("password").type = "password";
      document.getElementById("openEye").style.display = "none";
      document.getElementById("closeEye").style.display = "block";
    },
    closeEye() {
      document.getElementById("password").type = "text";
      document.getElementById("openEye").style.display = "block";
      document.getElementById("closeEye").style.display = "none";
    },
    goToAalesAgreement() {
      this.isShowSalesAgreement = true;
    },
    emitData(data) {
      if (data.isAgree === 1) {
        this.isAgree = true;
      } else {
        this.isAgree = false;
      }
      this.isShowSalesAgreement = false;
    },
    getTradeRules() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          let date = new Date(
            Date.parse(new Date()) +
              this.tradeRulesData.confirmationPlusDay * 24 * 60 * 60 * 1000
          );
          let month, day;
          if (date.getMonth() < 9) {
            month = "0" + (date.getMonth() + 1);
          }else {
            month = (date.getMonth() + 1);
          }
          if (date.getDate() < 10) {
            day = "0" + date.getDate();
          } else {
            day = date.getDate();
          }
          this.netRecognitionDate =
            "" + date.getFullYear() + "-" + month + "-" + day;
          this.getData();
        }
      });
    },
    onFakeInputBlur() {},
    initializeData() {
      // 初始化获取数据 获取当前是申购还是赎回
      this.MaximumRedemptionValue = this.$route.query.quantity;
      this.enumeration.title = "redeem";
      this.enumeration.rules = "redemptionRules";
      this.enumeration.tradingTitle = "redemptionShare";
      this.enumeration.accountName = "myAccount.redeemTo";
      this.buyOrSellNum = "allSell";
      this.currentQuantity = "redemptionQuantity";
      this.redemptionConfirmationDescription =
        this.$t("myAccount.expectedT") +
        getNowFormatDate() +
        this.$t("myAccount.redemptionConfirmationDescriptionRight");
    },
    async getData() {
      // 查询申购基金详情  名称 近一年收益  当前基金支持货币 最低申购金额 申购费率
      // 根据当前基金支持货币查询
      //  申购查询 当前货币账号余额
      // 赎回查询  查询当前可赎回份额
      try {
        await getFundInformation(this.$route.query.fundIsin).then((res) => {
          this.funddata = res.data;
          this.resloveData(res);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    resloveData(res) {
      let resFirst = res;
      let type = res.data.currencyType;
      this.MaximumRedemptionValue = fmoney(this.MaximumRedemptionValue, 0);
      this.resloveDataLast(resFirst);
    },
    resloveDataLast(res) {
      // 货币单位 请求对应货币单位的账户余额
      // 判断 申购还是赎回
      // 调用查询账户余额接口 或者是查份额接口
      // let type = res.data.currencyType;

      let monetaryUnit = "myAccount.yuan";
      if (res.data.currencyType === "CNY") {
        monetaryUnit = "myAccount.yuan";
        this.company = "yuan";
      } else if (res.data.currencyType === "HKD") {
        monetaryUnit = "myAccount.HKDollar";
        this.company = "HKDollar";
      } else if (res.data.currencyType === "USD") {
        monetaryUnit = "myAccount.dollar";
        this.company = "dollar";
      }
      // this.placeholder = this.$t("myAccount.sellPlaceholder") + Number(this.tradeRulesData.minRansom).toFixed(4);
      this.placeholder = this.$t("myAccount.sellPlaceholder") + this.tradeRulesData.minRansom;
      this.company = "share";
      // 判断当前国际化
      // this.productNameContent = (this.locale === 'zh-hans' ? res.data.name.cn : res.data.name.hk);
      if (this.locale == "zh-hans") {
        this.productNameContent =
          res.data.name && res.data.name.cn ? res.data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        this.productNameContent =
          res.data.name && res.data.name.hk ? res.data.name.hk : "-";
      } else {
        this.productNameContent =
          res.data.name && res.data.name.us ? res.data.name.us : "-";
      }
      // 收益率拼接百分号
      if (Number(res.data.cumulative1Y * 100) > 0) {
        this.yieldInRecentYearContent =
          "+" + Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      } else if (Number((res.data.cumulative1Y * 100).toFixed(2)) == 0) {
        this.yieldInRecentYearContent = "0.00%";
      } else {
        this.yieldInRecentYearContent =
          Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      }
    },
    goToRules() {
      this.$router.push({
        path: "/dealRule",
        query: {
          id: this.$route.query.fundIsin,
          type: 2,
        },
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    agreeAgreement() {
      this.isAgree = !this.isAgree;
    },
    buyOrSell() {
      this.inputValue = this.MaximumRedemptionValue.replace(/,/g, "");
    },
    closeDialog(e) {
      this.submitShare = 0;
      this.isDialogShow = false;
      this.isKeyPasswordBoardShow = false;
      this.isPasswordError = false;
      this.passwordLegal = false;
    },
    // showpassword(e) {
    //   this.isKeyPasswordBoardShow = true;
    // },
    // onNumber2Enter(val) {
    //   if (this.dialogPassword.length > 6) {
    //     return
    //   }
    //   this.dialogPassword += val
    //   let length = this.dialogPassword.length;
    //   this.passwordList.forEach((item, index) => {
    //     if (index < length) {
    //       item.isShow = true;
    //     }
    //   })
    //   if (this.dialogPassword.length === 6) {
    //     this.$jsBridge.run('startLogin', {
    //       type: 'D',
    //       password: this.dialogPassword,
    //       callback: ({ login_state, account_type }) => {
    //         console.log(login_state)
    //         if (login_state) {
    //           this.submitOrder();
    //         } else {
    //           Toast.succeed('Password error')
    //         }
    //       }
    //     })
    //   }
    // },
    // onNumber2Delete() {
    //   if (this.dialogPassword === '') {
    //     return
    //   }
    //   this.dialogPassword = this.dialogPassword.substr(0, this.dialogPassword.length - 1)
    //   let length = this.dialogPassword.length;
    //   this.passwordList.forEach((item, index) => {
    //     if (index >= length) {
    //       item.isShow = false;
    //     }
    //   })
    // },
    submitOrder() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          if (res.data.sellFlag) {
            this.submitOrderLast();
          } else {
            this.$router.go(-1);
            Toast.info(this.$t("myAccount.redemptionIsNotSupported"));
          }
        }
      });
    },
    async submitOrderLast() {
      // 暂时跳过密码校验 直接调用申购接口
      let parameter = {
        fundIsin: this.$route.query.fundIsin,
        orderType: "Sell",
        tradeAccount: this.newAccountNuber,
      };
      parameter.quantity = this.inputValue.replace(/|,/g, "");
      try {
        await purchase(parameter).then((res) => {
          
          this.isLoading = false;
          if(!res || res && res.code != 200){
            Toast.info(res.msg);
            return
          }
          this.submitShare = 0;
          this.isDialogShow = false;
          this.isKeyPasswordBoardShow = false;
          this.isPasswordError = false;
          this.passwordLegal = false;
          if (window.$App) {
            this.$jsBridge.run("navBack", {
              isClosePage: true,
              callback: () => {},
            });
          }
          this.$jsBridge.run("startEdWeb", {
            url:
              window.location.origin +
              `/subscribeRedeem?orderId=${res.data.orderId}&nav=0`,
            callback: () => {},
          });
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    showPopUp() {
      if (!this.isAgree) return Toast.info(this.$t("myAccount.pleaseChoose"));
      if (!this.inputValue)
        return Toast.info(this.$t("myAccount.redemptionPrompt"));

      // tradeRulesData  minHold  this.$route.query.quantity
      // 可与份额大于最小持有额  支持正常赎回 支持全部赎回  或者赎回后数量大于等于最小持有数量
      if (
        (Number(this.$route.query.quantity) <
          Number(this.tradeRulesData.minHold) ||
          Number(this.$route.query.quantity) <
            Number(this.tradeRulesData.minRansom)) &&
        Number(this.$route.query.quantity) != Number(this.inputValue)
      ) {
        return Toast.info(this.$t("myAccount.allTheRedemption"));
      } else if (
        (Number(this.tradeRulesData.minHold) <
          Number(this.$route.query.quantity) ||
          Number(this.tradeRulesData.minRansom) <
            Number(this.$route.query.quantity)) &&
        Number(this.inputValue) != Number(this.$route.query.quantity)
      ) {
        let num = this.$route.query.quantity - this.tradeRulesData.minHold;
        if (Number(this.inputValue) < Number(this.tradeRulesData.minRansom)) {
          return Toast.info(this.$t("myAccount.minimumRedeem"));
        }
        if (Number(this.inputValue) > Number(this.$route.query.quantity)) {
          return Toast.info(this.$t("myAccount.shareTooBig"));
        }
        if (Number(this.inputValue) > num) {
          console.log(this.inputValue, num, "ssss");
          return Toast.info(
            this.$t("myAccount.afterTheRedemptionShareIsSmall")
          );
        }
        // 可与份额小于最小持有额  只支持全部赎回
      } else if (
        Number(this.inputValue) != Number(this.$route.query.quantity)
      ) {
        console.log(
          Number(this.inputValue),
          Number(this.$route.query.quantity)
        );
        return Toast.info(this.$t("myAccount.afterTheRedemptionShareIsSmall"));
      }
      this.isDialogShow = true;
      // this.submitShare = Number(this.inputValue).toFixed(4);
      this.submitShare = this.inputValue;
      this.passwordListNew = "";
    },
    hidePopUp(type) {
      this.$set(this.isPopupShow, type, false);
    },
    onNumberEnter(val) {
      this.inputValue += val;
      this.$nextTick(() => {
        let num = this.tradeRulesData.unitPrecision;
        let numInteger, numDecimal;
        if (this.inputValue.split(".").length == 1) {
          numInteger = this.inputValue.split(".")[0];
          this.inputValue = numInteger.length > 10 ? numInteger.slice(0, 10) : numInteger;
        } else {
          numInteger = this.inputValue.split(".")[0];
          numDecimal = this.inputValue.split(".")[1];
          this.inputValue =
            numInteger.length > 10
              ? numDecimal.length > num
                ? numInteger.slice(0, 10) + "." + numDecimal.slice(0, num)
                : numInteger.slice(0, 10) + "." + numDecimal
              : numDecimal.length > num
              ? numInteger + "." + numDecimal.slice(0, num)
                : numInteger + "." + numDecimal;
        }
      });
    },
    onNumberDelete() {
      if (this.inputValue === "") {
        return;
      }
      this.inputValue = this.inputValue.replace(/,/g, "").substr(0, this.inputValue.length - 1);
    },
    enterNumbers() {
      let value = this.inputValue;
      this.inputValue = "";
      this.isPlaceholderShow = false;
      // this.$refs.scanTextbox.readOnly=true;
      this.isKeyBoardShow = true;
      // this.$refs.scanTextbox.readOnly=true;
      // this.$refs.scanTextbox.setAttribute('readonly', 'readonly')
      this.$refs.scanTextbox.setAttribute("readonly", "readonly") ||
        this.$refs.scanTextbox.trigger("blur");

      // setTimeout(() => {
      // this.$refs.scanTextbox.trigger('focus');
      // this.$refs.scanTextbox.removeAttribute('readonly') || this.$refs.scanTextbox.trigger('focus')
      // this.$refs.scanTextbox.readOnly=false;
      // this.inputValue = value;
      // }, 0);
      this.inputValue = value;
    },
    closeEnterNumbers() {
      this.isPlaceholderShow = true;
      this.isKeyBoardShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  width: 100%;
  height: 100%;
  ::v-deep .md-input-item-fake-placeholder {
    left: 0px;
    font-size: 32px;
    @include themeify {
      border-right-color: themed("second-text");
    }
  }
  ::v-deep .md-input-item-fake {
    height: 100px;
    padding: 0px;
    font-size: 80px;
  }
  ::v-deep .md-input-item-fake:after {
    @include themeify {
      border-right-color: themed("primary-color");
    }
  }
  ::v-deep .md-number-keyboard .delete {
    @include themeify {
      background-color: themed("bg-color");
      color: themed("text-color");
    }
  }
  ::v-deep .md-field-item-content::before {
    border: none !important;
  }
  ::v-deep .md-field-item-content {
    width: 300px;
  }
  ::v-deep .md-field-item-content:before {
    border: none;
  }
  ::v-deep .md-number-keyboard .slidedown {
    @include themeify {
      background-color: themed("bg-color");
      color: themed("text-color");
    }
  }
  ::v-deep .md-number-keyboard .confirm {
    // border: 1px solid;
    @include themeify {
      background-color: themed("primary-color");
    }
  }
  ::v-deep .md-number-keyboard .keyboard-number-item {
    // border: 1px solid;
    @include themeify {
      background-color: themed("bg-color");
      color: themed("text-color");
      // border-color: themed("line-color");
    }
  }
  ::v-deep .md-number-keyboard .md-popup-box {
    // padding-top: 1px;
    padding-bottom: 40px;
    @include themeify {
      background-color: themed("bg-color");
    }
  }
  ::v-deep .md-number-keyboard .md-popup-box:after {
    border-top: 0px solid;
    @include themeify {
      border-top-color: themed("line-color");
    }
  }
  // ::v-deep .md-number-keyboard-container .keyboard-number {
  //   @include themeify {
  //     background: themed("bg-color");
  //   }
  // }
  // ::v-deep .md-number-keyboard-container .keyboard-number .keyboard-number-list {
  //   @include themeify {
  //     background: themed("bg-color");
  //   }
  // }
  ::v-deep .md-number-keyboard-container:after {
    @include themeify {
      border-top-color: themed("line-color");
    }
  }
  ::v-deep
    .md-number-keyboard-container
    .keyboard-number
    .keyboard-number-list
    .keyboard-number-item:before {
    @include themeify {
      border-right-color: themed("line-color");
    }
  }
  ::v-deep
    .md-number-keyboard-container
    .keyboard-number
    .keyboard-number-list
    .keyboard-number-item:after {
    @include themeify {
      border-top-color: themed("line-color");
    }
  }
  ::v-deep .md-field-item.is-solid .md-field-item-title {
    width: 60px;
  }
  @include themeify {
    background-color: themed("bg-color");
    color: themed("text-color");
  }
  .dialog-main {
    background-color: rgba(0, 0, 0, 0.28);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;

    .dialog {
      position: absolute;
      top: 15%;
      left: 30px;
      @include themeify {
        background: themed("bg-color");
        color: themed("text-color");
      }
      border-radius: 4px;
      width: calc(100% - 60px);
      font-size: 32px;

      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        img {
          z-index: 5000;
          width: 32px;
          height: 32px;
          position: absolute;
          top: 50%;
          right: 30px;
          margin-top: -16px;
        }
        .text {
          line-height: 132px;
        }
      }
      .num {
        text-align: center;
        vertical-align: bottom;
        .text1 {
          font-size: 28px;
          line-height: 42px;
          padding-right: 10px;
        }
        .text2 {
          font-size: 80px;
          line-height: 100px;
        }
      }
      .account {
        width: calc(100% - 100px);
        margin: 0 50px;
        margin-top: 27px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26px;
        line-height: 52px;
        .text1 {
          @include themeify {
            color: themed("second-text");
          }
        }
      }
      .product-name {
        width: calc(100% - 100px);
        margin: 0 50px;
        padding: 14px 0px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-size: 26px;
        line-height: 40px;
        vertical-align: top;
        .text1 {
          @include themeify {
            color: themed("second-text");
          }
        }
        .text2 {
          display: inline-block;
          white-space: pre-wrap;
          width: 410px;
          text-align: right;
        }
      }
      .row-tip {
        @include themeify {
          background-color: themed("lam-bg");
          color: themed("primary-color");
        }
        width: calc(100% - 100px);
        margin: 0px 50px;
        font-size: 24px;
        line-height: 32px;
        padding: 12px 16px;
        .red {
          @include themeify {
            color: themed("k-line-red");
          }
        }
      }
      .tips {
        @include themeify {
          color: themed("primary-color");
          background-color: themed("lam-bg");
        }
        width: calc(100% - 100px);
        margin: 13px 50px;
        padding-left: 16px;
        text-align: left;
        font-size: 24px;
        line-height: 56px;
      }
      .password {
        width: calc(100% - 100px);
        margin: 40px 50px 0px 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        .item {
          @include themeify {
            background-color: themed("lam-bg");
          }
          width: 80px;
          height: 80px;
          display: flex;
          justify-content: center;
          align-items: center;
          .circular {
            @include themeify {
              background-color: themed("text-color");
            }
            width: 18px;
            height: 18px;
            border-radius: 50% 50%;
          }
        }
        .eye-div {
          @include themeify {
            background-color: themed("bg-color");
          }
          position: absolute;
          right: 1px;
          top: 1px;
          width: 68px;
          height: 88px;
          .eye-passord {
            position: absolute;
            right: 32px;
            top: 34px;
            width: 41px;
            height: 20px;
          }
        }
        .input-box {
          -webkit-user-select: text !important;
          -moz-appearance: button; /* Firefox */
          -webkit-appearance: button; /* Safari 和 Chrome */
          appearance: button;
          @include themeify {
            background: themed("bg-color");
            color: themed("tip-text");
            border-color: themed("input-line");
          }
          border: 1px solid;
          outline: none;
          font-size: 28px;
          padding-left: 30px;
          width: 100%;
          border-radius: 2px;
          padding: 30px 80px 26px 30px;
          &:focus {
            // outline: none;
            // border: none;
            // width: 80px;
            @include themeify {
              color: themed("text-color");
              border-color: themed("primary-color");
            }
          }
        }
      }
      .password-prompt {
        width: calc(100% - 100px);
        margin: 0 50px;
        line-height: 64px;
        font-size: 28px;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .password-err {
          position: absolute;
          top: 0px;
          left: 0px;
          @include themeify {
            color: themed("error-color");
          }
        }
        .password-forget {
          @include themeify {
            color: themed("primary-color");
          }
        }
      }
      .bounced-operation {
        margin-top: 28px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        div {
          width: 50%;
          line-height: 90px;
          text-align: center;
          font-size: 28px;
        }
        .cancel {
          @include themeify {
            background-color: themed("bg-btn-default");
            color: themed("second-text");
          }
          border-radius: 0px 0px 0px 4px;
        }
        .confim-disabled {
          @include themeify {
            background-color: themed("btn-second");
            color: themed("white-text");
          }
          border-radius: 4px 0px 0px 0px;
        }
        .confim {
          @include themeify {
            background-color: themed("primary-color");
            color: themed("white-text");
          }
          border-radius: 4px 0px 0px 0px;
        }
      }
    }
  }
  .dialog-main-last {
    background-color: rgba(0, 0, 0, 0.28);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    .dialog {
      position: absolute;
      top: 235px;
      left: 30px;
      @include themeify {
        background: themed("bg-color");
        color: themed("text-color");
      }
      border-radius: 4px;
      width: calc(100% - 60px);
      font-size: 32px;

      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        .text {
          line-height: 132px;
        }
        margin-bottom: 10px;
      }
      .font-line1 {
        font-size: 28px;
        @include themeify {
          background: themed("bg-color");
          color: themed("second-text");
        }
        text-align: center;
        line-height: 40px;
      }
      .font-line2 {
        font-size: 28px;
        @include themeify {
          background: themed("bg-color");
          color: themed("tag-text-yellow");
        }
        text-align: center;
        line-height: 58px;
      }
      .bounced-operation {
        margin-top: 55px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        div {
          width: 50%;
          line-height: 90px;
          text-align: center;
          font-size: 28px;
        }
        .cancel {
          @include themeify {
            background-color: themed("bg-btn-default");
            color: themed("second-text");
          }
          border-radius: 0px 0px 0px 4px;
        }
        &:last-child {
          @include themeify {
            background-color: themed("primary-color");
            color: themed("white-text");
          }
          border-radius: 4px 0px 0px 0px;
        }
      }
    }
  }
  .page-title {
    width: 100%;
    height: 88px;
    line-height: 88px;
    text-align: center;
    font-size: 36px;
    font-weight: 400;
    .text-right {
      @include themeify {
        color: themed("tag-text-yellow");
      }
      text-align: center;
      font-size: 28px;
      line-height: 88px;
    }
  }
  .bg-row {
    @include themeify {
      background: themed("list-bg");
    }
    height: 20px;
  }
  .row-money-page {
    width: calc(100% - 60px);
    margin: 0px 30px;
    margin-bottom: 44px;
    font-size: 34px;
    font-weight: Medium;
    line-height: 90px;
  }
  .row-num-page {
    @include themeify {
      border-bottom-color: themed("line-color");
    }
    width: calc(100% - 60px);
    position: relative;
    margin: 0px 30px;
    border-bottom: 1px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
    vertical-align: top;
    font-size: 32px;
    // .title {
    //   position: absolute;
    //   top: 0px;
    //   left: 0px;
    //   font-size: 32px;
    //   // padding-right: 30px;
    // }
    .left {
      display: inline-block;
      position: relative;
      width: calc(100% - 130px);
      .amout {
        width: 100%;
      }
      ::v-deep .md-field-item-content {
        width: 100%;
      }
      .placeholder {
        @include themeify {
          background: themed("bg-color");
          color: themed("second-text");
        }
        border: none;
        outline: none;
        font-size: 32px;
        width: 450px;
        overflow: hidden;
        &:focus {
          // outline: none;
          // border: none;
          // width: 80px;
          @include themeify {
            color: themed("text-color");
          }
          font-size: 80px;
          line-height: 80px;
        }
      }
      .masker {
        position: absolute;
        left: 90px;
        top: 0px;
        width: 450px;
        height: 100px;
        background-color: chartreuse;
        z-index: 6000;
        opacity: 0;
      }
    }
    .content-right {
      @include themeify {
        color: themed("link-text");
      }
      text-align: right;
    }
  }
  .row-num2-page {
    width: calc(100% - 60px);
    margin: 0px 30px;
    font-size: 28px;
    line-height: 88px;
    .title {
      @include themeify {
        color: themed("second-text");
      }
    }
    .content-right {
      @include themeify {
        color: themed("tag-text-yellow");
      }
      padding-left: 20px;
    }
  }
  .row-tip {
    @include themeify {
      background-color: themed("lam-bg");
      color: themed("primary-color");
    }
    width: calc(100% - 60px);
    margin: 0px 30px;
    font-size: 24px;
    line-height: 32px;
    padding: 12px 16px;
    .red {
      @include themeify {
        color: themed("k-line-red");
      }
    }
  }
  .padding-20 {
    margin: 0px 20px;
    .sell-button {
      @include themeify {
        background-color: themed("tag-text-yellow");
        color: themed("white-text");
      }
      width: 100%;
      height: 90px;
      line-height: 90px;
      font-size: 28px;
      font-weight: 400;
      border-radius: 2px;
      text-align: center;
    }
  }
  .row-agree {
    width: calc(100% - 60px);
    margin: 40px 30px;
    font-size: 24px;
    line-height: 50px;
    img {
      width: 30px;
      height: 30px;
      margin-right: 20px;
      vertical-align: middle;
    }
    .title {
      @include themeify {
        color: themed("second-text");
      }
    }
    .content-right {
      @include themeify {
        color: themed("link-text");
      }
    }
  }
}
</style>
