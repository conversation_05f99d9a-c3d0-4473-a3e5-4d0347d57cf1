<template>
  <div class="index-page">
    <div class="fixed">
      <div class="income-card">
        <img
          class="show-pic-left"
          src="@/assets/images/international/account_funds.png"
          alt="pic"
        />
        <span class="padding-20">{{ $t("myAccount.transaction") + "(" + parameter.currency + ")" }}</span>
        <img 
          :src="require(`@/assets/images/international/arrow1_${currencyShow ? 'up' : 'down'}_default.png`)"
          class="show-pic-right"
          alt="pic"
          @click="currencyShow = !currencyShow"
        />
      </div>
      <div v-if="currencyShow" class="currencyPopup">
        <div
          v-for="(item, index) in currencyList"
          :key="index"
          @click="currencyChoose(item.key)"
          :class="[parameter.currency === item.key ? 'active' : 'currencyItem']"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="select-card">
        <span @click="timeClick">{{ datePickerValue ? datePickerValue : $t("myAccount.allDates") }}
          <img
              class="show-pic-left"
              :src="require(`@/assets/images/international/arrow1_${isPopupShow ? 'up' : 'down'}_default.png`)"
              alt="pic"
          />
        </span>
        <span class="reset" @click="resetClick" v-if="datePickerValue">{{
          $t("common.btns.reset")
        }}</span>
      </div>
    </div>
    <div class="list-Item">
      <div v-for="(item, index) in accountdata" :key="index">
        <div class="time-flow-card">
          <div class="date">{{ moment(item.month).format("yyyy-MM") }}</div>
          <div class="text">
            <span class="label">{{ $t("myAccount.intoFlow") }}：</span>
            <span class="padding-50">{{
              fmoney(Number(item.inflow), 2) + parameter.currency
            }}</span>
            <span class="label">{{ $t("myAccount.outFlow") }}：</span>
            <span>{{
              fmoney(Number(item.outflow), 2) + parameter.currency
            }}</span>
          </div>
        </div>
        <div
          class="card-text"
          v-for="(item2, index2) in item.flows"
          :key="index2"
        >
          <div class="title-num">
            <span class="title-left">{{ fundsNameFun(item2.typeDesc) }}</span>
            <span class="num-right"
              >{{ (item2.direction === "INFLOW") ? "+" : (item2.typeDesc.cn === "入金" || item2.typeDesc.cn === "出金" ? "" :"-")
              }}{{ fmoney(Number(item2.amount), 2) }}</span
            >
          </div>
          <div class="describe">
            {{
              item2.typeDesc.cn === "入金" || item2.typeDesc.cn === "出金"
                ? "-"
                : ""
            }}{{ fundsNameFun(item2.description) }}
          </div>
          <div class="date">
            {{ moment(item2.transTime).format("MM-DD HH:mm") }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!accountdata.length" class="fund-product-null">
      <noData></noData>
    </div>
    <md-popup v-model="isPopupShow" position="bottom">
      <div class="timeCheck">
        <span class="timeCheck-left" @click="isPopupShow = false">{{
          $t("common.btns.cancel")
        }}</span>
        <span class="timeCheck-right" @click="hidePopUp()">{{
          $t("common.btns.affirm")
        }}</span>
      </div>
      <div>
        <md-date-picker
          ref="datePicker"
          type="custom"
          :min-date="minDate"
          :max-date="maxDate"
          :textRender='textRender'
          :custom-types="['yyyy', 'MM']"
          :default-date="currentDate"
          is-view
          keep-index
        ></md-date-picker>
      </div>
    </md-popup>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { capitalFlowsList } from "@/services/account";
import { fmoney } from "@/utils/util";
import moment from "moment";
import { Popup, PopupTitleBar, Button, DatePicker, Toast } from "mand-mobile";
import { getAccountNumber } from "@/services/account";
import noData from "@/components/international/common/no-data";
export default {
  name: "interCapitalFlow",
  components: {
    [Popup.name]: Popup,
    [PopupTitleBar.name]: PopupTitleBar,
    [Button.name]: Button,
    [DatePicker.name]: DatePicker,
    noData
  },
  data() {
    return {
      inflow: 0,
      flowOut: 0,
      isPopupShow: false,
      currencyShow: false,
      datePickerValue: "",
      minDate: new Date(new Date().getTime() - 24 * 365 * 60 * 60 * 1000 * 2),
      maxDate: new Date(),
      currentDate: "",
      currencyType: "myAccount.financialAccount",
      currencyList: [
        { text: this.$t("fundMessage.currencyType.HKD"), key: "HKD" },
        { text: this.$t("fundMessage.currencyType.USD"), key: "USD" },
      ],
      accountdata: [],
      parameter: {
        currency: "HKD",
      },
      monthMap: {
        1: 'January',
        2: 'February',
        3: 'March',
        4: 'April',
        5: 'May',
        6: 'June',
        7: 'July',
        8: 'August',
        9: 'September',
        10: 'October',
        11: 'November',
        12: 'December',
      },
    };
  },
  created() {
    getAccountNumber(this, this.$jsBridge.isSupported("getAppInfo")).then(
      (res) => {
        let object = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        this.capitalFlow();
      }
    );
    this.currentDate = new Date();
  },
  computed: {
    ...mapState(["theme", "accountNumber", "locale"]),
  },
  methods: {
    moment,
    fmoney,
    timeClick() {
      this.isPopupShow = true;
      this.currentDate = new Date();
    },
    currencyChoose(str) {
      this.parameter.currency = str;
      this.currencyShow = false;
      this.capitalFlow();
    },
    hidePopUp() {
      this.isPopupShow = false;
      this.datePickerValue = this.$refs.datePicker.getFormatDate("yyyy年MM月");
      const year = this.$refs.datePicker.getFormatDate("yyyy");
      const month = this.$refs.datePicker.getFormatDate("MM");
      const day = this.getDaysInMonth(year, month);
      this.parameter.transDateRangeStart = year + "-" + month + "-" + "01";
      this.parameter.transDateRangeEnd = year + "-" + month + "-" + day;
      this.capitalFlow();
    },
    getDaysInMonth(year, month) {
      month = parseInt(month, 10); //parseInt(number,type)这个函数后面如果不跟第2个参数来表示进制的话，默认是10进制。
      var temp = new Date(year, month, 0);
      return temp.getDate();
    },
    resetClick() {
      delete this.parameter.transDateRangeStart;
      delete this.parameter.transDateRangeEnd;
      this.datePickerValue = "";
      this.capitalFlow();
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data && data.cn ? data.cn : "-";
      } else if (this.locale == "zh-hant") {
        return data && data.hk ? data.hk : "-";
      } else {
        return data && data.us ? data.us : "-";
      }
    },
    async capitalFlow() {
      this.accountdata = [];
      console.log("this.accountNumber", this.accountNumber);
      const res = await capitalFlowsList(
        this.accountNumber,
        this.parameter,
        this
      );
      if (res.code === "200") {
        if (!res.data.monthlyFlows.length) {
          return Toast.info(this.$t("fundMessage.notMessage"));
        } else {
          this.accountdata = res.data.monthlyFlows;
        }
        this.inflow =
          this.fmoney(Number(res.data.inflow), 2) + this.parameter.currency;
        this.flowOut =
          this.fmoney(Number(res.data.outflow), 2) + this.parameter.currency;
      }
    },
    textRender() {
      // eslint-disable-next-line prefer-rest-params
      const args = Array.prototype.slice.call(arguments);
      const typeFormat = args[0]; // 类型
      const column0Value = args[1]; // 第1列选中值
      const column1Value = args[2]; // 第2列选中值
      const column2Value = args[3]; // 第3列选中值
      if (typeFormat === 'yyyy') {
        return `${column0Value}${this.$t('common.year')}`;
      }
      if (typeFormat === 'MM') {
        if (this.locale === 'en') {
          return `${this.monthMap[column1Value]}`;
        }
        return `${column1Value}${this.$t('common.month')}`;
      }
      if (typeFormat === 'dd') {
        return `${column2Value}${this.$t('common.day')}`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: Medium;
    .return {
      width: 16px;
      height: 30px;
      display: inline-block;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .income-card {
    margin: 0 32px;
    padding: 24px 0 24px;
    position: relative;
    text-align: left;
    font-size: 28px;
    line-height: 44px;
    .padding-20 {
      padding-left: 50px;
    }
    .show-pic-left {
      width: 36px;
      height: 36px;
      position: absolute;
      top: 50%;
      margin-top: -18px;
      left: 0px;
    }
    .show-pic-right {
      width: 36px;
      position: absolute;
      top: 0;
      right: 0px;
      margin-top: 18px;
    }
  }
  .select-card {
    padding: 22px 32px;
    font-size: 30px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text_1st);
    font-weight: 500;
    line-height: 46px;
    font-weight: bold;
    .show-pic-left {
      width: 36px;
      height: 36px;
      margin-left: 20px;
      vertical-align: middle;
    }
    .reset {
      background: var(--background);
      display: inline-block;
      width: 128px;
      height: 56px;
      border-radius: 52px;
      border: 1px solid var(--line_01);
      font-size: 24px;
      color: var(--text_1st);
      text-align: center;
      line-height: 56px;
    }
  }
  .time-flow-card {
    background: var(--gray_05);
    width: 100%;
    padding: 20px 32px;
    color: var(--text_1st);
    .date {
      line-height: 48px;
      text-align: left;
      font-size: 32px;
      margin-bottom: 8px;
      color: var(--text_1st);
    }
    .text {
      line-height: 36px;
      text-align: left;
      font-size: 24px;
      color: var(--text_1st);
      .label{
        color: var(--text_2nd);
      }
      .padding-50 {
        padding-right: 50px;
      }
    }
  }
  .card-text {
    margin: 0px 32px;
    border-bottom: 1px solid var(--line_01);
    padding: 24px 0;
    &:last-child {
      border-bottom: none;
    }
    .title-num {
      display: flex;
      justify-content: space-between;
      line-height: 48px;
      color: var(--text_1st);
      margin-bottom: 4px;
      .title-left {
        font-size: 28px;
      }
      .num-right {
        font-size: 32px;
      }
    }
    .describe {
      color: var(--text_3rd);
      line-height: 36px;
      font-size: 24px;
      margin-bottom: 12px;
    }
    .date {
      color: var(--text_3rd);
      line-height: 36px;
      font-size: 24px;
    }
  }
  .timeCheck {
    display: flex;
    justify-content: space-between;
    padding: 40px;
    font-size: 28px;
    background: var(--background);
    border-radius: 40px 40px 0 0 ;
    .timeCheck-left {
      color: var(--text_3rd);
    }
    .timeCheck-right {
      color: var(--text_1st);
    }
  }
  .currencyPopup {
    position: absolute;
    top: 70px;
    right: 32px;
    z-index: 2999;
    font-size: 28px;
    border-radius: 4px;
    background: var(--background);
    box-shadow: 0px 0px 8px 0px rgba(28,33,42,0.1);
    border-radius: 30px;
    text-align: center;
    padding: 18px 0;
    color: var(--text_3rd);
    &:before{
      position: absolute;
      top: -0.15rem;
      left: 0.5rem;
      content: "";
      border-bottom: 15px solid var(--background);
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
    }
    .currencyItem {
      padding: 18px 40px;
    }
    .active {
      padding: 18px 40px;
      color: var(--brand_01);
    }
  }
  ::v-deep .md-picker-column-item .column-list .column-item.active {
    color: var(--text_1st);
  }
  .fund-product-null {
    text-align: center;
    margin-top: 137px;
    img {
      width: 347px;
      height: 200px;
    }
  }
  .fixed {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--background);
  }
  .list-Item {
    padding-top: 192px;
  }
}
</style>
