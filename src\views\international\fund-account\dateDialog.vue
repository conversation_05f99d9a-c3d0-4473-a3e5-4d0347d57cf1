<template>
  <div class="bottom-body">
    <div class="confim">
      <span class="text-left" @click="cancel">{{$t("myAccount.cancell")}}</span>
      <span @click="submit">{{$t("myAccount.confim")}}</span>
    </div>
    <div class="display">
      <div :class="currentIndex === 7?'bg-select':''">
        <span @click="selectTime(7)">{{$t("myAccount.all")}}</span>
      </div>
      <div @click="selectTime(30)" :class="currentIndex === 30?'bg-select':''">
        <span>{{$t("myAccount.lastMonth")}}</span>
      </div>
      <div @click="selectTime(90)" :class="currentIndex === 90?'bg-select':''">
        <span>{{$t("myAccount.lastThreeMonth")}}</span>
      </div>
      <div @click="selectTime(365)" :class="currentIndex === 365?'bg-select':''">
        <span>{{$t('myAccount.nearlyYear')}}</span>
      </div>
    </div>
    <div class="display-input">
      <div :class="isActiveInput1 ? 'input active' : 'input'" @click="selectTimeStart">{{$t("myAccount.startTime")}}</div>
      <div class="gun">～</div>
      <div :class="isActiveInput2 ? 'input active' : 'input'" @click="selectTimeEnd">{{$t("myAccount.endTime")}}</div>
    </div>
    <md-date-picker
      class="datePicker"
      ref="datePicker"
      :today-text="$t('myAccount.today')"
      :min-date="minDate"
      :max-date="maxDate"
      :textRender='textRender'
      :default-date="currentDate"
      is-view
      @change="onDatePickerchange"
      :keep-index="true"
    ></md-date-picker>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { DatePicker,Toast } from "mand-mobile";
import moment from "moment";
export default {
  name: "inter-date-picker-demo",
  components: {
    [DatePicker.name]: DatePicker,
  },
  data() {
    return {
      isActiveInput1: false,
      isActiveInput2: false,
      allFlag: false,
      currentYear: '',
      currentMonth: '',
      currentDay: '',
      beginningOrEnd: '',
      currentIndex: '',
      isPopupShow: {},
      minDate: "",
      maxDate: "",
      currentDate: "",
      startTime: '开始时间',
      endTime: '结束时间',
      monthMap: {
        1: 'January',
        2: 'February',
        3: 'March',
        4: 'April',
        5: 'May',
        6: 'June',
        7: 'July',
        8: 'August',
        9: 'September',
        10: 'October',
        11: 'November',
        12: 'December',
      },
    };
  },
  computed: {
    ...mapState(["locale"]),
  },
  props:["createdDate"],
  created() {
    this.$nextTick(() => {
      let num = new Date(this.createdDate).valueOf();
      let num2 = new Date().getTime() - 24*365*60*60*1000
      if(num < num2) {
        this.minDate = new Date(num);
      }
    });
    if(this.createdDate)
    this.minDate = new Date(new Date().getTime() - 24*365*60*60*1000);
    this.maxDate = new Date();
    this.currentDate = new Date();
  },
  methods: {
    textRender() {
      // eslint-disable-next-line prefer-rest-params
      const args = Array.prototype.slice.call(arguments);
      const typeFormat = args[0]; // 类型
      const column0Value = args[1]; // 第1列选中值
      const column1Value = args[2]; // 第2列选中值
      const column2Value = args[3]; // 第3列选中值
      if (typeFormat === 'yyyy') {
        return `${column0Value}${this.$t('common.year')}`;
      }
      if (typeFormat === 'MM') {
        if (this.locale === 'en') {
          return `${this.monthMap[column1Value]}`;
        }
        return `${column1Value}${this.$t('common.month')}`;
      }
      if (typeFormat === 'dd') {
        return `${column2Value}${this.$t('common.day')}`;
      }
    },
    cancel() {
      this.$emit('cancel');
      this.isActiveInput1 = false;
      this.isActiveInput2 = false;
    },
    submit() {
      let parameter = {};
      if(this.startTime == '开始时间' || this.endTime == '结束时间') {
        Toast.info(this.$t("myAccount.pleaseSelectStartTimeAndEndTime"));
        return
      }
      parameter.startTime = new Date(this.startTime).getTime();
      parameter.endTime = new Date(this.endTime).getTime();
      if(parameter.startTime > parameter.endTime) {
        Toast.info(this.$t("myAccount.endTimeCannotBeLessThanStartTime"));
        return
      }
      this.isActiveInput1 = false;
      this.isActiveInput2 = false;
      this.$emit('timePassed',{startTime:this.startTime,endTime:this.endTime,allFlag:this.allFlag});
    },
    selectTime(num) {
      this.isActiveInput1 = false;
      this.isActiveInput2 = false;
      this.beginningOrEnd = '';
      if(num === 7) {
        // this.$emit('getData');
        this.allFlag = false;
        this.currentIndex = 7;
        // this.startTime = getFormatTimer(new Date().getTime() - 24*7*60*60*1000);
        this.startTime = this.createdDate;
        this.endTime = moment(new Date().getTime()).format('YYYY-MM-DD');
      }else if(num === 30) {
        this.allFlag = true;
        this.currentIndex = 30;
        this.startTime = moment(new Date().getTime() - 24*30*60*60*1000).format('YYYY-MM-DD');
        this.endTime = moment(new Date().getTime()).format('YYYY-MM-DD');
      }else if(num === 90) {
        this.allFlag = true;
        this.currentIndex = 90;
        this.startTime = moment(new Date().getTime() - 24*90*60*60*1000).format('YYYY-MM-DD');
        this.endTime = moment(new Date().getTime()).format('YYYY-MM-DD');
      }else if(num === 365) {
        this.allFlag = true;
        this.currentIndex = 365;
        this.startTime = moment(new Date().getTime() - 24*365*60*60*1000).format('YYYY-MM-DD');
        this.endTime = moment(new Date().getTime()).format('YYYY-MM-DD');
      }
    },
    selectTimeStart() {
      this.currentIndex = ''
      this.currentDate = new Date();
      this.startTime = moment(this.currentDate.getTime()).format('YYYY-MM-DD');
      this.beginningOrEnd = 'start';
      this.isActiveInput1 = true;
      this.isActiveInput2 = false;
      this.allFlag = true
    },
    selectTimeEnd() {
      this.currentIndex = ''
      this.currentDate = new Date();
      this.endTime = moment(this.currentDate.getTime()).format('YYYY-MM-DD');
      this.beginningOrEnd = 'end';
      this.isActiveInput1 = false;
      this.isActiveInput2 = true;
      this.allFlag = true
    },
    onDatePickerchange(columnIndex, itemIndex, value) {
      // if(this.beginningOrEnd) {
      //   this.currentYear = this.startTime.split('-')[0];
      //   this.currentMonth = this.startTime.split('-')[1];
      //   this.currentDay = this.startTime.split('-')[2];
      // }
      // if(value.type === 'Month') {
      //   this.currentMonth = value.value;
      //   if(Number(value.value)<10) {
      //     this.currentMonth = '0' + this.currentMonth;
      //   }
      // }else if(value.type === 'Year') {
      //   this.currentYear = value.value;
      // }else if(value.type === 'Date') {
      //   this.currentDay = value.value;
      //   if(Number(value.value)<10) {
      //     this.currentDay = '0' + this.currentDay;
      //   }
      // }
      // if(this.beginningOrEnd === 'start') {
      //   this.startTime = this.currentYear + '-' + this.currentMonth + '-' + this.currentDay;
      // }else if(this.beginningOrEnd === 'end') {
      //   this.endTime = this.currentYear + '-' + this.currentMonth + '-' + this.currentDay;
      // }
      // console.log(`[Mand Mobile] DatePicker getFormatDate: ${this.$refs.datePicker.getFormatDate('yyyy/MM/dd')}`)
      if(this.beginningOrEnd === 'start') {
        this.startTime = this.$refs.datePicker.getFormatDate('yyyy-MM-dd');
      }else if(this.beginningOrEnd === 'end') {
        this.endTime = this.$refs.datePicker.getFormatDate('yyyy-MM-dd');
      }
    },
    showPopUp(type) {
      this.$set(this.isPopupShow, type, true);
    },
    hidePopUp(type) {
      this.$set(this.isPopupShow, type, false);
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-body {
  background: var(--background);
  ::v-deep .md-picker-column-item .column-list .column-item {
    font-size: 28px!important;
    color: var(--text_4rd);
  }
  ::v-deep .md-picker-column-item .column-list .column-item.active {
    color: var(--text_1st);
  }
  ::v-deep .md-picker-column-masker.bottom:after {
    border-top-color: var(--line_01);
  }
  ::v-deep .md-picker-column-masker.top:before {
    border-top-color: var(--line_01);
  }
  .confim {
    width: calc(100% - 64px);
    margin: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      line-height: 128px;
      font-size: 28px;
      color: var(--text_1st);
    }
    .text-left {
      color: var(--text_3rd);
    }
  }
  .display {
    width: 100%;
    padding: 0px 30px;
    display: flex;
    flex-wrap: wrap;
    div {
      background: var(--gray_05);
      margin: 10px 10px 10px 0;
      display: inline-block;
      border-radius: 32px;
      color: var(--text_2nd);
      font-size: 28px;
      padding: 0px 36px;
      line-height: 64px; 
    }
    .bg-select {
      background: var(--brand_01);
      color: var(--background);
    }
  }
  .display-input {
    width: 100%;
    padding: 46px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .input {
      width: 310px;
      line-height: 80px;
      text-align: center;
      border-radius: 10px;
      background: var(--gray_05);
      border: 1px solid var(--gray_05);
      color: var(--text_1st);
    }
    .active {
      background: var(--gray_05);
      border: 1px solid var(--brand_01);
      color: var(--brand_01);
    }
    .gun {
     color: var(--text_2nd);
    }
  }
}
</style>

