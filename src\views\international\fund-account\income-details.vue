<template>
  <div class="index-page">
    <!-- <div class="page-title">
      <img class="return" @click="goBack" src="../../assets/images/icon_back_new.png" alt="pic">
      <span>{{$t("myAccount.incomeDetails")}}</span>
    </div> -->
    <div v-show="isScrollSwitch" class="income-card">
      <p class="text-line1" @click="currencyShow = !currencyShow">
        <!-- <span class="padding-20">{{$t("myAccount.cumulativeIncome")}}</span> -->
        <span class="padding-20">{{
          $t("myAccount.transaction") + "(" + parameter.currency + ")"
        }}</span>
        <img
          class="show-pic"
          :src="require(`@/assets/images/international/arrow1_${currencyShow ? 'up' : 'down'}_default.png`)"
          alt="pic"
        />
      </p>
      <p
        class="text-line2"
        :class="
          data.isPositive === 2
            ? 'positive'
            : data.isPositive === 0
            ? 'negative'
            : 'second-text'
        "
      >
        {{ data.totalReturn }}
      </p>
    </div>
    <div class="income-card-programme1">
      <p class="text-line1" @click="currencyShow = !currencyShow">
        <!-- <span class="padding-20">{{$t("myAccount.cumulativeIncome")}}</span> -->
        <span class="padding-20">{{
          $t("myAccount.transaction") + "(" + parameter.currency + ")"
        }}</span>
        <img
          class="show-pic"
          :src="require(`@/assets/images/international/arrow1_${currencyShow ? 'up' : 'down'}_default.png`)"
          alt="pic"
        />
      </p>
      <!-- :class="data.isPositive === 2 ? 'positive' : data.isPositive === 0 ? 'negative' : 'second-text'" -->
      <p
        class="text-line2"
        :class="
          klineTheme === 'redRiseGreenFall'
            ? data.isPositive === 2
              ? 'positive'
              : data.isPositive === 0
              ? 'negative'
              : 'second-text'
            : data.isPositive === 2
            ? 'negative'
            : data.isPositive === 0
            ? 'positive'
            : 'second-text'
        "
      >
        {{ data.totalReturn }}
      </p>
    </div>
    <div
      v-if="currencyShow"
      :class="isScrollSwitch ? 'currencyPopup fixed' : 'currencyPopup right424'"
    >
      <div
        v-for="(item, index) in currencyList"
        :key="index"
        @click="currencyChoose(item.key)"
        :class="[parameter.currency === item.key ? 'active' : 'currencyItem']"
      >
        {{ item.text }}
      </div>
    </div>
    <div v-show="currencyShow" class="dialog-mask" @click="closeDialog"></div>

    <div v-show="isScrollSwitch" class="margin-90"></div>
    <template
      v-if="data.dailyRevenues && data.dailyRevenues.length && accountStatus"
    >
      <div
        class="card-text"
        v-for="(item, index) in data.dailyRevenues"
        :key="index"
      >
        <span class="text-left">{{ item.revenueDate }}</span>
        <!-- :class="item.isPositive === 2 ? 'positive' : (item.isPositive === 0 ? 'negative' : 'second-text')" -->
        <span
          v-if="item.tradeDate"
          :class="
            klineTheme === 'redRiseGreenFall'
              ? item.isPositive === 2
                ? 'positive'
                : item.isPositive === 0
                ? 'negative'
                : 'second-text'
              : item.isPositive === 2
              ? 'negative'
              : item.isPositive === 0
              ? 'positive'
              : 'second-text'
          "
          >{{ item.gains }}</span
        >
      </div>
    </template>
    <noData v-else></noData>
    <loading v-if="isLoading"></loading>
  </div>
</template>


<script>
import { mapState } from "vuex";
import { fmoney } from "@/utils/util.js";
import { getRevenuesNum, getAccountNumber } from "@/services/account.js";
import { Toast } from "mand-mobile";
import loading from "@/components/loading/index";
import noData from "@/components/international/common/no-data";

export default {
  name: "interIncomeDetails",
  data() {
    return {
      isScrollSwitch: false,
      parameter: {
        currency: "HKD",
      },
      currencyShow: false,
      data: {},
      currencyList: [
        { text: this.$t('myAccount.HKDollar'), key: "HKD" },
        { text: this.$t('myAccount.dollar'), key: "USD" },
      ],
      isLoading: true,
      size: 15,
      isGetData: true,
      accountStatus: false,
    };
  },
  computed: {
    ...mapState(["accountNumber", "theme", "klineTheme"]),
  },
  components: { loading, noData },
  created() {
    this.isLoading = true;
    getAccountNumber(this).then((res) => {
      console.log(res);
      let object = res.data.tradingAccountList.find((item) => {
        return item.type === "FUND";
      });
      console.log("==========", object);
      if (object) {
        this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        this.getdata();
      } else {
        this.accountStatus = false;
      }
    });
  },
  mounted() {
    window.addEventListener("scroll", this.dataScrollIncome, true);
  },
  destroyed() {
    if (document.body) {
      document.body.scrollTop = 0;
    }
    if (document.documentElement) {
      document.documentElement.scrollTop = 0;
    }
    window.removeEventListener("scroll", this.dataScrollIncome, true);
  },
  methods: {
    closeDialog() {
      this.currencyShow = false;
    },
    currencyChoose(str) {
      this.parameter.currency = str;
      this.size = 15;
      this.getdata();
      this.currencyShow = false;
      // this.capitalFlow()
    },
    dataScrollIncome() {
      let ScrollHeight = this.getScrollHeight();
      let DocumentTop = this.getDocumentTop();
      let WindowHeight = this.getWindowHeight();
      if (ScrollHeight == DocumentTop + WindowHeight && !this.isGetData) {
        this.isGetData = true;
        this.isLoading = true;
        this.size = this.size + 15;
        this.getdata();
      }
      if (DocumentTop > 98 && !this.isScrollSwitch) {
        this.isScrollSwitch = true;
      } else if (DocumentTop <= 98 && this.isScrollSwitch) {
        this.isScrollSwitch = false;
      }
    },
    //文档高度
    getDocumentTop() {
      var scrollTop = 0,
        bodyScrollTop = 0,
        documentScrollTop = 0;
      if (document.body) {
        bodyScrollTop = document.body.scrollTop;
      }
      if (document.documentElement) {
        documentScrollTop = document.documentElement.scrollTop;
      }
      scrollTop =
        bodyScrollTop - documentScrollTop > 0
          ? bodyScrollTop
          : documentScrollTop;
      return scrollTop;
    },
    //可视窗口高度
    getWindowHeight() {
      var windowHeight = 0;
      if (document.compatMode == "CSS1Compat") {
        windowHeight = document.documentElement.clientHeight;
      } else {
        windowHeight = document.body.clientHeight;
      }
      return windowHeight;
    },

    //滚动条滚动高度
    getScrollHeight() {
      var scrollHeight = 0,
        bodyScrollHeight = 0,
        documentScrollHeight = 0;
      if (document.body) {
        bodyScrollHeight = document.body.scrollHeight;
      }
      if (document.documentElement) {
        documentScrollHeight = document.documentElement.scrollHeight;
      }
      scrollHeight =
        bodyScrollHeight - documentScrollHeight > 0
          ? bodyScrollHeight
          : documentScrollHeight;
      return scrollHeight;
    },

    goBack() {
      this.$router.go(-1);
    },
    getdata() {
      let params = {
        size: this.size,
        currency: this.parameter.currency,
      };
      getRevenuesNum(this.accountNumber, params, this).then((res) => {
        if (res.code == "200") {
          this.accountStatus = true;
          this.data = res.data;
          this.processingData();
        }
      });
    },
    processingData() {
      this.data.totalReturn = Number(this.data.totalReturn);
      if (this.data.totalReturn > 0) {
        this.data.isPositive = 2;
        this.data.totalReturn = "+" + fmoney(this.data.totalReturn, 2);
      } else if (Number(this.data.totalReturn) === 0) {
        this.data.isPositive = 1;
        this.data.totalReturn = fmoney(this.data.totalReturn, 2);
      } else {
        this.data.isPositive = 0;
        this.data.totalReturn =
          "-" + fmoney(Math.abs(this.data.totalReturn), 2);
      }

      this.data.dailyRevenues.forEach((item, index) => {
        if (!item.tradeDate) {
          this.data.dailyRevenues[index].revenueDate =
            this.data.dailyRevenues[index].revenueDate +
            this.$t("myAccount.nonTradingDay");
          return;
        }
        item.gains = Number(item.gains);
        if (item.gains > 0) {
          this.data.dailyRevenues[index].isPositive = 2;
          this.data.dailyRevenues[index].gains = "+" + fmoney(item.gains, 2);
        } else if (item.gains === 0) {
          this.data.dailyRevenues[index].isPositive = 1;
          this.data.dailyRevenues[index].gains = fmoney(item.gains, 2);
        } else {
          this.data.dailyRevenues[index].isPositive = 0;
          this.data.dailyRevenues[index].num = "-" + fmoney(-item.num, 2);
        }
      });
      this.isLoading = false;
      this.isGetData = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  position: relative;
  .dialog-mask {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    z-index: 2800;
  }
  .currencyPopup {
    position: absolute;
    top: 0px;
    left: 30px;
    z-index: 2999;
    font-size: 28px;
    border-radius: 4px;
    background: var(--background);
    box-shadow: 0px 0px 8px 0px rgba(28,33,42,0.1);
    border-radius: 30px;
    text-align: center;
    padding: 18px 0;
    color: var(--text_3rd);
    &:before{
      position: absolute;
      top: -0.15rem;
      left: 0.5rem;
      content: "";
      border-bottom: 15px solid var(--background);
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
    }
    .currencyItem {
      padding: 18px 40px;
    }
    .active {
      padding: 18px 40px;
      color: var(--brand_01);
    }
  }
  .fixed {
    position: fixed;
    top: 90px;
    left: 30px;
  }
  .right424 {
    top: 100px;
    left: 424px;
  }
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: Medium;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .income-card {
    background: var(--gray_05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 32px;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 1000;
    width: 100%;
    .text-line1 {
      line-height: 92px;
      text-align: left;
      font-size: 24px;
      position: relative;
      color: var(--text_1st);
      .show-pic {
         width: 36px;
        height: 36px;
        vertical-align: middle;
        margin-left: 12px;
        margin-bottom: 5px;
      }
      .show {
        transform: rotate(180deg);
      }
    }
    .text-line2 {
      line-height: 92px;
      font-size: 32px;
      text-align: right;
    }
    // .positive {
    //   @include themeify {
    //     color: themed("k-line-red");
    //   }
    // }
    // .negative {
    //   @include themeify {
    //     color: themed("k-line-green");
    //   }
    // }
  }
  .income-card-programme1 {
    padding: 56px 32px;
    .text-line1 {
      line-height: 44px;
      text-align: center;
      font-size: 24px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--text-1st);
      .show-pic {
        margin-left: 20px;
        width: 36px;
        height: 36px;
      }
      .show {
        transform: rotate(180deg);
      }
    }
    .text-line2 {
      line-height: 72px;
      font-size: 48px;
      text-align: center;
    }
    .positive {
      color: var(--red);
    }
    .negative {
      color: var(--green);
    }
    .second-text {
      color: var(--text_4rd);
    }
  }
  .margin-90 {
    margin-top: 90px;
  }
  .card-text {
    height: 98px;
    line-height: 98px;
    margin: 0px 32px;
    border-bottom: 1px solid var(--line_01);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28px;
    .positive {
      color: var(--red);
    }
    .negative {
      color: var(--green);
    }
    .second-text {
      color: var(--text_4rd);
    }
  }
  .fund-account-empty {
    width: 334px;
    height: 197px;
    text-align: center;
    margin: 30px auto 0;
    img {
      width: 100%;
    }
    p {
      @include font_color(second-text);
      height: 28px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #868e9e;
      line-height: 28px;
    }
  }
}
</style>
