<template>
  <div class="index-page">
    <!-- <div class="title">
      <img
        class="return"
        @click="goBack"
        :src="require(`@/assets/images/${theme}/icon_back_new.png`)"
        alt="pic"
      />
      <span>{{ $t("myAccount.transaction") }}</span>
    </div> -->

    <div class="my-account">
      <div class="total">
        <div class="left">
          <span>{{ $t("myAccount.totalAssets") }}</span>
          <img
            v-if="!isHiden"
            @click="isHiden = !isHiden"
            class="hide"
            src="@/assets/images/transaction/icon_eye.png"
            alt="pic"
          />
          <img
            v-else
            @click="isHiden = !isHiden"
            class="hide"
            src="@/assets/images/transaction/icon_close.png"
            alt="pic"
          />
          <span class="text">{{ $t("myAccount.HKDollar") }}</span>
          <img
            class="hkd"
            @click="isHiden = !isHiden"
            :src="require('@/assets/images/dark/com_ico_arrow.png')"
            alt="pic"
          />
        </div>
        <div class="right">
          <img
            class="pic-right"
            @click="share"
            src="@/assets/images/transaction/icon_share.png"
            alt="pic"
          />
          <span class="text">{{ $t("myAccount.yesterdayEarnings") }}</span>
        </div>
      </div>
      <div class="total-detail">
        <span v-show="isHiden" class="detail-left">{{ accountInformation.total }}</span>
        <span v-show="isHiden" class="detail-right">{{ accountInformation.profitAndLoss }}</span>
        <span v-show="!isHiden" class="detail-left">{{ accountInformation.trueTotal }}</span>
        <span v-show="!isHiden" class="detail-right">{{ accountInformation.trueProfitAndLoss }}</span>
      </div>
      <div class="remaining-funds">
        <span>{{ $t("myAccount.cashBalance") }}</span>
        <span>{{ $t("myAccount.fundsInTransit") }}</span>
      </div>
      <div class="remaining-funds num-last">
        <span v-show="!isHiden">100102.73</span>
        <span v-show="isHiden" class="detail-right">{{ accountInformation.profitAndLoss }}</span>
        <span v-show="!isHiden">1000.00</span>
        <span v-show="isHiden" class="detail-right">{{ accountInformation.profitAndLoss }}</span>
      </div>
      <div class="remaining-funds remaining-funds2">
        <span>{{ $t("myAccount.positionFund") }}</span>
        <span>{{ $t("myAccount.accountCumulativeIncome") }}</span>
      </div>
      <div class="remaining-funds num-last">
        <span v-show="!isHiden">100102.73</span>
        <span v-show="isHiden" class="detail-right">{{ accountInformation.profitAndLoss }}</span>
        <span v-show="!isHiden">1000.00</span>
        <span v-show="isHiden" class="detail-right">{{ accountInformation.profitAndLoss }}</span>
      </div>
    </div>
    <div class="link">
      <div class="link-item" v-for="(item,index) in linkList" :key="index" @click="jump(index)">
        <img class="link-img" :src="item.url" alt="pic" />
        <div class="link-text">{{ item.title }}</div>
      </div>
    </div>
    <div class="fund-title">
      <span>{{ $t("myAccount.fundPosition") }}</span>
    </div>
    <div class="table">
      <div class="table-th">
        <span>{{ $t("myAccount.name") }}</span>
        <span class="th-right">
          <span class="amount-of-money">{{$t("myAccount.amountAndYesterdayEarnings")}}</span>
          <span class="profit">{{$t("myAccount.positionIncome")}}</span>
        </span>
      </div>
      <div
        :class="index === positionList.length - 1 ? 'table-tr last-tr' : 'table-tr'"
        v-for="(item,index) in positionList"
        :key="index"
      >
        <span class="name">{{ item.name }}</span>
        <span class="tr-right">
          <span class="amount-of-money">
            <span class="money">{{ item.money }}</span>
            <span class="single-day-profit">{{ item.yesterdayEarnings }}</span>
          </span>
          <span class="profit">
            <span class="position-income">{{ item.positionIncome }}</span>
            <span class="percentage">{{ item.percentage }}</span>
          </span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";
import {fmoney} from '@/utils/util.js'
export default {
  name: "interFundAccount",
  computed: {
    ...mapState(['theme']),
  },
  data() {
    return {
      positionList: [
        { name: "易方达(香港)港元货币市场基金(港元)C类累积", money: "726,378.95", yesterdayEarnings: '+2,683.23', positionIncome: '+13,212.84', percentage: '11.50%' },
        { name: "富国天惠成长混合", money: "726,378.95", yesterdayEarnings: '+50,140.23', positionIncome: '+9,094.43', percentage: '11.50%' },
        { name: "易方达(香港)港元货币市场基金(港元)C类累积", money: "726,378.95", yesterdayEarnings: '+2,683.23', positionIncome: '+13,212.84', percentage: '11.50%' },
        { name: "富国天惠成长混合", money: "726,378.95", yesterdayEarnings: '+50,140.23', positionIncome: '+9,094.43', percentage: '11.50%' },
        { name: "易方达(香港)港元货币市场基金(港元)C类累积", money: "726,378.95", yesterdayEarnings: '+2,683.23', positionIncome: '+13,212.84', percentage: '11.50%' },
        { name: "富国天惠成长混合", money: "726,378.95", yesterdayEarnings: '+50,140.23', positionIncome: '+9,094.43', percentage: '11.50%' },
      ],
      linkList: [
        { title: '基金排行', url: require('@/assets/images/transaction/icon_jjph.png') },
        { title: '债券基金', url: require('@/assets/images/transaction/icon_zqjj.png') },
        { title: '基金公司', url: require('@/assets/images/transaction/icon_jjgs.png') },
        { title: '风险等级', url: require('@/assets/images/transaction/icon_ffdj.png') },
        { title: '订单记录', url: require('@/assets/images/transaction/icon_ddjl.png') },
        { title: '收益明细', url: require('@/assets/images/transaction/icon_symx.png') },
        { title: '资金流水', url: require('@/assets/images/transaction/icon_zzls.png') },
        { title: '存入资金', url: require('@/assets/images/transaction/icon_crzj.png') }
      ],

      accountInformation: {
        total: "********",
        profitAndLoss: "********",
        trueTotal: "********",
        trueProfitAndLoss: "********",
      },
      isHiden: true,
    };
  },
  created() {
    this.accountInformation.trueTotal =  fmoney(this.accountInformation.trueTotal,2);
    this.accountInformation.trueProfitAndLoss =  fmoney(this.accountInformation.trueProfitAndLoss,2);
  },
  methods: {
    jump(index) {
      switch (index) {
        case 0:
          this.$router.push({
            path: '/international/fundKind'
          })
          break;
        case 1:
          this.$router.push({
            path: '/international/fundKind'
          })
          break;
        case 2:
          this.$router.push({
            path: '/international/fundCompany'
          })
          break;
        case 3:
          this.$router.push({
            path: '/international/risk/level'
          })
          break;

        case 4:
          this.$router.push({
            path: '/international/orderRecord'
          })
          break;
          case 5:
          this.$router.push({
            path: '/international/incomeDetails'
          })
          break;
        case 6:
          this.$router.push({
            path: '/international/capitalFlow'
          })
          break;
        case 7:
          this.$router.push({
            path: '/international/fundKind'
          })
          break;
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    share() {

    }
  },
};
</script>

<style lang="scss" scoped>
.index-page {
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
    padding-bottom: 69px;
  }
  .title {
    width: 100%;
    height: 128px;
    text-align: center;
    line-height: 128px;
    font-size: 32px;
    font-weight: Medium;
    position: relative;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .my-account {
    background-image: url("~@/assets/images/transaction/bg_trade.png");
    background-size: 100% 100%;
    color: #ffffff;
    width: calc(100% - 60px);
    margin: 0px 30px;
    padding: 0px 30px 25px 30px;
    .total {
      padding-top: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        line-height: 44px;
        position: relative;
        .text {
          padding-left: 98px;
          padding-right: 34px;
        }
        .hide {
          width: 38px;
          height: 38px;
          position: absolute;
          left: 104px;
          top: 50%;
          transform: translateY(-50%);
        }
        .hkd {
          width: 18px;
          height: 10px;
          position: absolute;
          right: 0px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .right {
        line-height: 44px;
        position: relative;
        .text {
          color: #bfd1ff;
          padding-left: 48px;
        }
        .pic-right {
          width: 28px;
          height: 28px;
          position: absolute;
          left: 0px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .total-text {
      height: 58px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #bfd1ff;
      .text-left {
        font-size: 28px;
        text-align: left;
      }
      .text-right {
        font-size: 28px;
        text-align: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .text-pic {
          width: 38px;
          height: 38px;
          margin-right: 16px;
          background-image: url("~@/assets/images/transaction/icon_share.png");
          background-size: contain;
        }
      }
    }
    .total-detail {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .detail-left {
        font-size: 62px;
        height: 100%;
        line-height: 74px;
        text-align: left;
      }
      .detail-right {
        font-size: 28px;
        height: 100%;
        line-height: 74px;
        text-align: right;
      }
    }
    .remaining-funds {
      color: #bfd1ff;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      line-height: 34px;
      font-size: 24px;
      margin-top: 30px;
    }
    .num-last {
      margin-top: 0px;
    }
    .remaining-funds2 {
      margin-top: 20px;
    }
  }
  .link {
    width: calc(100% - 6px);
    padding: 16px 0px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    .link-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      text-align: center;
      .link-img {
        width: 44px;
        height: 44px;
        margin-top: 16px;
      }
      .link-text {
        line-height: 52px;
        font-style: 24px;
      }
    }
  }
  .fund-title {
    width: calc(100% - 60px);
    margin: 0px 30px;
    margin-top: 10px;
    line-height: 88px;
    font-size: 28px;
    text-align: left;
  }
  .table {
    @include themeify {
      background: themed("second-bg");
    }
    width: calc(100% - 60px);
    margin: 0 30px;
    padding: 0px 20px;
    .table-th {
      @include themeify {
        color: themed("second-text");
      }
      display: flex;
      justify-content: space-between;
      line-height: 62px;
      font-size: 22px;
      .th-right {
        .amount-of-money {
          text-align: right;
        }
        .profit {
          text-align: right;
          padding-left: 98px;
        }
      }
    }
    .table-tr {
      @include themeify {
        border-bottom-color: themed("line-color");
      }
      border-bottom: 1px solid;
      padding: 12px 0px 18px 0px;
      display: flex;
      justify-content: space-between;
      vertical-align: top;
      .name {
        display: inline-block;
        width: 288px;
        white-space: pre-wrap;
        font-size: 26px;
        line-height: 40px;
      }
      .tr-right {
        .amount-of-money {
          text-align: right;
          display: inline-block;
          vertical-align: top;
          .money {
            display: block;
            font-size: 26px;
            line-height: 40px;
          }
          .single-day-profit {
            font-size: 22px;
            line-height: 28px;
          }
        }
        .profit {
          display: inline-block;
          width: 190px;
          text-align: right;
          vertical-align: top;
          .position-income {
            display: block;
            font-size: 26px;
            line-height: 40px;
          }
          .percentage {
            font-size: 22px;
            line-height: 28px;
          }
        }
      }
    }
    .last-tr {
      border: none;
    }
  }
}
</style>
