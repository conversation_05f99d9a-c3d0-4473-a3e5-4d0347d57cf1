<template>
  <div class="subscribe-redeem-state">
    <div class="subscribe-redeem-state-text">
      <span>
        {{ $t("myAccount." + orderData.orderType) }}
      </span>
      <span v-if="orderData.orderType == 'Sell'">
        {{ $t("myAccount.shareSingle") }}
      </span>
      <span v-else>
        {{ format(orderData.currency) }}
      </span>
      <span
        v-show="isCancellButtonShow"
        @click="cancelOrder"
        class="cancel-order"
        >{{ $t("myAccount.CANCELL") }}</span
      >
    </div>
    <div v-if="orderData.orderType == 'Sell'" class="account-num">
      {{ orderData.quantity }}
    </div>
    <div v-else class="account-num">
      {{ orderData.amount }}
    </div>
    <div v-if="orderData.orderType != 'Dividend'" class="order-status">
      {{ $t("myAccount." + orderData.status) }}
    </div>
    <div class="order-state-content">
      <div v-if="orderData.orderType != 'Dividend'" class="order-type">
        <img v-if="orderData.orderType == 'Sell'" class="sell" :src="require(`@/assets/images/international/progress_bar_03.png`)" alt="pic"/>
        <img v-else class="buy" :src="require(`@/assets/images/international/progress_bar_02.png`)" alt="pic"/>
        <div class="order-message order-message1">
          <!-- <p>{{ $t("myAccount.orderSubmitted") }}</p>
          <p>
            {{
              orderData.orderType == "Sell"
                ? $t("myAccount.expectedT") +
                  tradeRulesData.redemptionPlusDay +
                  $t("myAccount.redemptionDescriptionlabelEnd")
                : $t("myAccount.expectedT") +
                  tradeRulesData.confirmationPlusDay +
                  $t("myAccount.orderDetailBuy")
            }}
          </p>
          <p v-if="orderData.orderType == 'Buy'">
            {{ $t("myAccount.expectedT")
            }}{{ tradeRulesData.moneyConfirmedPlusDay
            }}{{ $t("myAccount.checkYield") }}
          </p> -->
          <div>
            <p>{{ $t("myAccount.orderSubmitted") }}</p>
            <p>{{ orderData.orderTime && moment(orderData.orderTime).format("YYYY-MM-DD HH:mm") }}</p>
          </div>
          <div v-if="orderData.orderType == 'Buy'">
            <p>{{ $t("myAccount.ConfirmShare") }}</p>
            <p>
              {{ $t("myAccount.ExpectedBefore", {time: tradeRulesData.shareConfirmedDay && moment(tradeRulesData.shareConfirmedDay).endOf('day').format("YYYY-MM-DD HH:mm") }) }}
              {{ $t("myAccount.ConfirmedWithNAV", {time: moment(tradeRulesData.navConfirmedDay).format("YYYY-MM-DD")}) }}
            </p>
          </div>
          <div>
            <p>{{ orderData.orderType == "Sell" ? $t("myAccount.FundPosted") : $t("myAccount.StartViewingEarning") }}</p>
            <p>
              {{
                $t("myAccount.ExpectedBefore", {time: (orderData.orderType == "Sell" ?
                tradeRulesData.redemptionArriveDay && moment(tradeRulesData.redemptionArriveDay).endOf('day').format("YYYY-MM-DD HH:mm") : 
                tradeRulesData.profitArriveDay && moment(tradeRulesData.profitArriveDay).endOf('day').format("YYYY-MM-DD HH:mm"))
                })
              }}
            </p>
          </div>
        </div>
      </div>
      <div
        v-else
        :class="
          orderData.orderType != 'Dividend'
            ? 'order-type'
            : 'order-type order-type-line'
        "
      ></div>
      <ul class="order-state-ul">
        <li>
          <label>{{ $t("myAccount.productName") }}</label>
          <div>
            {{ fundsNameFun(orderData) }}
          </div>
        </li>
        <li>
          <label>{{
            orderData.orderType == "Buy"
              ? $t("myAccount.paymentAccount")
              : orderData.orderType == "Sell"
              ? $t("myAccount.redeemTo")
              : $t("myAccount.shareOutBonus")
          }}</label>
          <div>{{ $t("myAccount.paymentAccountContent") }}</div>
        </li>
        <li>
          <label>{{
            orderData.orderType == "Buy"
              ? $t("myAccount.subscriptionAmount")
              : $t("myAccount.redemptionShare")
          }}</label>
          <div>
            {{
              orderData.orderType === "Sell"
                ? fmoney(orderData.quantity, 4)
                : orderData.currency + " " + orderData.amount
            }}
          </div>
        </li>
      </ul>
    </div>
    <div class="dialog-main" v-show="isDialogShow">
      <div class="dialog">
        <div class="title">
          <span class="text">{{ $t("myAccount.popUpTitle") }}</span>
          <img src="@/assets/images/international/close2.png" class="close" @click="closeDialog('center')" />
        </div>
        <div class="password">
          <input
            id="password"
            v-model="passwordListNew"
            @input="setFontColor"
            class="input-box"
            :type="currencyShow? 'password' : 'text'"
            :placeholder="$t('myAccount.passwordPrompt')"
          />
          <div
            class="eye-div"
            @click="toggleEye"
          >
            <img
              :src="require(`@/assets/images/international/preview_${currencyShow ? 'close' : 'open'}.png`)"
              class="eye-passord"
              alt="pic"
            />
          </div>
        </div>
        <div class="password-prompt">
          <span v-show="isPasswordError" class="password-err">{{
            $t("myAccount.passwordMistake")
          }}</span>
          <span class="password-forget" @click="submitResetPassword">{{
            $t("myAccount.forgotPassword")
          }}</span>
        </div>
        <div class="bounced-operation">
          <div class="cancel" @click="closeDialog('center')">
            {{ $t("common.btns.cancel") }}
          </div>
          <div v-show="!passwordLegal" class="confim-disabled">
            {{ $t("myAccount.determine") }}
          </div>
          <div v-show="passwordLegal" class="confim" @click="submitPassword">
            {{ $t("myAccount.determine") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { fmoney } from "@/utils/util.js";
import moment from "moment";
import { getFundInformation } from "@/services/account.js";
import { getOrderData, cancelTheOrder } from "@/services/account.js";
import { Toast } from "mand-mobile";
import { tradeRules } from "@/services/fund";
export default {
  computed: {
    ...mapState(["theme", "locale", "accountNumber"]),
  },
  data() {
    return {
      fmoney,
      isCancellButtonShow: false,
      passwordLegal: false,
      isPasswordError: false,
      orderData: {},
      orderDescribe: "default",
      isDialogShow: false,
      passwordListNew: "",
      tradeRulesData: Object,
      currencyShow: true
    };
  },

  created() {
    this.getdata();
    // 判断订单类型   申购赎回订单  查询交易规则
    // if()
  },
  methods: {
    moment,
    submitResetPassword() {
      this.$jsBridge.run("resetPassword", {
        accountType: "funding",
        account: this.accountNumber,
      });
    },
    format(type) {
      if (type == "HKD") {
        return "(" + this.$t("fundMessage.currencyType.HKD") + ")";
      } else if (type == "USD") {
        return "(" + this.$t("fundMessage.currencyType.USD") + ")";
      }
    },
    closeDialog(e) {
      this.passwordListNew = "";
      this.isDialogShow = false;
      this.isPasswordError = false;
    },
    submitPassword() {
      this.$jsBridge.run("startLogin", {
        type: "D",
        password: this.passwordListNew,
        callback: ({ login_state, account_type }) => {
          if (login_state) {
            this.cancelOrderSubmit();
            this.isPasswordError = false;
            this.isDialogShow = false;
            Toast.info(this.$t("myAccount.itsSuccess"));
          } else {
            this.isPasswordError = true;
          }
        },
      });
    },
    setFontColor() {
      this.isPasswordError = false;
      if (
        this.passwordListNew.length >= 6 &&
        this.passwordListNew.length < 16
      ) {
        this.passwordLegal = true;
      } else {
        this.passwordLegal = false;
      }
    },
    toggleEye() {
      this.currencyShow = !this.currencyShow
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data.productName && data.productName.cn
          ? data.productName.cn
          : "-";
      } else if (this.locale == "zh-hant") {
        return data.productName && data.productName.hk
          ? data.productName.hk
          : "-";
      } else {
        return data.productName && data.productName.us
          ? data.productName.us
          : "-";
      }
    },
    cancelOrder() {
      // this.getdata();
      // if()
      // 判断当前时间是否可以撤销  时间戳判断
      this.isDialogShow = true;
    },
    cancelOrderSubmit() {
      let timestamp = Date.parse(new Date());
      let cancellTimer = new Date(this.orderData.cancellationDeadline);
      cancellTimer = cancellTimer.getTime();
      if (timestamp >= cancellTimer) {
        Toast.info(this.$t("myAccount.orderInProcess"));
        this.getdata();
        return;
      }
      cancelTheOrder(this.$route.query.id).then((res) => {
        if (res.code === "200") {
          this.getdata();
        }
      });
    },
    getdata() {
      let timestamp = Date.parse(new Date());
      getOrderData(this.$route.query.id).then((res) => {
        this.orderData = res.data;
        if (this.orderData.orderTime) {
          this.orderData.orderTime = moment(this.orderData.orderTime).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        let cancellTimer = new Date(this.orderData.cancellationDeadline);
        cancellTimer = cancellTimer.getTime();
        if (this.orderData.status === "SUBMITTED" && timestamp < cancellTimer) {
          this.isCancellButtonShow = true;
        } else {
          this.isCancellButtonShow = false;
        }
        if (this.orderData.orderType === "Buy") {
          this.orderData.amount = fmoney(Number(this.orderData.amount), 2);
          this.gettradeRules(this.orderData.fundIsin, this.orderData.orderTime);
        } else if (this.orderData.orderType === "Sell") {
          this.orderData.quantity = fmoney(Number(this.orderData.quantity), 4);
          this.gettradeRules(this.orderData.fundIsin, this.orderData.orderTime);
        }
      });
    },
    gettradeRules(fundIsin, orderTime) {
      let actualTime = orderTime && (moment(orderTime).format('YYYY-MM-DD') + 'T' + moment(orderTime).format('HH:mm:ss'));
      tradeRules(fundIsin, actualTime).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
        }
      });
    },
    getFundInformationRules() {
      getFundInformation(this.$route.query.fundIsin).then((res) => {
        this.rulesDatas = res.data;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style>
.md-button-inner {
  font-size: 28px !important;
}
</style>

<style lang="scss" scoped>
.subscribe-redeem-state {
  min-height: 100vh;
  text-align: center;
  .dialog-main {
    background-color: var(--mask);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .dialog {
      background: var(--background);
      border-radius: 30px;
      padding: 48px 40px;
      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        padding: 0;
        .text {
          display: block;
          font-size: 34px;
          color: var(--text_1st);
          font-weight: 600;
          width: 80%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin: 0 auto;
        }
        .close {
          width: 48px;
          height: 48px;
          position: absolute;
          right: 0;
          top:0;
        }
      }
      .password {
        margin: 38px 0 40px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        .eye-div {
          position: absolute;
          right:0;
          top: 0;
          width: 100px;
          height: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 0;
          .eye-passord {
            width: 48px;
            height: 48px;
          }
        }
        .input-box {
          background: var(--gray_05);
          color: var(--text_3rd);
          -moz-appearance: button; /* Firefox */
          -webkit-appearance: button; /* Safari 和 Chrome */
          appearance: button;
          border: none;
          outline: none;
          font-size: 28px;
          padding-left: 30px;
          width: 100%;
          border-radius: 20px;
          padding: 26px 80px 26px 30px;
          width: 470px;
          height: 100px;
          &:focus {
            color: var(--text_1st);
          }
        }
      }
      .password-prompt {
        line-height: 64px;
        font-size: 28px;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .password-err {
          position: absolute;
          top: 0px;
          left: 0px;
          color: var(--error)
        }
        .password-forget {
          color: var(--brand_02);
        }
      }
      .bounced-operation {
        margin-top: 28px;
        display: flex;
        justify-content: space-between;
        div {
          width: 220px;
          height: 80px;
          line-height: 80px;
          text-align: center;
          font-size: 28px;
          border-radius: 40px;
        }
        .cancel {
          background-color: var(--background);
          color: var(--text_1st);
          border: 1px solid var(--line_01);
        }
        .confim-disabled {
          background-color: var(--gray_04);
          color: var(--text_disabled);
          border: 1px solid var(--gray_04);
        }
        .confim {
          background-color: var(--brand_01);
          color: var(--text_5th);
          border: 1px solid var(--brand_01)
        }
      }
    }
  }
  .page-title {
    height: 80px;
    line-height: 80px;
    text-align: center;
    position: relative;
    font-size: 32px;
    font-weight: 500;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }

  .subscribe-redeem-state-text {
    position: relative;
    font-size: 30px;
    line-height: 70px;
    font-weight: 400;
    margin-top: 56px;
    color: var(--text_1st);
    .cancel-order {
      color: var(--brand_02);
      position: absolute;
      top: 0px;
      height: 80px;
      line-height: 80px;
      font-size: 28px;
      font-weight: 400;
      right: 30px;
    }
  }
  .account-num {
    font-weight: bold;
    font-size: 48px;
    line-height: 72px;
    margin-bottom: 16px;
    color: var(--text_1st);
  }
  .order-status {
    color: var(--data_08);
    width: calc(100% - 64px);
    margin: 0 32px;
    padding-bottom: 32px;
    border-bottom: 1px solid var(--line_01);
    font-size: 28px;
    line-height: 44px;
  }
  .order-state-content {
    .order-type {
      display: flex;
      justify-content: center;
      font-size: 26px;
      margin: 40px 0px 120px 0px;
      padding: 0 105px;
      color: var(--text_2nd);
      .buy {
        width: 48px;
        height: 334px;
      }
      .sell {
        width: 40px;
        height: 154px;
      }
      .order-message {
        text-align: left;
        margin-left: 40px;
        div {
          margin-top: 38px;
          &:first-child {
            margin-top: 0;
          }
          p {
            &:last-child {
              font-size: 22px;
              margin-top: 20px;
              color: var(--text_2nd);
            }
          }
        }
      }
      .order-message1 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .order-type-line {
      margin-top: 0px;
    }
    .order-state-ul {
      padding: 0 32px;
      font-size: 28px;
      margin-bottom: 70px;
      li {
        padding: 32px 0;
        display: flex;
        justify-content: space-between;
        align-items: start;
        border-bottom: 1px solid var(--line_01);
        color: var(--text_3rd);
        text-align: left;
        &:last-child {
          border-bottom: 0;
        }
        label {
          min-width: 180px;
        }
        div {
          color: var(--text_1st);
          text-align: right;
        }
      }
    }
  }
}
</style>