<template>
  <div class="index-page">
    <div
      v-show="isOrderPopUp || isAccountPopUp"
      class="dialog-mask"
      @click="closeDialog"
    ></div>
    <!-- 切换账户弹出层 -->
    <!-- <ul v-show="false"
        :class="theme === 'dark' ? 'drop-down-selection-account drop-down-selection-account-dark' : 'drop-down-selection-account'">
      <li class="li" @click="getData()">
        <img src="@/assets/images/img_securities.png" class="ul-pic" alt />
        <span>{{$t("myAccount.transaction")}}</span>
      </li>
      <li class="li" @click="getData()">
        <img src="@/assets/images/icon_securities.png" class="ul-pic" alt />
        <span>{{$t("myAccount.securitiesAccount")}}</span>
      </li>
      <li class="li" @click="getData()">
        <img src="@/assets/images/icon_futures.png" class="ul-pic" alt />
        <span>{{$t("myAccount.futuresAccount")}}</span>
      </li>
    </ul> -->
    <!-- 弹出层 -->
    <ul v-show="isOrderPopUp" class="drop-down-selection">
      <li class="li" @click="getAllData()">
        <span>{{ $t("myAccount.allOrders") }}</span>
      </li>
      <li class="li" @click="getBuyData()">
        <span>{{ $t("myAccount.BuyOrder") }}</span>
      </li>
      <li class="li" @click="getSellData()">
        <span>{{ $t("myAccount.SellOrder") }}</span>
      </li>
      <li class="li" @click="getDividendData()">
        <span>{{ $t("myAccount.DividendOrder") }}</span>
      </li>
    </ul>
    <div class="fixed">
      <!-- <div class="page-title">
        <img :src="require(`@/assets/images/${theme}/icon_back_new.png`)" @click="goBack" alt="pic" class="return" />
        <span>{{ $t("myAccount.orderRecord") }}</span>
      </div> -->
      <!-- <div class="income-card" @click="isAccountPopUp = true">
        <span class="show-pic-left"></span>
        <span class="padding-20">{{ $t("myAccount.financialAccount") }}</span>
        <img :src="require(`@/assets/images/${theme}/com_ico_arrow.png`)" alt="pic" class="show-pic-right" />
      </div> -->
      <div class="select-card">
        <div class="item">
          <span
            v-show="isAllOrders"
            @click="isOrderPopUp = true"
            class="padding-147"
            >{{ $t("myAccount.allOrders") }}</span
          >
          <span
            v-show="isBuyOrder"
            @click="isOrderPopUp = true"
            class="padding-147"
            >{{ $t("myAccount.BuyOrder") }}</span
          >
          <span
            v-show="isSellOrder"
            @click="isOrderPopUp = true"
            class="padding-147"
            >{{ $t("myAccount.SellOrder") }}</span
          >
          <span
            v-show="isDividendOrder"
            @click="isOrderPopUp = true"
            class="padding-147"
            >{{ $t("myAccount.DividendOrder") }}</span
          >
          <img
            @click="isOrderPopUp = true"
            src="@/assets/images/international/arrow1_down_default.png"
            alt="pic"
            class="show-pic-left"
          />
        </div>
        <div class="item">
          <span @click="selectTimer">
            <span v-if="!isTimeParameter">{{ $t("myAccount.allDates") }}</span>
            <span v-else>{{ timeParameter }}</span>
          </span>
          <img
            @click="selectTimer"
            src="@/assets/images/international/arrow1_down_default.png"
            alt="pic"
            class="show-pic-left2"
          />
        </div>
      </div>
    </div>
    <div class="self-table">
      <div class="table-left-fixed">
        <div class="table-left-header">
          <span>{{ $t("myAccount.state") }}</span>
        </div>
        <div
          :class="
            orderList.content.length - 1 === index
              ? 'table-left-row border-none'
              : 'table-left-row'
          "
          v-for="(item, index) in orderList.content"
          :key="index"
          @click="gotoDetail(item)"
        >
          <div class="line1">
            <span>{{ $t(`myAccount.${item.orderType}` + "Order") }}</span>
          </div>
          <div class="line2">
            <span>{{ $t(`myAccount.${item.status}`) }}</span>
          </div>
        </div>
      </div>
      <!-- 表内容 多列 多行 -->
      <div class="table-right-scroll">
        <div
          class="table-right-column-body"
          v-for="(item, index) in table1"
          :key="index"
          :class="[locale === 'en' && index ==0 ? 'en-width' : '']"
        >
          <!-- <div
            v-if="index === 0"
            :class="
              table1[0].column.length
                ? 'table-right-column-header-none'
                : 'table-left-header'
            "
          ></div> -->
          <div v-if="index === 1" class="table-right-column-header">
            {{ $t("myAccount.name") }}
          </div>
          <div v-if="index === 2" class="text-right">
            <span>{{ $t("myAccount.amountOfMoney") }}</span>
            <img
              @click="showTip = true"
              :src="require(`@/assets/images/international/instruction2.png`)"
              class="icon-question"
            />
          </div>
          <div v-if="index === 3" class="text-right-last">
            {{ $t("myAccount.orderTime") }}
          </div>
          <div :class="sid === item.column.length - 1 ? 'table-row border-none' : 'table-row'"
            v-for="(sitem, sid) in item.column"
            :key="sid"
            @click="gotoDetail(sitem)"
          >
            <!-- <div class="line1-none" v-if="index === 0"></div>
            <div class="line2-none" v-if="index === 0"></div> -->

            <div class="line1" v-if="index === 1">
              {{ sitem.name ? sitem.name : "--" }}
            </div>
            <div class="line2" v-if="index === 1"></div>
            <div v-if="index === 2" class="table-row-right">
              <div class="money">
                <span v-if="sitem.amountOfMoney">{{
                  formatData(sitem.amountOfMoney)
                }}</span>
                <span v-else>--</span>
              </div>
              <div class="amount">
                <span v-if="sitem.quantity">{{
                  formatDataQuntity(sitem.quantity)
                }}</span>
                <span v-else>--</span>
              </div>
            </div>
            <!-- <div v-if="index === 2" class="table-row-right">
              <div><span>{{ sitem.amountOfMoney }}</span></div>
            </div> -->
            <div v-if="index === 3" class="table-row-right-last">
              <span>{{ sitem.orderTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!table1[0].column.length" class="fund-product-null">
      <noData></noData>
    </div>
    <div class="fixed-timer-select" v-if="isSelectTimer">
      <div class="mask" @click="isSelectTimer = false"></div>
      <timer-select
        :createdDate="createdDate"
        @cancel="cancelDialog"
        @timePassed="reGetData"
        @getData="getData"
        class="timer-select"
        ref="timerSelect"
      >
      </timer-select>
    </div>
    <loading v-if="isLoading"></loading>
    <md-popup v-model="showTip" :mask-closable="false" class="popup-tip">
      <div class="content">{{ $t("myAccount.Tips") }}</div>
      <md-button @click="showTip = false" type="primary" class="btn">{{ $t("myAccount.know") }}</md-button>
    </md-popup>
  </div>
</template>

<script>
import { getOrderRecordList } from "@/services/account.js";
import { mapState } from "vuex";
import timerSelect from "./dateDialog.vue";
import { fmoney } from "@/utils/util.js";
import moment from "moment";
import { Toast, Popup } from "mand-mobile";
import loading from "@/components/loading/index";
import { getAccountNumber } from "@/services/account";
import noData from "@/components/international/common/no-data";
export default {
  name: "interOrderRecord",
  components: { timerSelect, loading, [Popup.name]: Popup, noData },
  computed: {
    ...mapState(["theme", "locale", "accountNumber"]),
  },
  data() {
    return {
      createdDate: "",
      isAllOrders: true,
      isBuyOrder: false,
      isSellOrder: false,
      isDividendOrder: false,
      isTimeParameter: false,
      timeParameter: "",
      isLoading: false,
      isBuy: 1,
      isSelectTimer: false,
      orderList: [],
      alldata: [],
      isGetData: true,
      parameter: {
        accountNumber: "1",
        orderDateRangeStart: "",
        orderDateRangeEnd: "",
        orderType: "",
        page: 0,
        size: 15,
      },
      scroll: "null",
      isOrderPopUp: false,
      isAccountPopUp: false,
      table1: [{ column: [] }, { column: [] }, { column: [] }, { column: [] }],
      showTip: false,
    };
  },
  created() {
    this.isLoading = true;
    getAccountNumber(this).then((res) => {
      let object = res.data.tradingAccountList.find((item) => {
        return item.type === "FUND";
      });
      this.createdDate = object.createdDate.split(" ")[0];
      this.$store.commit("setAccountNumber", object.tradeAccountNumber);
      this.getData();
    });
  },
  mounted() {
    window.addEventListener("scroll", this.dataScroll, true);
  },
  destroyed() {
    if (document.body) {
      document.body.scrollTop = 0;
    }
    if (document.documentElement) {
      document.documentElement.scrollTop = 0;
    }
    window.removeEventListener("scroll", this.dataScroll, true);
  },
  methods: {
    dataScroll() {
      let ScrollHeight = this.getScrollHeight();
      let DocumentTop = this.getDocumentTop();
      let WindowHeight = this.getWindowHeight();
      if (ScrollHeight - (DocumentTop + WindowHeight) < 10) {
        if (this.isGetData) {
          return;
        }
        if (this.orderList.totalElements > this.parameter.size) {
          this.isGetData = true;
          this.isLoading = true;
          this.parameter.size = this.parameter.size + 15;
          this.getData();
        } else {
          return Toast.info(this.$t("myAccount.noMoreData"));
        }
      }
    },
    //文档高度
    getDocumentTop() {
      var scrollTop = 0,
        bodyScrollTop = 0,
        documentScrollTop = 0;
      if (document.body) {
        bodyScrollTop = document.body.scrollTop;
      }
      if (document.documentElement) {
        documentScrollTop = document.documentElement.scrollTop;
      }
      scrollTop =
        bodyScrollTop - documentScrollTop > 0
          ? bodyScrollTop
          : documentScrollTop;
      return scrollTop;
    },
    //可视窗口高度
    getWindowHeight() {
      var windowHeight = 0;
      if (document.compatMode == "CSS1Compat") {
        windowHeight = document.documentElement.clientHeight;
      } else {
        windowHeight = document.body.clientHeight;
      }
      return windowHeight;
    },

    //滚动条滚动高度
    getScrollHeight() {
      var scrollHeight = 0,
        bodyScrollHeight = 0,
        documentScrollHeight = 0;
      if (document.body) {
        bodyScrollHeight = document.body.scrollHeight;
      }
      if (document.documentElement) {
        documentScrollHeight = document.documentElement.scrollHeight;
      }
      scrollHeight =
        bodyScrollHeight - documentScrollHeight > 0
          ? bodyScrollHeight
          : documentScrollHeight;
      return scrollHeight;
    },
    gotoDetail(item) {
      this.$router.push({
        path: "/international/orderDetail",
        query: {
          id: item.orderId,
        },
      });
    },
    getAllData() {
      if (document.body) {
        document.body.scrollTop = 0;
      }
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      this.isLoading = true;
      this.isBuy = 1;
      this.parameter.orderType = "";
      this.parameter.size = 15;
      this.isAllOrders = true;
      this.isBuyOrder = false;
      this.isSellOrder = false;
      this.isDividendOrder = false;
      this.getData();
    },
    getBuyData() {
      if (document.body) {
        document.body.scrollTop = 0;
      }
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      this.isLoading = true;
      this.parameter.size = 15;
      this.isBuy = 2;
      this.parameter.orderType = "Buy";
      this.isAllOrders = false;
      this.isBuyOrder = true;
      this.isSellOrder = false;
      this.isDividendOrder = false;
      this.getData();
    },
    getSellData() {
      if (document.body) {
        document.body.scrollTop = 0;
      }
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      this.isLoading = true;
      this.parameter.size = 15;
      this.isBuy = 3;
      this.parameter.orderType = "Sell";
      this.isAllOrders = false;
      this.isBuyOrder = false;
      this.isSellOrder = true;
      this.isDividendOrder = false;
      this.getData();
    },
    getDividendData() {
      if (document.body) {
        document.body.scrollTop = 0;
      }
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      this.isLoading = true;
      this.parameter.size = 15;
      this.isBuy = 4;
      this.parameter.orderType = "Dividend";
      this.isAllOrders = false;
      this.isBuyOrder = false;
      this.isSellOrder = false;
      this.isDividendOrder = true;
      this.getData();
    },
    cancelDialog() {
      this.isSelectTimer = false;
    },
    reGetData(timer) {
      if (document.body) {
        document.body.scrollTop = 0;
      }
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      this.isTimeParameter = timer.allFlag;
      this.isLoading = true;
      this.parameter.size = 15;
      this.parameter.orderDateRangeStart = timer.startTime + "";
      this.parameter.orderDateRangeEnd = timer.endTime + "";
      this.timeParameter =
        this.parameter.orderDateRangeStart +
        " 至 " +
        this.parameter.orderDateRangeEnd;
      this.getData();
      this.isSelectTimer = false;
    },
    selectTimer() {
      this.isSelectTimer = true;
    },

    closeDialog() {
      this.isOrderPopUp = false;
      this.isAccountPopUp = false;
    },
    async getData() {
      this.table1 = [
        { column: [] },
        { column: [] },
        { column: [] },
        { column: [] },
      ];
      this.isOrderPopUp = false;
      this.isAccountPopUp = false;
      try {
        let requestParams = {
          accountNumber: this.accountNumber,
          page: this.parameter.page,
          size: this.parameter.size,
        };
        if (this.parameter.orderDateRangeStart) {
          requestParams.orderDateRangeStart =
            this.parameter.orderDateRangeStart;
          requestParams.orderDateRangeEnd = this.parameter.orderDateRangeEnd;
        }
        if (this.isBuy === 2) {
          requestParams.orderType = "Buy";
        } else if (this.isBuy === 3) {
          requestParams.orderType = "Sell";
        } else if (this.isBuy === 4) {
          requestParams.orderType = "Dividend";
        }
        await getOrderRecordList(requestParams, this).then((res) => {
          if (res.code === "200") {
            this.orderList = res.data;
            this.dataProcessing();
            this.isLoading = false;
            this.isGetData = false;
          }
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    formatData(data) {
      return data > 0 ? "+" + fmoney(data, 2) : fmoney(data, 2);
      // if (item.orderType == 'Buy' || item.orderType == 'Dividend') {
      //   if (quantitObject.amountOfMoney == 0) {
      //     quantitObject.amountOfMoney = '0.00'
      //   } else {
      //     quantitObject.amountOfMoney = "+" + fmoney(quantitObject.amountOfMoney, 2);
      //   }
      // } else {
      //   quantitObject.amountOfMoney = "-" + fmoney(quantitObject.amountOfMoney, 2);
      // }
    },
    formatDataQuntity(data) {
      return fmoney(data, 4);
    },
    dataProcessing() {
      // 清空 table1
      if (!this.orderList.content.length) {
        return Toast.info(this.$t("fundMessage.notMessage"));
      }
      this.table1.forEach((item, index) => {
        this.table1[index].column = [];
      });
      this.orderList.content.forEach((item) => {
        let typeObject = {};
        typeObject.category = item.orderType;
        typeObject.state = item.status;
        typeObject.orderId = item.orderId;
        this.table1[0].column.push(typeObject);

        let nameObject = {};
        nameObject.name = this.locale === "zh-hans" ? item.productName.cn : (this.locale === "zh-hant" ? item.productName.hk : item.productName.us);
        nameObject.orderId = item.orderId;
        this.table1[1].column.push(nameObject);

        let quantitObject = {};
        quantitObject.amountOfMoney = Number(item.amount);
        quantitObject.quantity = Number(item.quantity);
        quantitObject.orderId = item.orderId;
        // if (item.orderType == 'Buy' || item.orderType == 'Dividend') {
        //   if (quantitObject.amountOfMoney == 0) {
        //     quantitObject.amountOfMoney = '0.00'
        //   } else {
        //     quantitObject.amountOfMoney = "+" + fmoney(quantitObject.amountOfMoney, 2);
        //   }
        // } else {
        //   quantitObject.amountOfMoney = "-" + fmoney(quantitObject.amountOfMoney, 2);
        // }
        this.table1[2].column.push(quantitObject);

        let timeObject = {};
        timeObject.orderId = item.orderId;
        timeObject.orderTime = moment(item.orderTime).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.table1[3].column.push(timeObject);
      });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.international {
  background: var(--background);
}
.dialog-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  z-index: 6000;
}
.drop-down-selection {
  font-size: 28px;
  color: var(--text_3rd);
  background: var(--background);
  box-shadow: 0px 0px 8px 0px rgba(28,33,42,0.1);
  border-radius: 30px;
  text-align: center;
  padding: 18px 0;
  z-index: 6001;
  position: fixed;
  left: 60px;
  top: 100px;
  &:before{
    position: absolute;
    top: -0.15rem;
    left: 0.5rem;
    content: "";
    border-bottom: 15px solid var(--background);
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
  }
  .li {
     padding: 18px 40px;
  }
}
.fixed {
  background: var(--background);
  z-index: 1400;
  width: 100%;
  position: fixed;
  top: 0px;
  left: 0px;
  .income-card {
    height: 74px;
    line-height: 74px;
    width: 100%;
    position: relative;
    text-align: left;
    font-size: 28px;
    .padding-20 {
      padding-left: 40px;
    }
    .show-pic-left {
      background-image: url("~@/assets/images/img_securities.png");
      background-size: 100% 100%;
      display: inline-block;
      width: 30px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 0px;
    }
    .show-pic-right {
      width: 18px;
      height: 10px;
      position: absolute;
      top: 50%;
      margin-top: -5px;
      right: 0px;
    }
  }

  .select-card {
    height: 88px;
    line-height: 88px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      color: var(--text_1st);
    }
    .show-pic-left {
      width: 36px;
      height: 36px;
      vertical-align: middle;
      margin-left: 8px;
    }
    .show-pic-left2 {
      width: 36px;
      height: 36px;
      vertical-align: middle;
      margin-left: 8px;
    }
  }
}
.index-page {
  padding-top: 104px;
  background: var(--background);
  .self-table {
    background: var(--background);
    // width: calc(100% - 60px);
    margin: 0px 30px;
    // position: relative;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .table-left-fixed {
      width: 200px;
      font-size: 0px;
      background: var(--background);
      // position: absolute;
      // left: 0px;
      // top: 0px;
      // margin: 0px;
      // z-index: 999;
      .table-left-header {
        width: 200px;
        color: var(--text_3rd);
        height: 60px;
        line-height: 60px;
        text-align: left;
        font-size: 22px;
        padding-right: 40px;
      }
      .table-left-row {
        border-bottom: 1px solid var(--line_01);
        padding: 18px 0px;
        .line1 {
          text-align: left;
          height: 30px;
          line-height: 30px;
          // padding-right: 40px;
          font-size: 26px;
          color: var(--text_1st);
        }
        .line2 {
          color: var(--text_2nd);
          text-align: left;
          height: 48px;
          line-height: 48px;
          // padding-right: 40px;
          font-size: 24px;
        }
      }
      .border-none {
        border: none !important;
      }
    }
    .fund-product-null {
      text-align: center;
      margin-top: 137px;
      img {
        width: 347px;
        height: 200px;
      }
    }
    .table-right-scroll {
      font-size: 0px;
      overflow: auto;
      overflow-y: hidden;
      white-space: nowrap;
      div::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }
      .table-right-column-body {
        font-size: 0px;
        display: inline-block;
        vertical-align: bottom;
        .table-right-column-header {
          color: var(--text_3rd);
          height: 60px;
          line-height: 60px;
          text-align: left;
          padding-left: 20px;
          font-size: 22px;
          padding-right: 40px;
        }
        .table-right-column-header-none {
          color: var(--text_3rd);
          height: 60px;
          line-height: 60px;
          text-align: left;
          padding-left: 20px;
          font-size: 22px;
          padding-right: 112px;
        }
        .table-left-header {
          color: var(--text_3rd);
          height: 60px;
          line-height: 60px;
          text-align: left;
          font-size: 22px;
          padding-right: 84px;
        }
        .text-right {
          color: var(--text_3rd);
          height: 60px;
          line-height: 60px;
          text-align: right;
          padding-left: 20px;
          font-size: 22px;
          padding-right: 20px;
        }
        .text-right-last {
          color: var(--text_3rd);
          height: 60px;
          line-height: 60px;
          text-align: right;
          padding-left: 40px;
          font-size: 22px;
          padding-right: 20px;
        }
        .table-row {
          font-size: 0px;
          position: relative;
          border-bottom: 1px solid var(--line_01);
          padding: 18px 0px;
          .line1-none {
            text-align: left;
            padding-left: 20px;
            height: 30px;
            line-height: 30px;
            padding-right: 112px;
            font-size: 26px;
          }
          .line2-none {
            color: var(--text_3rd);
            text-align: left;
            padding-left: 20px;
            height: 48px;
            line-height: 48px;
            padding-right: 112px;
            font-size: 24px;
          }
          .line1 {
            text-align: left;
            padding-left: 20px;
            height: 30px;
            line-height: 30px;
            padding-right: 40px;
            font-size: 26px;
            color: var(--text_1st);
          }
          .line2 {
            color: var(--gray_01);
            text-align: left;
            padding-left: 20px;
            height: 48px;
            line-height: 48px;
            padding-right: 40px;
            font-size: 24px;
          }
          .table-row-right {
            height: 78px;
            padding-left: 20px;
            padding-right: 20px;
            padding-top: 13px;
            padding-bottom: 24px;
            text-align: right;
            .money {
              font-size: 26px;
              line-height: 40px;
              color: var(--text_1st);
            }
            .amount {
              color: var(--text_1st);
              font-size: 24px;
            }
          }
          .table-row-right-last {
            color: var(--text_1st);
            height: 78px;
            line-height: 78px;
            padding-left: 40px;
            padding-right: 20px;
            font-size: 22px;
            text-align: right;
          }
        }
        .border-none {
          border: none !important;
        }
      }
      .en-width {
        // width: 240px;
      }
    }
  }
  .fixed-timer-select {
    z-index: 1600;
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0px;
    top: 0px;
    .mask {
      background-color: var(--mask);
      z-index: 1500;
      width: 100%;
      height: 100%;
    }
    .timer-select {
      position: absolute;
      bottom: 0px;
      left: 0px;
      width: 100%;
      border-radius: 40px 40px 0 0;
    }
  }
}
.icon-question {
  width: 24px;
  height: 24px;
  line-height: 1;
  vertical-align: -2px;
  margin-left: 6px;
}
.popup-tip {
  z-index: 1500;
  ::v-deep.md-popup-box {
    padding: 48px 40px;
    z-index: 1502;
    background: var(--background);
    border-radius: 30px;
  }
  .content {
    width: 470px;
    font-size: 28px;
    color: var(--text_1st);
    line-height: 40px;
    text-align: left;
  }

  ::v-deep.md-button {
    border-radius: 40px;
    color: var(--text_5th);
    margin-top: 40px;
    height: 80px;
  }
}
</style>
