<template>
  <div class="company-details-box">
    <div v-if="companyDetail && companyDetail.pictureUrl" class="company-header-backImage">
      <img :src="companyDetail && companyDetail.pictureUrl ? companyDetail.pictureUrl : ''" alt=""/>
      <div v-if="companyDetail && companyDetail.logoUrl" class="company-details-header">
        <img :src=" companyDetail && companyDetail.logoUrl ? companyDetail.logoUrl : ''" alt=""/>
        <div class="company-details-message">
          <p class="company-details-title">
            {{ companyDetail.name[localeType[locale]] ? companyDetail.name[localeType[locale]] : "-" }}
          </p>
        </div>
      </div>
    </div>
    <div class="company-synopsis-product">
      <div class="company-synopsis-box">
        <div :class="synopsisShowAll ? 'company-synopsis-content-close' : 'company-synopsis-content'">
          {{ companyDetail && companyDetail.introduce ? companyDetail.introduce : "-" }}
        </div>
        <div class="company-synopsis-label">
          <span @click="synopsisShowAll = !synopsisShowAll">{{ synopsisShowAll ? $t('fundMessage.packUp') : $t('fundMessage.more') }}</span>
        </div>
      </div>
      <!-- 旗下产品数据 -->
      <div class="company-synopsis-list-box">
        <div class="company-synopsis-border">
          {{ $t('fundMessage.subordinateFund') }}
        </div>
        <!-- 数据列表 -->
        <ul class="fund-debenture" v-if="companyFunsList && companyFunsList.length > 0">
          <li class="fund-debenture-list">
            <div v-for="(item, index) in companyFunsList" :key="index" class="fund-debenture-details" @click="fundDetails(item)">
              <div class="fund-debenture-header">
                <p class="fund-debenture-name">
                  {{ item.name[localeType[locale]] ? item.name[localeType[locale]] : "-" }}
                </p>
                <p class="fund-debenture-incomeRatio"
                :class="{
                  'fund-depreciate': item.cumulative1Y == 0 || !item.cumulative1Y,
                  'fund-depreciate-green':
                    (klineTheme === 'greenRiseRedFall' &&
                      item.cumulative1Y > 0) ||
                    (klineTheme === 'redRiseGreenFall' &&
                      item.cumulative1Y < 0),
                  'fund-depreciate-red':
                    (klineTheme === 'redRiseGreenFall' &&
                      item.cumulative1Y > 0) ||
                    (klineTheme === 'greenRiseRedFall' &&
                      item.cumulative1Y < 0),
                  }"
                  >
                  <!-- <span
                    v-if="item.type == 'MONEY_MARKET'"
                    :class="{ 'fund-depreciate': item.annualised7Day < 0 }"
                  >
                    {{
                      item.annualised7Day > 0
                        ? '+' + (item.annualised7Day*100).toFixed(2)
                        : (item.annualised7Day*100).toFixed(2)
                    }}%
                  </span> -->
                  <span>
                    {{ item.cumulative1Y > 0 ? "+" + (item.cumulative1Y * 100).toFixed(2) : (item.cumulative1Y * 100).toFixed(2) }}%
                  </span>
                </p>
              </div>
              <div class="fund-debenture-header">
                <div class="fund-debenture-property">
                  <p>{{ item.currencyType }}</p>
                  <p>{{ bondType(item.type, $t("fundMessage.bondType")) }}</p>
                  <p :class='item.riskLevel == 5 ? "error":"warning"'>
                    {{ riskLevel(item.riskLevel, $t("fundMessage.fundRiskLevel")) }}
                  </p>
                </div>
                <div class="fund-debenture-amount">
                  {{ $t("fundMessage.cumulative1Y") }}
                </div>
              </div>
            </div>
          </li>
        </ul>
        <ul v-else>
          <FundNotHave
            v-if="companyFunsList.length <= 0"
            :subordinateImage="true"
            :synopsisShowAll="synopsisShowAll"
          ></FundNotHave>
        </ul>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { Toast } from "mand-mobile";
import { getCompaniesDetails_funds } from "@/services/fund";
import FundNotHave from "@/components/international/fund-not-have";
import Loading from "@/components/international/loading/index";
import { riskLevel, bondType } from "@/utils/fund-type";
import { getAccountNumber } from "@/services/account";
export default {
  components: {
    FundNotHave,
    Loading,
  },
  data() {
    return {
      companyUrl: "", // 获取当前企业传递数据
      companyDetail: null, // 获取当前企业详情
      synopsisShowAll: false,
      companyFunsList: [], // 债券基金
      isLoading: true,
      fundType: null,
      pageNum: 0,
      pageSize: 15,
      localeType: {
        "zh-hans": "cn",
        "zh-hant": "hk",
        en: "us",
      },
    };
  },
  mounted() {
    this.companyUrl = this.$route.query;
    this.companiesFunds("detail"); // 详情
    this.companiesFunds("funds"); // 旗下基金
    this.getAccountNumber();
  },
  computed: {
    ...mapState(["locale", "klineTheme"]),
  },
  methods: {
    async getAccountNumber() {
      try {
        await getAccountNumber(
          this,
          this.$jsBridge.isSupported("getAppInfo")
        ).then((res) => {
          let object = res.data.tradingAccountList.find((item) => {
            return item.type === "FUND";
          });
          this.$store.commit("setAccountNumber", object.tradeAccountNumber);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    companiesFunds(type) {
      // 基金公司--详情 & 旗下基金
      this.isLoading = true;
      var params = {};
      if (type == "detail") {
        params = {
          "Accept-Languages": this.locale == "zh-hans" ? "zh-CN" : "zh-HK",
        };
      } else {
        params = {
          size: this.pageSize,
          page: this.pageNum,
          "Accept-Languages": this.locale == "zh-hans" ? "zh-CN" : "zh-HK",
        };
      }
      getCompaniesDetails_funds(params, "", this.companyUrl.companyId, type)
        .then((res) => {
          // console.log(res);
          this.isLoading = false;
          if (res.code == 200) {
            if (type == "detail") {
              this.companyDetail = res.data;
            } else {
              this.companyFunsList = res.data.content;
            }
          } else {
            Toast({ content: res.msg });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    fundDetails(data) {
      // console.log(data)
      // data = Object.assign(data, { currentType: 2 })
      this.$router.push({
        path: "/international/productDetails",
        query: { fundIsin: data.fundIsin, currentType: "all" },
      });
    },
    bondType(type, data) {
      // 基金类型
      let newText = "-";
      newText = bondType(type, data);
      return newText;
    },
    riskLevel(type, data) {
      // 基金风险等级
      let newText = "-";
      newText = riskLevel(type, data);
      return newText;
    },
  },
};
</script>

<style lang="scss" scoped>
.company-details-box {
  opacity: 1;
  height: 100%;
  // overflow: auto;
  color: var(--text_5th);
  .company-header-backImage {
    opacity: 1;
    position: relative;
    height: 324px;
    opacity: 0.8;
    img {
      width: 100%;
      height: 100%;
    }
    .company-details-header {
      height: 128px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: absolute;
      top: 60px;
      left: 30px;
      right: 30px;
      img {
        width: 214px;
        height: 100%;
        border-radius: 8px;
        margin-right: 21px;
      }
      .company-details-image {
        width: 214px;
        height: 128px;
        border-radius: 8px;
        overflow: hidden;
        @include background_color(bg-color);
      }
      .company-details-message {
        // width: 446px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .company-details-title {
          font-size: 28px;
          font-weight: 600;
          @include font_color(white-text);
        }
        .company-details-introduce {
          font-size: 22px;
          margin-top: 20px;
        }
      }
    }
  }

  // .company-synopsis-bg {
  //   // @include background_color(fund-bg);
  // }
  .company-synopsis-product {
    position: absolute;
    top: 248px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0 32px;
    background: var(--background);
    border-radius: 20px 20px 0px 0px;
    // border-radius: 40px 40px 0px 0px;
    color: var(--text_1st);
    .company-synopsis-box {
      // width: 690px;
      // height: 197px;
      padding: 40px 0;
      font-size: 26px;
      .company-synopsis-content {
        // height: 100px;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .company-synopsis-content,
      .company-synopsis-content-close {
        text-indent: 50px;
        line-height: 39px;
      }
      .company-synopsis-label {
        // margin-top: 10px;
        color: var(--brand_01);
        text-align: right;
      }
    }
    .company-synopsis-list-box {
      .company-synopsis-border {
        font-size: 34px;
        font-weight: 500;
        color: var(--text_1st);
        font-style: normal;
        letter-spacing: 0px;
        text-align: left;
        line-height: 54px;
        margin-bottom: 32px;
      }
      .fund-debenture {
        opacity: 1;
        // @include box_shadow(shadow-color);
        // box-shadow: 0px 3px 12px 0px #ECF1F8;
        .fund-debenture-list {
          // @include background_color(second-bg);
          .fund-debenture-details {
            padding: 32px 0;
            border-bottom: 1px solid;
            border-color: var(--line_01);
            .fund-debenture-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .fund-debenture-name {
                font-size: 28px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: var(--text_1st);
              }
              .fund-debenture-incomeRatio {
                font-size: 30px;
                padding: 8px 16px;
                font-weight: normal;
                border-radius: 32px;
                opacity: 1;
                color: var(--background);
              }
              .fund-depreciate {
                background: var(--gray_02);
              }
              .fund-depreciate-green {
                background-color: var(--green);
              }
              .fund-depreciate-red {
                background-color: var(--red);
              }
              .fund-debenture-property {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 12px;
                font-size: 22px;
                p {
                  color: var(--brand_01);
                  border: 1px solid var(--brand_01);
                  border-radius: 6px;
                  line-height: 38px;
                  margin-right: 20px;
                  padding: 0 14px;
                  &.warning {
                    color: var(--warning);
                    border: 1px solid var(--warning);
                  }
                  &.error {
                    color: var(--error);
                    border: 1px solid var(--error);
                  }
                }
              }
              .fund-debenture-amount {
                font-size: 22px;
                margin-top: 22px;
                color: var(--text_3rd);
              }
            }
          }
        }
      }
    }
  }
}
</style>
