<template>
  <div class="fund-company-box">
    <template v-if="companyLists && companyLists.length > 0">
      <ul class="fund-company-list-box">
        <li v-for="(item, index) in companyLists" :key="index" class="fund-company-single" @click="goDetails(item)">
          <div
            class="fund-company-image"
            :class="{ 'image-box-shoadow': marketTheme == 'light' }"
          >
            <img v-if="item.logoUrl" :src="item.logoUrl" alt="" />
          </div>
          <div class="fund-company-describe">
            <p class="fund-company-title">
              {{ fundsNameFun(item) }}
            </p>
            <p class="fund-company-introduce">
              {{item.introduce}}
            </p>
          </div>
        </li>
      </ul>
      <div v-if="totalDetails" class="fund-company-all">
        {{ totalDetails ? $t('myAccount.loadIsComplete') : $t('myAccount.loadMore') }}
      </div>
      <div v-else class="fund-company-all" @click="toLoadCompany">
        {{ totalDetails ? $t('myAccount.loadIsComplete') : $t('myAccount.loadMore') }}
      </div>
    </template>
    <fund-not-have
      v-if="companyLists.length === 0"
      :companyImage="true"
    ></fund-not-have>
    <loading v-if="isLoading"></loading>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCompanies } from '@/services/fund'
import FundNotHave from '@/components/international/fund-not-have'
import Loading from '@/components/international/loading/index'

export default {
  components: {
    FundNotHave,
    Loading,
  },
  data() {
    return {
      companyLists: [],
      pageNum: 0,
      pageSize: 15,
      marketTheme: 'light',
      totalDetails: false,
      isLoading: true,
    }
  },
  computed: {
    ...mapState(['theme', 'locale']),
  },
  mounted() {
    this.marketTheme = localStorage.getItem('theme')
    this.toLoadCompany()
  },

  methods: {
    fundsNameFun(data) {
      if (this.locale == 'zh-hans') {
        return data.name && data.name.cn ? data.name.cn : (data.name.hk ? data.name.hk : '-')
      } else if (this.locale == 'zh-hant') {
        return data.name && data.name.hk ? data.name.hk : '-'
      } else {
        return data.name && data.name.us ? data.name.us : '-'
      }
    },
    goDetails(data) {
      // console.log('公司详情=====', data);
      this.$router.push({
        path: '/international/fundCompanyDetails',
        query: { companyId: data.companyId },
      })
    },
    toLoadCompany() {
      // 基金公司数据
      // this.totalDetails = true;
      this.isLoading = true
      if (this.totalDetails) {
        return
      } else {
        getCompanies({
          page: this.pageNum,
          size: this.pageSize,
          exitsFunds: false,
        })
          .then((res) => {
            this.isLoading = false;
            this.companyLists = this.companyLists.concat(res.data.content);
            if (this.companyLists.length >= res.data.totalElements) {
              this.totalDetails = true
            } else {
              this.pageNum += 1
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.fund-company-box {
  height: 100%;
  overflow: auto;
  color: var(--text_2nd);
  .fund-company-list-box {
    background: var(--background);
    .fund-company-single {
      padding: 40px 32px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      background: var(--background);
      .fund-company-image {
        width: 214px;
        height: 128px;
        margin-right: 26px;
        overflow: hidden;
        border-radius: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .image-box-shoadow {
        box-shadow: 0px 0px 8px rgba(28,33,42, 0.1);
        // box-shadow: 0px 3px 12px #ecf1f8;
      }
      .fund-company-describe {
        flex:1;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        justify-content: start;
        .fund-company-title {
          overflow: hidden;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
          word-break:break-all;/*打断单词*/
          // word-wrap:break-word;/*允许长单词换行*/
          display: -webkit-box;
          -webkit-line-clamp: 1;  
          -webkit-box-orient: vertical;
          color: var(--text_1st);
          font-size: 28px;
          font-weight: 500;
          line-height: 44px;
        }
        .fund-company-introduce {
          margin-top: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;  
          -webkit-box-orient: vertical;
          font-size: 22px;
          color: var(--text_3rd);
          line-height: 36px;
          word-break: break-word;
        }
      }
    }
  }
  .fund-company-all {
    font-size: 24px;
    line-height: 40px;
    text-align: center;
    margin: 20px 0;
    color: var(--text_1st);
  }
}
</style>
