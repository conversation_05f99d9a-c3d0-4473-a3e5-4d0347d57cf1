<template>
  <div class="index-page">
    <div class="backgroun-opacity"></div>
    <div class="search-box">
      <div class="title header-close-title">
        <span></span>
        <span class="title-content">{{ $t('select.screen') }}</span>
        <div class="close-png" @click="closeScreeningConditions">
          <img src="@/assets/images/search_close.png" alt="">
        </div>
        <!-- <md-icon
          class="close-png"
          @click="closeScreeningConditions"
          size="24px"
          name="close"
        ></md-icon> -->
      </div>
      <ul class="main">
        <!-- 交易货币 -->
        <li>
          <p class="title">{{ $t('select.transactionCurrency') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in transactionCurrency"
              :key="index"
              class="piece"
              :class="
                requestDetails.currencyType.includes(item.type)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectTransactionCurrency(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <!-- 风险等级 -->
        <li>
          <p class="title">{{ $t('select.riskLevel') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in riskLevel"
              :key="index"
              class="piece"
              :class="
                requestDetails.riskLevel.includes(item.type)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectRiskLevel(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <li>
          <p class="title">{{ $t('select.whetherToPayDividends') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in whetherToPayDividends"
              :key="index"
              class="piece"
              :class="
                requestDetails.dividend === item.type
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectWhetherToPayDividends(item)"
            >
              {{ $t('select.' + item.content) }}
            </div>
          </div>
        </li>
        <li>
          <p class="title">{{ $t('select.fundCompany') }}</p>
          <div class="content-button">
            <div
              v-for="(item, index) in fundCompany"
              :key="index"
              class="piece"
              :class="
                requestDetails.fundCompanyIds.includes(item.companyId)
                  ? 'iselect-piece'
                  : 'noselect-piece'
              "
              @click="selectFundCompany(item)"
            >
              {{fundsNameFun(item, 'name')}}
            </div>
          </div>
        </li>
      </ul>
      <div class="button-line">
        <div class="reset" @click="closeScreeningConditions('clear')">{{ $t('common.btns.reset') }}</div>
        <div class="confirm" @click="closeScreeningConditions('confirm')">
          {{ $t('fundMessage.affirmBtn') }}
        </div>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCompanies } from '@/services/fund'
import Loading from '@/components/loading/index.vue'
export default {
  name: 'screeningConditions',
  components: {
    Loading,
  },
  props: {
    isScreeningConditions: {
      type: Boolean,
    },
    currentTypeTab: {
      type: String,
    },
  },
  data() {
    return {
      transactionCurrency: [
        { type: 'HKD', content: 'hongKongDollar' },
        { type: 'USD', content: 'dollar' },
      ],
      riskLevel: [
        { type: 1, content: 'lowRisk' },
        { type: 2, content: 'mediumAndLowRisk' },
        { type: 3, content: 'mediumRisk' },
        { type: 4, content: 'mediumAndHighRisk' },
        { type: 5, content: 'highRisk' },
      ],
      whetherToPayDividends: [
        { type: true, content: 'dividend' },
        { type: false, content: 'noDividend' },
      ],
      fundCompany: [],
      requestDetails: {
        currencyType: [], // 交易货币
        riskLevel: [], // 风险等级
        dividend: '', // 是否派息
        fundCompanyIds: [],
      },
      isLoading: false,
    }
  },
  watch: {
    isScreeningConditions(newV, oldV) {
      if (newV) {
        this.isLoading = true
        getCompanies({
          page: 0,
          size: 999,
          exitsFunds: true,
        })
          .then((res) => {
            // console.log('基金公司===', res)
            if (res.code == 200) {
              this.fundCompany = res.data.content
            }
          })
          .catch((err) => {
            console.log(err)
          })
        this.isLoading = false
      }
    },
    currentTypeTab(newV, oldV) {
      // console.log('tab切换=====', newV, oldV)
      this.requestDetails = {
        currencyType: [],
        riskLevel: [],
        dividend: '',
        fundCompanyIds: [],
      }
    },
  },
  computed: {
    ...mapState(['locale']),
  },
  methods: {
    closeScreeningConditions(data) {
      switch (data) {
        case 'confirm':
          this.$emit('closeScreeningConditions', {
            isShow: false,
            requestDetails: this.requestDetails,
          })
          break
        case 'clear':
          this.requestDetails = {
            currencyType: [],
            riskLevel: [],
            dividend: '',
            fundCompanyIds: [],
          }
          break
        default:
          this.requestDetails = {
            currencyType: [],
            riskLevel: [],
            dividend: '',
            fundCompanyIds: [],
          }
          this.$emit('closeScreeningConditions', {
            isShow: false,
            requestDetails: this.requestDetails,
          })
          break
      }
    },
    selectTransactionCurrency(data) {
      // 交易货币
      // console.log(data);
      this.screenData(this.requestDetails.currencyType, data.type)
    },
    selectRiskLevel(data) {
      // 风险等级
      // console.log(data);
      this.screenData(this.requestDetails.riskLevel, data.type)
    },
    selectWhetherToPayDividends(data) {
      // 是否派息
      // console.log(data);
      this.requestDetails.dividend = data.type
    },
    selectFundCompany(data) {
      // 基金公司
      // console.log(data);
      this.screenData(this.requestDetails.fundCompanyIds, data.companyId)
    },

    // 筛选条件选择
    screenData(arr, data) {
      if (arr.includes(data)) {
        // 存在则删除
        if (arr.length > 1) {
          // 至少保留一个
          arr.forEach((ele, i) => {
            if (ele == data) {
              arr.splice(i, 1)
            }
          })
        }
      } else {
        // 不存在则添加
        arr.push(data)
      }
    },
    fundsNameFun(data) {
      if (this.locale == 'zh-hans') {
        return data.name && data.name.cn ? data.name.cn : '-'
      } else if (this.locale == 'zh-hant') {
        return data.name && data.name.hk ? data.name.hk : '-'
      } else {
        return data.name && data.name.us ? data.name.us : '-'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.index-page {
  .backgroun-opacity {
    height: 100%;
    background: var(--mask);
  }
  .search-box {
    height: 92%;
    position: absolute;
    bottom: 0;
    background: var(--background);
    border-radius: 40px 40px 0 0;
    display: flex;
    flex-direction: column;
    .header-close-title {
      padding: 0 40px;
    }
    .title {
      text-align: center;
      line-height: 120px;
      font-size: 32px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .close-png {
        img {
          width: 28px;
          height: 28px;
        }
      }
    }
    .main {
      flex: 1;
      overflow: auto;
      padding: 0px 30px;
      li {
        .title {
          color: var(--text_1st);
          font-weight: 500;
          line-height: 94px;
          text-align: left;
          font-size: 30px;
        }
        .content-button {
          display: flex;
          justify-content: left;
          flex-wrap: wrap;
          .piece {
            font-size: 28px;
            padding: 10px 48px;
            line-height: 44px;
            margin-right: 24px;
            margin-bottom: 24px;
            border-radius: 32px;
            max-width: calc(100% - 96px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .iselect-piece {
            color: var(--background);
            background: var(--brand_01);
          }
          .noselect-piece {
            color: var(--text_3rd);
            background: var(--gray_05);
          }
        }
      }
    }
    .button-line {
      padding: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid var(--line_01);
      .reset {
        background-color: var(--background);
        color: var(--text_1st);
        border: 1px solid var(--line_01);
        width: 328px;
        height: 80px;
        border-radius: 40px;
        text-align: center;
        line-height: 80px;
      }
      .confirm {
        background-color: var(--brand_01);
        color: var(--text_5th);
        border: 1px solid var(--brand_01);
        width: 328px;
        height: 80px;
        border-radius: 40px;
        text-align: center;
        line-height: 80px;
      }
    }
  }
}
</style>
