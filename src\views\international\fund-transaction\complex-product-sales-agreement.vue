<template>
  <div class="sales-agreement">
    <!-- <div class="title">{{agreementBoolean(agreementTextObj.clauseName)}}</div>
    <div class="content">{{agreementBoolean(agreementTextObj.clauseVersion)}}</div>
    <div class="content">{{agreementTextObj.needTick ? '是' : '否'}}</div>
    <div class="content">{{agreementTextObj.needUserInfo ? '是' : '否'}}</div>
    <div class="content">{{agreementBoolean(agreementTextObj.path)}}</div>
    <div class="content">{{agreementBoolean(agreementTextObj.platform)}}</div>
    <div class="content">{{agreementBoolean(agreementTextObj.platformVersion)}}</div> -->
    <div class="title" v-html="agreementTextObj"></div>
    <div class="button-bottom">
      <md-button type="default" round @click="goToTransaction(0)">{{ $t('common.btns.cancel') }}</md-button>
      <md-button type="primary" round @click="goToTransaction(1)">{{ $t('myAccount.complexProductAgreement') }}</md-button>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import Loading from '@/components/loading/index'
import { agreementContent, agreementH5 } from '@/services/common'
export default {
  components: {
    Loading,
  },
  props: {
    salesData: {
      type: Object
    }
  },
  data() {
    return {
      isisRouteBuy: '',
      fundIsin: '',
      isRouteBuy: '',
      isLoading: true,
      agreementTextObj: '', // 条款协议内容
    }
  },
  created() {
    // this.isisRouteBuy = this.$route.query.isisRouteBuy
    // this.fundIsin = this.$route.query.fundIsin
    // this.isRouteBuy = this.$route.query.isRouteBuy
    this.fundIsin = this.salesData.fundIsin
  },
  methods: {
    goToTransaction(num) {
      // this.$router.push({
      //   path: this.$route.query.urlType == 'sell' ? '/transactionSell' : '/transactionBuy',
      //   query: {
      //     isAgree: num,
      //     isisRouteBuy: this.isisRouteBuy,
      //     fundIsin: this.fundIsin,
      //     isRouteBuy: this.isRouteBuy,
      //   },
      // })
      this.$emit('inputPasswordShow', {
          isAgree: num
        })
    },
    // 数据判空
    agreementBoolean(data) {
      if (data) {
        return data
      } else {
        return '-'
      }
    },
  },
  mounted() {
    // this.hideNav() // 隐藏标题栏
    agreementContent({
      platform: 'H5', // 客户端类型,可用值:IOS,Android,PC,H5,Web,NONE
      type: 'Complex_Products_Warning_Statements', // 协议条款类型
    }).then((res) => {
      console.log(res)
      if (res.code == 200) {
        // this.agreementTextObj = res.data;
        agreementH5(res.data.path).then((data) => {
          // console.log('协议内容=====', data);
          let html = data.data
          html = html.split('<body>')[1].split('/<body>')[0]
          this.agreementTextObj = html
        })
      }
      this.isLoading = false
    })
  },
}
</script>

<style lang="scss" scoped>
.sales-agreement {
  position: relative;
  color: var(--text_1st);
  background: var(--background);
  padding: 0px 30px;
  .title {
    width: 100%;
    margin-top: 30px;
    line-height: 42px;
    margin-bottom: 174px;
    font-size: 30px;
    text-align: left;
    text-indent: 60px;
    font-weight: 500;
  }
  .content {
    margin-top: 20px;
    font-weight: 400;
    font-size: 26px;
    line-height: 37px;
    text-align: left;
  }
  .title2 {
    margin-top: 60px;
  }
  .button-bottom {
    padding: 24px 32px;
    background: var(--background);
    width: 100%;
    position: fixed;
    bottom: 0px;
    left: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
