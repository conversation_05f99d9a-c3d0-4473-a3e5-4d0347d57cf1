<template>
  <div class="index-page">
    <div v-show="!isShowSalesAgreement && !isShowComplexProducSalesAgreement">
      <loading v-if="isLoading"></loading>
      <ed-row
        :contentLeft="enumeration.accountName"
        :contentRight="accountContent"
        :borderStyle="true"
      >
        <div>{{ $t(accountContent) }}</div>
      </ed-row>
      <ed-row :contentLeft="enumeration.productName">
        <span>{{ !productNameContent ? "--" : productNameContent }}</span>
      </ed-row>
      <div class="stock-yield">
        {{ $t(enumeration.yieldInRecentYear) }}
        <span
        :class="
            klineTheme === 'redRiseGreenFall'
              ? (Number(yieldInRecentYearShow) < 0
                ? 'output'
                : Number(yieldInRecentYearShow) == 0
                ? ''
                : 'entry')
              : Number(yieldInRecentYearShow) > 0
              ? 'output'
              : Number(yieldInRecentYearShow) == 0
              ? ''
              : 'entry'
          "
        >{{ yieldInRecentYearContent }}</span>
      </div>
      <div class="bg-row"></div>
      <div class="row-money-page">
        <span>{{ $t("myAccount." + enumeration.tradingTitle) }}</span>
      </div>

      <!-- IOS delete inputValue后， placeholder 不显示，加上这个就好了，暂时这么处理 -->
      <div class="input-mask">
        <md-input-item is-virtual-keyboard v-model="inputValue"></md-input-item>
      </div>
      <!-- 申购赎回 输入金额 -->
      <div class="row-num-page">
        <span class="monetary-unit" v-if="funddata.currencyType">{{funddata.currencyType}}</span>
        <div class="wrap">
          <span class="left">
            <md-input-item
              title=""
              ref="input11"
              class="amout"
              :placeholder="placeholder"
              is-virtual-keyboard
              v-model="inputValue"
              virtual-keyboard-vm="myNumberKeyBoard"
            ></md-input-item>
            <span v-show="isKeyBoardShow" class="masker"></span>
          </span>
          <span class="content-right" @click="buyOrSell()">{{
            $t("myAccount." + buyOrSellNum)
          }}</span>
        </div>
      </div>
      <md-number-keyboard
        ref="myNumberKeyBoard"
        v-model="isKeyBoardShow"
        @enter="onNumberEnter"
        @delete="onNumberDelete"
      >
      </md-number-keyboard>

      <div class="row-num2-page">
        <span class="title">
          <span>{{ $t("myAccount." + currentQuantity) }}</span>
          <span>{{ MaximumRedemptionValue }}</span>
          <span>{{ company === "" ? "" : $t("myAccount." + company) }}</span>
        </span>
        <span :class="{'lang-en': $store.state.locale === 'en'}">
          <span class="red" v-show="isAscensionShow">{{
            $t("myAccount.lowOnCash")
          }}</span>
          <span
            v-show="isAscensionShow"
            class="content-right"
            @click="pageJump"
            >{{ $t("myAccount.promote") }}</span
          >
        </span>
      </div>
      <div class="row-tip">
        <span>
          <!-- <span>{{ $t("myAccount.purchaseRates") }}:</span>
          <span
            v-if="tradeRulesData.subscriptionRatePreferential"
            class="red"
            >{{
              Number(tradeRulesData.subscriptionRatePreferential * 100).toFixed(
                2
              ) + "% "
            }}</span
          >
          <span v-else class="red">{{
            Number(tradeRulesData.subscriptionRate * 100).toFixed(2) + "% "
          }}</span> -->
          <span>
            {{ $t("myAccount.expected") }}
            <!-- {{ Number(tradeRulesData.confirmationPlusDay) + Number(tradeRulesData.afterNine) }} -->
            <!-- {{ tradeRulesData.confirmationPlusDay }} -->
            {{ tradeRulesData.shareConfirmedDay }}
            {{ $t("myAccount.subscriptionDescription1") }}
          </span>
        </span>
      </div>
      <div class="row-agree">
        <img :src="require(`@/assets/images/international/multi_select_${isAgree ? 'selected' : 'default'}.png`)" alt="pic"  @click="agreeAgreement"/>
        <span class="title">
          <span>{{ $t("myAccount.protocolDescription") }}</span>
        </span>
        <span class="content-right" @click="goToAalesAgreement">{{
          $t("myAccount.agreement")
        }}</span>
      </div>
      <div class="margin">
        <md-button @click="showPopUp('center')" class="btn-buy" round type="primary">{{
          $t("myAccount." + enumeration.title)
        }}</md-button>
      </div>
      <div class="page-title">
        <span class="text-right" @click="goToRules">{{
          $t("myAccount." + enumeration.rules)
        }}</span>
      </div>
      <BottomTips />
      <div class="dialog-main" v-show="isDialogShow">
        <div class="dialog" id="password-transaction-dialog">
          <div class="title">
            <!-- <img :src='require(`../../assets/images/${theme}/icon-close.png`)' alt="pic" @click="closeDialog('center')" /> -->
            <span class="text">{{ $t("myAccount.popUpTitle") }}</span>
          </div>
          <div class="num">
            <span class="text1">{{ funddata.currencyType }}</span>
            <span class="text2">{{ dialogNum }}</span>
          </div>
          <div class="account">
            <div class="text1">{{ $t("myAccount.paymentAccount") }}</div>
            <div class="text2">{{
              $t("myAccount.paymentAccountContent")
            }}</div>
          </div>
          <div class="product-name">
            <div class="text1">{{ $t("myAccount.productName") }}</div>
            <div class="text2">{{ productNameContent }}</div>
          </div>
          <div class="row-tip">
            <span>
              <span>
                {{ $t("myAccount.expected") }}
                <!-- {{ Number(tradeRulesData.confirmationPlusDay) + Number(tradeRulesData.afterNine) }} -->
                <!-- {{ tradeRulesData.confirmationPlusDay }} -->
                {{ tradeRulesData.shareConfirmedDay }}
                {{ $t("myAccount.subscriptionDescriptionbuy1") }}
                {{ tradeRulesData.navConfirmedDay }}
                {{ $t("myAccount.redemptionConfirmationDescriptionRight") }}
              </span>
            </span>
          </div>
          <div class="password">
            <input
              id="password"
              v-model="passwordListNew"
              @input="setFontColor"
              class="input-box"
              :class="{'error-input': isPasswordError}"
              :type="openEye ? 'text' : 'password'"
              :placeholder="$t('myAccount.popUpTitle')"
            />
            <img :src="require(`@/assets/images/international/preview_${openEye ? 'open' : 'close'}.png`)" class="eye-passord" alt="pic" @click="openEye=!openEye"/>
          </div>
          <div class="password-prompt">
            <span v-show="isPasswordError" class="password-err">{{
              $t("myAccount.passwordMistake")
            }}</span>
            <span @click="submitResetPassword" class="password-forget">{{
              $t("myAccount.forgotPassword")
            }}</span>
          </div>
          <div class="bounced-operation">
            <md-button type="default" round @click="closeDialog" class="cancel">{{ $t("common.btns.cancel") }}</md-button>
            <md-button type="primary" v-if="!passwordLegal" round>{{ $t("myAccount.determine") }}</md-button>
            <md-button type="primary" v-else round @click="submitPassword">{{ $t("myAccount.determine") }}</md-button>
          </div>
        </div>
      </div>
    </div>

    <salesAgreement
      v-show="isShowSalesAgreement"
      :salesData="$route.query"
      @showAgreement="emitData"
    ></salesAgreement>
    <complexProductSalesAgreement
      v-show="isShowComplexProducSalesAgreement"
      :salesData="$route.query"
      @inputPasswordShow="inputPasswordShow"
    ></complexProductSalesAgreement>

    <!-- 风险测评 dialog -->
    <div class="dialog-main1" v-if="ExpiredShow">
      <div class="dialog">
        <p class="content-describe">{{  expired_ExpiringSoon }}</p>
        <div class="button-bottom-risk">
          <div class="first-class" @click="onBasicCancel">{{ ExpiredBtn === 'later' ? this.$t('common.Later') : $t('common.btns.cancel') }}</div>
          <div @click="onBasicConfirm">{{ $t('common.SubmitQuestionnaire') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
import edRow from "@/components/international/common-row.vue";
import BottomTips from "@/components/BottomTips.vue";
import { NumberKeyboard, Button, InputItem } from "mand-mobile";
import {
  getFundInformation,
  getAccountMoney,
  getAccountNumber,
  purchase,
  getAccountEmail,
  fundCashRiskAssessment
} from "@/services/account.js";
import { tradeRules } from "@/services/fund";
import { fmoney, getNowFormatDate } from "@/utils/util.js";
import { Toast } from "mand-mobile";
import loading from "@/components/loading/index";
import salesAgreement from "@/views/international/fund-transaction/sales-agreement";
import complexProductSalesAgreement from "@/views/international/fund-transaction/complex-product-sales-agreement";
export default {
  name: "orderRecord",
  computed: {
    ...mapState(["theme", "locale", "accountNumber", "klineTheme"]),
  },
  data() {
    return {
      isShowComplexProducSalesAgreement: false,
      agreeComplexProductSalesAgreement: false, // 是否同意复杂产品协议
      dialogNum: 0,
      isLoading: false,
      passwordLegal: false,
      userEmail: "",
      isDialogResetPassword: false,
      isAscensionShow: false,
      isPasswordError: false,
      netRecognitionDate: "",
      tradeRulesData: {},
      minimumAmount: 0,
      redemptionConfirmationDescription: "",
      funddata: {},
      accountBalance: 600,
      MaximumRedemptionValue: 0,
      enumeration: {
        title: "",
        rules: "",
        accountName: "",
        productName: "myAccount.productName",
        tradingTitle: "",
        yieldInRecentYear: "myAccount.yieldInRecentYearText",
      },
      placeholder: "",
      buyOrSellNum: "",
      currentQuantity: "",
      company: "",
      isAgree: false,
      isKeyPasswordBoardShow: true,
      dialogPassword: "",
      passwordList: [
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
      ],
      passwordListNew: "",
      isDialogShow: false,
      code: "",
      isPopupShow: {},
      isKeyBoardShow: false,
      accountContent: "myAccount.paymentAccountContent",
      productNameContent: "",
      yieldInRecentYearContent: "",
      yieldInRecentYearShow: "",
      inputValue: "",
      accountData: "",
      isShowSalesAgreement: false, // 协议组件
      newAccountNuber: "",
      orderId: "",
      openEye: false,
      ExpiredBtn: '',
      ExpiredShow: false,
      riskFundIsin: "",
      expired_ExpiringSoon: ''
    };
  },
  components: {
    loading,
    salesAgreement,
    complexProductSalesAgreement,
    edRow,
    BottomTips,
    [NumberKeyboard.name]: NumberKeyboard,
    [InputItem.name]: InputItem,
    [Button.name]: Button,
  },

  async created() {
    let res = {};

    this.initializeData();
    // 为避免用户直接从app页面进入h5页面导致交易账户获取不到当前最新的
    res = await getAccountNumber(this);
    if (res.data.tradingAccountList && res.data.tradingAccountList.length) {
      let object = res.data.tradingAccountList.find((item) => {
        return item.type === "FUND";
      });
      this.newAccountNuber = object.tradeAccountNumber;
    }
    this.getTradeRules();
    // 申购赎回 都查基金详情接口
  },
  methods: {
    submitResetPassword() {
      this.$jsBridge.run("resetPassword", {
        accountType: "funding",
        account: this.accountData.tradeAccountNumber,
      });
    },
    forgetPassword() {
      getAccountEmail(this, this.$jsBridge.isSupported("getAppInfo")).then(
        (res) => {
          this.accountData = res.data.tradingAccountList.find((item) => {
            return item.type === "FUND";
          });
          this.userEmail = res.data.user.email;
          this.isDialogResetPassword = true;
        }
      );
    },
    submitPassword() { // 输入支付密码
      this.isLoading = true;
      this.$jsBridge.run("startLogin", {
        type: "D",
        password: this.passwordListNew,
        callback: ({ login_state, account_type }) => {
          if (login_state) {
            this.submitOrder();

            // this.affirmPayment(this.orderId)
            this.isPasswordError = false;
          } else {
            this.isLoading = false;
            this.isPasswordError = true;
            this.passwordLegal = false;
          }
        },
      });
    },
    pageJump() {
      this.$jsBridge.run("toPage", {
        jumpType: window.$App ? "H5" : "NATIVE",
        loginState: "JUDGMENT",
        openAccountState: "JUDGMENT_FUND",
        navigationContentCode: "BUSINESS_DEPOSIT",
        navigationUri: "BUSINESS_DEPOSIT",
        titleDisplay: "DISPALY",
      });
    },
    setFontColor() {
      this.isPasswordError = false;
      if (
        this.passwordListNew.length >= 6 &&
        this.passwordListNew.length < 16
      ) {
        this.passwordLegal = true;
      } else {
        this.passwordLegal = false;
      }
    },
    goToAalesAgreement() {
      this.isShowSalesAgreement = true;
    },
    emitData(data) {
      // 监听协议组件事件触发
      if (data.isAgree === 1) {
        this.isAgree = true;
      } else {
        this.isAgree = false;
      }
      this.isShowSalesAgreement = false;
    },
    getTradeRules() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          // let date = new Date(
          //   Date.parse(new Date()) + this.tradeRulesData.confirmationPlusDay * 24 * 60 * 60 * 1000
          // );
          // let month, day;
          // if (date.getMonth() < 9) {
          //   month = "0" + (date.getMonth() + 1);
          // }else {
          //   month = (date.getMonth() + 1);
          // }
          // if (date.getDate() < 10) {
          //   day = "0" + date.getDate();
          // } else {
          //   day = date.getDate();
          // }
          // this.netRecognitionDate = "" + date.getFullYear() + "-" + month + "-" + day;
          this.getData();
        }
      });
    },
    initializeData() {
      // 初始化获取数据 获取当前是申购还是赎回
      // 标题 规则 账户 份额
      this.enumeration.title = "apply";
      this.enumeration.rules = "subscriptionRules";
      this.enumeration.accountName = "myAccount.paymentAccount";
      this.enumeration.tradingTitle = "subscriptionAmount";
      this.buyOrSellNum = "allBuy";
      this.currentQuantity = "balanceBuy";
    },
    async getData() {
      // 查询申购基金详情  名称 近一年收益  当前基金支持货币 最低申购金额 申购费率
      // 根据当前基金支持货币查询
      //  申购查询 当前货币账号余额
      // 赎回查询  查询当前可赎回份额
      try {
        await getFundInformation(this.$route.query.fundIsin).then((res) => {
          this.funddata = res.data;
          this.resloveData(res);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    resloveData(res) {
      let resFirst = res;
      let type = res.data.currencyType;
      getAccountMoney(this.newAccountNuber).then((res) => {
        let num = res.data.balances.find((item) => {
          return item.currency === type;
        });
        if (num) {
          this.MaximumRedemptionValue = fmoney(num.availableAmount, 2);
        } else {
          this.MaximumRedemptionValue = 0.0;
        }
        this.resloveDataLast(resFirst);
      });
    },
    resloveDataLast(res) {
      // 货币单位 请求对应货币单位的账户余额
      // 判断 申购还是赎回
      // 调用查询账户余额接口 或者是查份额接口
      // let type = res.data.currencyType;

      let monetaryUnit = "myAccount.yuan";
      if (res.data.currencyType === "CNY") {
        monetaryUnit = "myAccount.yuan";
        this.company = "yuan";
      } else if (res.data.currencyType === "HKD") {
        monetaryUnit = "myAccount.HKDollar";
        this.company = "HKDollar";
      } else if (res.data.currencyType === "USD") {
        monetaryUnit = "myAccount.dollar";
        this.company = "dollar";
      }
      if (this.MaximumRedemptionValue == 0) {
        this.isAscensionShow = true;
      } else if (
        this.tradeRulesData.firstInvestment &&
        Number(
          this.MaximumRedemptionValue
            ? this.MaximumRedemptionValue
              ? this.MaximumRedemptionValue.replace(/,/g, "")
              : 0
            : 0
        ) < Number(this.tradeRulesData.minFirstInvestment)
      ) {
        this.isAscensionShow = true;
      } else if (
        !this.tradeRulesData.firstInvestment &&
        Number(
          this.MaximumRedemptionValue
            ? this.MaximumRedemptionValue.replace(/,/g, "")
            : 0
        ) < Number(this.tradeRulesData.minNextInvestmentAmount)
      ) {
        this.isAscensionShow = true;
      } else {
        this.isAscensionShow = false;
      }

      if (!this.tradeRulesData.firstInvestment) {
        this.minimumAmount = fmoney(this.tradeRulesData.minNextInvestmentAmount, 2);
        // console.log(this.minimumAmount);
        this.placeholder =
          this.minimumAmount +
          this.$t(monetaryUnit) +
          this.$t("myAccount.additional");
      } else {
        this.minimumAmount = fmoney(this.tradeRulesData.minFirstInvestment, 2);
        this.placeholder =
          this.minimumAmount +
          this.$t(monetaryUnit) +
          this.$t("myAccount.buyPlaceholder");
      }
      // 判断当前国际化
      // this.productNameContent = (this.locale === 'zh-hans' ? res.data.name.cn : res.data.name.hk);
      if (this.locale == "zh-hans") {
        this.productNameContent =
          res.data.name && res.data.name.cn ? res.data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        this.productNameContent =
          res.data.name && res.data.name.hk ? res.data.name.hk : "-";
      } else {
        this.productNameContent =
          res.data.name && res.data.name.us ? res.data.name.us : "-";
      }
      // 收益率拼接百分号
      this.yieldInRecentYearShow = Number(res.data.cumulative1Y * 100);
      if (Number(res.data.cumulative1Y * 100) > 0) {
        this.yieldInRecentYearContent =
          "+" + Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      } else if (Number((res.data.cumulative1Y * 100).toFixed(2)) == 0) {
        this.yieldInRecentYearContent = "0.00%";
      } else {
        this.yieldInRecentYearContent =
          Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      }
    },
    goToRules() {
      this.$router.push({
        path: "/international/dealRule",
        query: {
          id: this.$route.query.fundIsin,
        },
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    agreeAgreement() {
      this.isAgree = !this.isAgree;
    },
    buyOrSell() {
      if (this.MaximumRedemptionValue == 0) {
        this.inputValue = 0;
      } else {
        this.inputValue = this.MaximumRedemptionValue || 0;
      }
    },
    closeDialog() { // 取消--关闭输入密码弹窗
      this.dialogNum = 0;
      this.isDialogShow = false;
      this.isKeyPasswordBoardShow = false;
      this.isPasswordError = false;
      this.passwordLegal = false;
      this.passwordListNew = "";
    },
    submitOrder() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          if (res.data.buyFlag) { // 是否可买入
            this.submitOrderLast();
          } else {
            this.$router.go(-1);
            Toast.info(this.$t("myAccount.subscriptionIsNotSupported"));
          }
        }
      });
    },
    async submitOrderLast() {
      let parameter = {
        fundIsin: this.$route.query.fundIsin,
        orderType: "Buy",
        tradeAccount: this.newAccountNuber,
      };
      parameter.amount = this.inputValue.replace(/,/g, "");
      try {
        await purchase(parameter).then((res) => {
          this.isLoading = false;
          this.closeDialog();
          if (!res) return;
          if(!res || res && res.code != 200){
            Toast.info(res.msg);
            return
          }
          this.orderId = res.data.orderId;
          this.affirmPayment(this.orderId)
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    affirmPayment(ID) {
      console.log(ID);
      if (window.$App) {
        this.$jsBridge.run("navBack", {
          isClosePage: true,
          callback: () => {},
        });
      }
      this.$jsBridge.run("startEdWeb", {
        url:
          window.location.origin +
          `/international/subscribeRedeem?orderId=${ID}&nav=0`,
        callback: () => {},
      });
    },
    onBasicConfirm() { // 跳转风险评测
      this.ExpiredShow = false
      // this.$router.push({
      //   path: "/risk/tips"
      // })
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/international/risk/tips?nav=0&isExpired=yes`,
        callback: () => {},
      });
    },
    onBasicCancel() { // 跳转基金申购页面
      console.log('取消按钮');
      this.ExpiredShow = false
      if (this.ExpiredBtn === 'later') {
        this.accountAttribute()
      }
    },
    showPopUp() {
      // console.log("申购", this.$route.query.isProfessionalInvestor);
      /*
        风险测评日期有效状态 --> 风险测评结果已过期，则不允许用户申购
        （1）如有登录校验、基金开户校验，则放在登录校验、基金开户校验后面。
        （2）如没有登录校验、基金开户校验，则放在第一步校验
      */
      fundCashRiskAssessment(this.newAccountNuber).then(riskStatus => {
        if (riskStatus.data.assessmentStatus === 'Expired') { // 已过期
          this.ExpiredShow = true
          this.ExpiredBtn = 'cancel'
          this.expired_ExpiringSoon = this.$t('common.Expired')
          return
        } else if (riskStatus.data.assessmentStatus === 'ExpiringSoon') { // 过期倒计时
          this.ExpiredShow = true
          this.ExpiredBtn = 'later'
          this.expired_ExpiringSoon = this.$t('common.ExpiringSoon', { time: moment(riskStatus.data.expiryDate).format("YYYY-MM-DD") })
          return
        } else {
          this.accountAttribute()
        }
      })
    },
    accountAttribute() {
      if (!this.isAgree) return Toast.info(this.$t("myAccount.pleaseChoose"));
      if (!this.inputValue) {
        return Toast.info(this.$t("myAccount.explainThePrompt"));
      }
      // 校验输入框值是否大于账户数
      if (this.MaximumRedemptionValue == 0) {
        return Toast.info(this.$t("myAccount.abnormalAmounts"));
      }
      let inputValue = this.inputValue.replace(/,/g, "");
      if (
        Number(inputValue) >
        Number(
          this.MaximumRedemptionValue
            ? this.MaximumRedemptionValue.replace(/,/g, "")
            : 0
        )
      )
      
      return Toast.info(this.$t("myAccount.abnormalAmounts"));
      // 校验输入框值是否大于最小申购值
      if (Number(inputValue) < Number(this.minimumAmount)) {
        return Toast.info(this.placeholder);
      }
      if (this.funddata.complexProductFlag && !this.agreeComplexProductSalesAgreement) { // 是否同意复杂产品协议
        this.isShowComplexProducSalesAgreement = true;
        return;
      } else {
        this.isDialogShow = true;
        this.dialogNum = this.inputValue;
      }
    },
    inputPasswordShow(data) {
      this.isShowComplexProducSalesAgreement = false;
      if (data.isAgree === 1) {
        this.agreeComplexProductSalesAgreement = true;
        this.dialogNum = this.inputValue;
        this.isDialogShow = true;
      } else {
        this.agreeComplexProductSalesAgreement = false;
      }
    },
    hidePopUp(type) {
      this.$set(this.isPopupShow, type, false);
    },
    onNumberEnter(val) {
      this.inputValue += val;
      this.$nextTick(() => {
        let num = 2;
        let numInteger, numDecimal;
        if (this.inputValue.split(".").length == 1) {
          numInteger = this.inputValue.split(".")[0];
          let tempNumInteger = numInteger.replaceAll(',','');
          let reg = /(\d)(?=(\d{3})+$)/g
          let actualNumInteger = tempNumInteger.replace(reg, '$1,')
          this.inputValue =
            numInteger.length > 13 ? numInteger.slice(0, 13) : actualNumInteger;
        } else {
          numInteger = this.inputValue.split(".")[0];
          numDecimal = this.inputValue.split(".")[1];
          this.inputValue =
            numInteger.length > 13
              ? numDecimal.length > num
                ? numInteger.slice(0, 13) + "." + numDecimal.slice(0, num)
                : numInteger.slice(0, 13) + "." + numDecimal
              : numDecimal.length > num
              ? numInteger + "." + numDecimal.slice(0, num)
              : numInteger + "." + numDecimal;
        }
      });
    },
    onNumberDelete() {
      if (this.inputValue === "") {
        return;
      }
      
      if (this.inputValue.split(".").length == 1) {
        let tempNumInteger = this.inputValue.replace(/,/g, "");
        tempNumInteger = tempNumInteger.substr(0, tempNumInteger.length - 1);
        this.$nextTick(() => {
          let reg = /(\d)(?=(\d{3})+$)/g;
          this.inputValue = tempNumInteger.replace(reg, '$1,');
          // 更新不了，实在没办法了，哈哈
          this.$refs.input11.inputValue = this.inputValue;
        })

      } else {
        this.inputValue = this.inputValue
        .substr(0, this.inputValue.length - 1);
      }
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.input-mask {
  height: 0;

  & ::v-deep.md-input-item .md-input-item-fake {
    font-size: 0;
  }
}
.index-page {
  width: 100%;
  height: 100vh;
  padding-top: 16px;
  background-color: var(--background);
  color: var(--text_1st);
  ::v-deep .md-input-item-fake-placeholder {
    left: 0px;
    font-size: 32px;
    border-right-color: var(--text_3rd);
  }
  ::v-deep .md-input-item-fake {
    color: var(--text_1st);
    padding: 0px;
    font-size: 48px;
    line-height: 80px;
    font-family: D-DINExp, D;
  }
  ::v-deep .md-input-item-fake::after {
    border-right-color: var(--brand_01);
  }
  ::v-deep .md-number-keyboard .delete {
    background-color: var(--background);
    color: var(--text_1st);
  }
  ::v-deep .md-field-item-content::before {
    border: none !important;
  }
  ::v-deep .md-field-item-content {
    width: 300px;
  }
  ::v-deep .md-field-item-content:before {
    border: none;
  }
  ::v-deep .md-number-keyboard .slidedown {
    background-color: var(--background);
    color: var(--text_1st);
  }
  ::v-deep .md-number-keyboard .confirm {
    background-color: var(--text_3rd);
  }
  ::v-deep .md-number-keyboard .keyboard-number-item {
    background-color: var(--background);
    color: var(--text_1st);
  }
  ::v-deep .md-number-keyboard .md-popup-box {
    // padding-top: 1px;
    padding-bottom: 40px;
    background-color: var(--background);
  }
  ::v-deep .md-number-keyboard .md-popup-box:after {
    border-top: 0px solid var(--line_01);
  }
  ::v-deep .md-number-keyboard-container:after {
    border-top-color: var(--line_01);
  }
  ::v-deep
    .md-number-keyboard-container
    .keyboard-number
    .keyboard-number-list
    .keyboard-number-item:before {
    border-right-color: var(--line_01);
  }
  ::v-deep
    .md-number-keyboard-container
    .keyboard-number
    .keyboard-number-list
    .keyboard-number-item:after {
      border-top-color: var(--line_01);
  }
  ::v-deep .md-field-item.is-solid .md-field-item-title {
    width: 80px;
  }

  .dialog-main {
    background-color: var(--mask);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;

    .dialog {
      position: absolute;
      top: 8%;
      left: 30px;
      background: var(--background);
      color: var(--text_1st);
      border-radius: 30px;
      width: calc(100% - 60px);
      font-size: 32px;

      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        margin: 48px 0 24px 0;
        img {
          z-index: 5000;
          width: 32px;
          height: 32px;
          position: absolute;
          top: 50%;
          right: 30px;
          margin-top: -16px;
        }
        .text {
          color: var(--text_1st);
          line-height: 48px;
          font-weight: 600;
        }
      }
      .num {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 8px;
        .text1 {
          font-size: 28px;
          line-height: 44px;
          padding-right: 16px;
        }
        .text2 {
          font-size: 48px;
          line-height: 72px;
        }
      }
      .account {
        margin: 0 40px;
        margin-top: 24px;
        font-size: 28px;
        line-height: 40px;
        .text1 {
          color: var(--text_3rd);
        }

        .text2 {
          margin-top: 8px;
        }
      }
      .product-name {
        margin: 24px 40px 0;
        font-size: 28px;
        line-height: 40px;
        .text1 {
          color: var(--text_3rd);
        }
        .text2 {
          display: inline-block;
          white-space: pre-wrap;
          width: 410px;
          margin-top: 8px;
        }
      }
      .row-tip {
        background-color: var(--brand_06);
        color: var(--text_3rd);
        margin: 24px 40px 32px;
        font-size: 22px;
        line-height: 32px;
        padding: 16px 20px 12px;
        .red {
          color: var(--red);
        }
      }
      .tips {
        color: var(--text_3rd);
        background: var(--brand_06);
        width: calc(100% - 100px);
        margin: 13px 50px;
        padding-left: 16px;
        text-align: left;
        font-size: 24px;
        line-height: 56px;
      }
      .password {
        margin: 32px 40px 16px 40px;
        padding: 0 24px;
        border-radius: 10px;
        background: var(--gray_05);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .eye-passord {
          width: 48px;
        }
        .input-box {
          appearance: button;
          background: var(--gray_05);
          color: var(--text_1st);
          border: none;
          outline: none;
          font-size: 28px;
          width: 100%;
          height: 100px;
        }
      }
      .error-input {
        border: 1px solid var(--error);
      }
      .password-prompt {
        margin-top: 0.16rem;
        margin: 16px 40px 32px;
        line-height: 36px;
        font-size: 24px;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .password-err {
          position: absolute;
          top: 0px;
          left: 0px;
          color: var(--error);
        }
        .password-forget {
          color: var(--brand_02);
        }
      }
      .bounced-operation {
        border-top: 1px solid var(--line_01);
        margin-top: 40px;
        padding: 24px 40px 46px;
        display: flex;
        justify-content: space-around;
        div {
          width: 50%;
          line-height: 90px;
          text-align: center;
          font-size: 28px;
        }
        .cancel {
          color: var(--text_1st);
          border: 1px solid var(--line_02);
          margin-right: 42px;
        }
      }
    }
  }
  .dialog-main1 {
    background-color: var(--mask);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    .dialog {
      width: 70%;
      background: var(--background);
      padding: 48px 40px;
      max-width: 550px;
      border-radius: 30px;
      .content-describe {
        text-align: center;
        line-height: 40px;
        font-size: 28px;
        color: var(--text_1st);
      }
      .button-bottom-risk {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        div {
          border-radius: 40px;
          width: 220px;
          height: 80px;
          line-height: 80px;
          background-color: var(--brand_01);
          color: var(--text_5th);
          border: 1px solid var(--brand_01);
          text-align: center;
        }
        .first-class {
          background-color: var(--background);
          color: var(--text_1st);
          border: 1px solid var(--line_01);
        }
      }
      .button-bottom-risk-no-flex {
        display: block;
        div {
          width: 100%;
        }
        .first-class {
          border: 0;
          text-decoration: underline;
          text-decoration-color: var(--text_1st);
          margin-top: 20px;
        }
      }
    }
  }
  .page-title {
    color: var(--brand_02);
    padding-top: 8px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    position: relative;
    font-size: 26px;
    font-weight: Medium;
    .return {
      width: 16px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -15px;
      left: 30px;
    }
  }
  .stock-yield {
    font-size: 24px;;
    text-align: right;
    line-height: 36px;
    margin-right: 32px;
    padding-bottom: 16px;
    color: var(--text_4rd);
    span {
      margin-left: 16px;
    }
  }

  .bg-row {
    background: var(--gray_05);
    height: 32px;
  }
  .row-money-page {
    margin: 32px;
    font-size: 34px;
    line-height: 52px;
    font-weight: 500;
    font-family: PingFangSC-Medium, PingFang SC;
    color: var(--text_1st);
  }
  .row-num-page {
    position: relative;
    margin: 0px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32px;
    .monetary-unit {
      font-family: D-DINExp, D;
      width: 146px;
      font-size: 32px;
      line-height: 48px;
    }

    .wrap {
      flex-grow: 1;
      display: flex;
      align-items: center;
      position: relative;
      &:after {
        content: " ";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-image: linear-gradient(to bottom, var(--background) 50%, var(--line_01) 50%);
      }

      .left {
        display: inline-block;
        position: relative;
        width: 100%;
        height: 80px;
        .amout {
          width: 100%;
        }
        ::v-deep .md-field-item-content {
          width: 100%;
        }
        .placeholder {
          background: var(--background);
          color: var(--text_3rd);
          border: none;
          outline: none;
          font-size: 32px;
          width: 450px;
          overflow: hidden;
          &:focus {
            // outline: none;
            // border: none;
            // width: 80px;
            color: var(--text_1st);
            // font-size: 80px;
            // line-height: 80px;
          }
        }
        .masker {
          position: absolute;
          left: 35px;
          top: 0px;
          width: 350px;
          height: 75px;
          background-color: chartreuse;
          z-index: 6000;
          opacity: 0;
        }
      }
      .content-right {
        flex-shrink: 0;
        font-size: 28px;
        line-height: 28px;
        color: var(--brand_02);
        width: 120px;
      }
    }
  }
  .row-num2-page {
    width: calc(100% - 60px);
    margin: 20px 32px 0;
    font-size: 28px;
    line-height: 44px;
    color: var(--text_3rd);
    .lang-en {
      display: block;
    }
    .red {
      color: var(--red);
    }
    .title {
      padding-right: 32px;
      span:nth-of-type(2) {
        padding-left: 4px;
      }
    }
    .content-right {
      background: var(--data_04);
      color: var(--background);
      border-radius: 4px;
      padding: 0 4px;
      font-size: 22px;
      line-height: 32px;
      margin-left: 20px;
    }
  }
  .row-tip {
    margin-top: 20px;
    border-radius: 10px;
    margin: 20px 32px 0;
    font-size: 22px;
    line-height: 32px;
    padding: 16px 20px;
    background: var(--brand_06);
    color: var(--text_3rd);
    .red {
      color: var(--red);
    }
  }
  .margin{
    margin: 16px 32px;
    .btn-buy {
      font-size: 30px;
      height: 90px;
      line-height: 90px;
      color: var(--text_5th);
    }
  }
  .row-agree {
    margin: 80px 34px 8px;
    font-size: 28px;
    background: var(--background);
    padding: 16px 0;
    word-break: break-all;
    img {
      width: 36px;
      height: 36px;
      margin-right: 20px;
      vertical-align: middle;
    }
    .title {
      color: var(--text_3rd);
    }
    .content-right {
      color: var(--brand_01);
      margin-left: 4px;
    }
  }
}
</style>
