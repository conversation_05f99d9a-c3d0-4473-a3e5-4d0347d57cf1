<template>
  <div class="index-page">
    <template v-if="!isShowSalesAgreement">
      <loading v-if="isLoading"></loading>
      <ed-row
        :contentLeft="enumeration.accountName"
        :contentRight="accountContent"
         :borderStyle="true"
      >
        <span>{{ $t(accountContent) }}</span>
      </ed-row>
      <ed-row :contentLeft="enumeration.productName">
        <span>{{ !productNameContent ? "--" : productNameContent }}</span>
      </ed-row>
      <div class="yield-in-recentYear">
        <span>{{ $t('myAccount.yieldInRecentYearText') }}</span>
        <span
        :class="
            klineTheme === 'redRiseGreenFall'
              ? (Number(yieldInRecentYearShow) < 0
                ? 'output'
                : Number(yieldInRecentYearShow) == 0
                ? ''
                : 'entry')
              : Number(yieldInRecentYearShow) > 0
              ? 'output'
              : Number(yieldInRecentYearShow) == 0
              ? ''
              : 'entry'
          "
        >{{ yieldInRecentYearContent }}</span>
      </div>
      <div class="row-money-page">
        <span>{{ $t("myAccount." + enumeration.tradingTitle) }}</span>
      </div>
      <!-- 申购赎回 输入金额 -->
      <div class="row-num-page">
        <div class="content-border">
          <span class="left">
            <md-input-item
              ref="input11"
              class="amout"
              :placeholder="
                isPlaceholderShow && !inputValue.length ? placeholder : ''
              "
              is-virtual-keyboard
              v-model="inputValue"
              virtual-keyboard-vm="myNumberKeyBoard"
            ></md-input-item>
            <span v-show="isKeyBoardShow" class="masker"></span>
          </span>
          <span class="content-right" @click="buyOrSell()">{{ $t("myAccount." + buyOrSellNum) }}</span>
        </div>
      </div>
      <div
        class="
          md-example-child
          md-example-child-number-keyboard
          md-example-child-number-keyboard-1
        "
      >
        <md-number-keyboard
          ref="myNumberKeyBoard"
          v-model="isKeyBoardShow"
          @enter="onNumberEnter"
          @delete="onNumberDelete"
        >
        <!-- <p class="number-keyboard-header">
          <img src="@/assets/images/international/eddid_logo_1.png" alt="">
          <span class="pay-title">守护您的交易安全</span>
        </p> -->
        </md-number-keyboard>
      </div>

      <div class="row-num2-page">
        <span class="title">
          <span>{{ $t("myAccount." + currentQuantity) }}</span>
          <span>{{ MaximumRedemptionValue }}</span>
          <span>{{ company === "" ? "" : $t("myAccount." + company) }}</span>
        </span>
      </div>
      <div class="row-tip">
        <div>
          <span>
            {{ $t("myAccount.redemptionDescriptionlabel1")}}
            <!-- {{ tradeRulesData.confirmationPlusDay}} -->
            {{ tradeRulesData.navConfirmedDay }}
            {{ $t("myAccount.redemptionDescriptionlabelAfter1") }}
            <!-- {{ tradeRulesData.redemptionPlusDay}} -->
            {{ tradeRulesData.redemptionArriveDay}}
            {{ $t("myAccount.redemptionDescriptionlabelEnd1") }}
          </span>
        </div>
        <div>
          <span>{{ $t("myAccount.redemptionRate") }}</span>
          <span v-if="tradeRulesData.redemptionRatePreferential" class="red">{{
            (tradeRulesData.redemptionRatePreferential * 100).toFixed(2) + "%"
          }}</span>
          <span v-else class="red">{{
            (tradeRulesData.redemptionRate * 100).toFixed(2) + "%"
          }}</span>
        </div>
      </div>
      <div class="row-agree">
        <!-- isAgree -->
        <img :src="require(`@/assets/images/international/multi_select_${isAgree ? 'selected' : 'default'}.png`)" alt="pic"  @click="agreeAgreement"/>
        <span class="title">
          <span>{{ $t("myAccount.protocolDescription") }}</span>
        </span>
        <span class="content-right" @click="goToAalesAgreement">{{
          $t("myAccount.agreement")
        }}</span>
      </div>
      <div class="padding-20">
        <md-button type="bulletin" round @click="showPopUp('center')" class="sell-button">{{ $t("myAccount." + enumeration.title) }}</md-button>
      </div>
      <div class="page-title">
        <span class="text-right" @click="goToRules">{{
          $t("myAccount." + enumeration.rules)
        }}</span>
      </div>
      <div class="dialog-main" v-show="isDialogShow">
        <div class="dialog">
          <div class="title">
            <span class="text">{{ $t("myAccount.popUpTitle") }}</span>
          </div>
          <div class="num">
            <span class="text1">{{ $t("myAccount.share") }}</span>
            <span class="text2">{{ submitShare }}</span>
          </div>
          <div class="account">
            <div class="text1">{{ $t("myAccount.redeemTo") }}</div>
            <div class="text2">{{ $t("myAccount.paymentAccountContent") }}</div>
          </div>
          <div class="product-name">
            <div class="text1">{{ $t("myAccount.productName") }}</div>
            <div class="text2">{{ productNameContent }}</div>
          </div>
          <div class="tips">
            <span>
              {{ $t("myAccount.redemptionBouncedExpectedT1")}}
              <!-- {{ tradeRulesData.redemptionPlusDay}} -->
              {{ tradeRulesData.redemptionArriveDay }}
              {{ $t("myAccount.redemptionBouncedExpectedTAfter1") }}
            </span>
          </div>
          <div class="password">
            <input
              id="password"
              v-model="passwordListNew"
              @input="setFontColor"
              class="input-box"
              :class="{'error-input': isPasswordError}"
              :type="openEye ? 'text' : 'password'"
              :placeholder="$t('myAccount.popUpTitle')"
            />
            <img :src="require(`@/assets/images/international/preview_${openEye ? 'open' : 'close'}.png`)" class="eye-passord" alt="pic" @click="openEye=!openEye"/>
          </div>
          <div class="password-prompt">
            <span v-show="isPasswordError" class="password-err">{{
              $t("myAccount.passwordMistake")
            }}</span>
            <span @click="submitResetPassword" class="password-forget">{{
              $t("myAccount.forgotPassword")
            }}</span>
          </div>
          <div class="bounced-operation">
            <md-button type="default" round @click="closeDialog('center')" class="cancel">{{ $t("common.btns.cancel") }}</md-button>
            <md-button type="primary" v-if="!passwordLegal" round>{{ $t("myAccount.determine") }}</md-button>
            <md-button type="primary" v-else round @click="submitPassword">{{ $t("myAccount.determine") }}</md-button>
          </div>
        </div>
      </div>
    </template>
    <salesAgreement
      v-show="isShowSalesAgreement"
      :salesData="$route.query"
      @showAgreement="emitData"
    ></salesAgreement>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment"
import edRow from "@/components/international/common-row.vue";
import {
  NumberKeyboard,
  Button,
  InputItem,
  Codebox,
  Popup,
  PopupTitleBar,
  Icon,
} from "mand-mobile";
import {
  getFundInformation,
  getAccountMoney,
  purchase,
  getAccountNumber,
  getAccountEmail,
} from "@/services/account.js";
import { tradeRules } from "@/services/fund";
import { fmoney, getNowFormatDate } from "@/utils/util.js";
import { Toast } from "mand-mobile";
import loading from "@/components/loading/index";
import salesAgreement from "@/views/international/fund-transaction/sales-agreement";
export default {
  name: "orderRecord",
  computed: {
    ...mapState(["theme", "locale", "accountNumber", "klineTheme"]),
  },
  data() {
    return {
      submitShare: 0,
      isLoading: false,
      passwordLegal: false,
      userEmail: "",
      isDialogResetPassword: false,
      isPasswordError: false,
      netRecognitionDate: "",
      tradeRulesData: {},
      minimumAmount: 0,
      isPlaceholderShow: true,
      redemptionConfirmationDescription: "",
      funddata: {},
      accountBalance: 600,
      MaximumRedemptionValue: 0,
      enumeration: {
        title: "",
        rules: "",
        accountName: "",
        productName: "myAccount.productName",
        tradingTitle: "",
        yieldInRecentYear: "myAccount.yieldInRecentYearText",
      },
      placeholder: "",
      buyOrSellNum: "",
      currentQuantity: "",
      company: "",
      isAgree: false,
      isKeyPasswordBoardShow: true,
      dialogPassword: "",
      passwordList: [
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
        { isShow: false },
      ],
      passwordListNew: "",
      isDialogShow: false,
      code: "",
      isPopupShow: {},
      isKeyBoardShow: false,
      accountContent: "myAccount.paymentAccountContent",
      productNameContent: "",
      yieldInRecentYearContent: "",
      yieldInRecentYearShow: 0,
      inputValue: "",
      accountData: "",
      isShowSalesAgreement: false, // 协议组件
      newAccountNuber: "",
      openEye: false
    };
  },
  components: {
    loading,
    salesAgreement,
    edRow,
    [NumberKeyboard.name]: NumberKeyboard,
    [InputItem.name]: InputItem,
    [Popup.name]: Popup,
    [PopupTitleBar.name]: PopupTitleBar,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Codebox.name]: Codebox,
  },

  async created() {
    let res = {};

    this.initializeData();
    if (!this.accountNumber) {
      res = await getAccountNumber(
        this,
        this.$jsBridge.isSupported("getAppInfo")
      );
      if (res.data.tradingAccountList && res.data.tradingAccountList.length) {
        let object = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.newAccountNuber = object.tradeAccountNumber;
      }
    } else {
      this.newAccountNuber = this.accountNumber;
    }
    this.getTradeRules();

    // 申购赎回 都查基金详情接口
  },
  methods: {
    submitResetPassword() {
      this.$jsBridge.run("resetPassword", {
        accountType: "funding",
        account: this.accountData.tradeAccountNumber,
        // callback: () => {
        //   Toast.info(this.$t('myAccount.'))
        // }
      });
    },
    forgetPassword() {
      getAccountEmail(this).then((res) => {
        this.accountData = res.data.tradingAccountList.find((item) => {
          return item.type === "FUND";
        });
        this.userEmail = res.data.user.email;
        this.isDialogResetPassword = true;
      });
    },
    submitPassword() {
      this.isLoading = true;
      this.$jsBridge.run("startLogin", {
        type: "D",
        password: this.passwordListNew,
        callback: ({ login_state, account_type }) => {
          if (login_state) {
            this.submitOrder();
            this.isPasswordError = false;
          } else {
            this.isLoading = false;
            this.isPasswordError = true;
          }
        },
      });
    },
    pageJump() {
      this.$jsBridge.run("toPage", {
        jumpType: window.$App ? "H5" : "NATIVE",
        loginState: "JUDGMENT",
        openAccountState: "JUDGMENT_FUND",
        navigationContentCode: "BUSINESS_DEPOSIT",
        navigationUri: "BUSINESS_DEPOSIT",
        titleDisplay: "DISPALY",
      });
    },
    setFontColor() {
      this.isPasswordError = false;
      if (
        this.passwordListNew.length >= 6 &&
        this.passwordListNew.length < 16
      ) {
        this.passwordLegal = true;
      } else {
        this.passwordLegal = false;
      }
    },
    goToAalesAgreement() {
      this.isShowSalesAgreement = true;
    },
    emitData(data) {
      if (data.isAgree === 1) {
        this.isAgree = true;
      } else {
        this.isAgree = false;
      }
      this.isShowSalesAgreement = false;
    },
    getTradeRules() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          let date = new Date(
            Date.parse(new Date()) +
              this.tradeRulesData.confirmationPlusDay * 24 * 60 * 60 * 1000
          );
          let month, day;
          if (date.getMonth() < 9) {
            month = "0" + (date.getMonth() + 1);
          }else {
            month = (date.getMonth() + 1);
          }
          if (date.getDate() < 10) {
            day = "0" + date.getDate();
          } else {
            day = date.getDate();
          }
          this.netRecognitionDate =
            "" + date.getFullYear() + "-" + month + "-" + day;
          this.getData();
        }
      });
    },
    onFakeInputBlur() {},
    initializeData() {
      // 初始化获取数据 获取当前是申购还是赎回
      this.MaximumRedemptionValue = this.$route.query.quantity;
      this.enumeration.title = "redeem";
      this.enumeration.rules = "redemptionRules";
      this.enumeration.tradingTitle = "redemptionShare";
      this.enumeration.accountName = "myAccount.redeemTo";
      this.buyOrSellNum = "allSell";
      this.currentQuantity = "redemptionQuantity";
      this.redemptionConfirmationDescription =
        this.$t("myAccount.expectedT") +
        getNowFormatDate() +
        this.$t("myAccount.redemptionConfirmationDescriptionRight");
    },
    async getData() {
      // 查询申购基金详情  名称 近一年收益  当前基金支持货币 最低申购金额 申购费率
      // 根据当前基金支持货币查询
      //  申购查询 当前货币账号余额
      // 赎回查询  查询当前可赎回份额
      try {
        await getFundInformation(this.$route.query.fundIsin).then((res) => {
          this.funddata = res.data;
          this.resloveData(res);
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    resloveData(res) {
      let resFirst = res;
      let type = res.data.currencyType;
      this.MaximumRedemptionValue = fmoney(this.MaximumRedemptionValue, 0);
      this.resloveDataLast(resFirst);
    },
    resloveDataLast(res) {
      // 货币单位 请求对应货币单位的账户余额
      // 判断 申购还是赎回
      // 调用查询账户余额接口 或者是查份额接口
      // let type = res.data.currencyType;

      let monetaryUnit = "myAccount.yuan";
      if (res.data.currencyType === "CNY") {
        monetaryUnit = "myAccount.yuan";
        this.company = "yuan";
      } else if (res.data.currencyType === "HKD") {
        monetaryUnit = "myAccount.HKDollar";
        this.company = "HKDollar";
      } else if (res.data.currencyType === "USD") {
        monetaryUnit = "myAccount.dollar";
        this.company = "dollar";
      }
      // this.placeholder = this.$t("myAccount.sellPlaceholder") + Number(this.tradeRulesData.minRansom).toFixed(4);
      this.placeholder = this.$t("myAccount.sellPlaceholder") + Number(this.tradeRulesData.minRansom)
      this.company = "share";
      // 判断当前国际化
      // this.productNameContent = (this.locale === 'zh-hans' ? res.data.name.cn : res.data.name.hk);
      if (this.locale == "zh-hans") {
        this.productNameContent =
          res.data.name && res.data.name.cn ? res.data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        this.productNameContent =
          res.data.name && res.data.name.hk ? res.data.name.hk : "-";
      } else {
        this.productNameContent =
          res.data.name && res.data.name.us ? res.data.name.us : "-";
      }
      // 收益率拼接百分号
      this.yieldInRecentYearShow = Number(res.data.cumulative1Y * 100)
      if (Number(res.data.cumulative1Y * 100) > 0) {
        this.yieldInRecentYearContent =
          "+" + Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      } else if (Number((res.data.cumulative1Y * 100).toFixed(2)) == 0) {
        this.yieldInRecentYearContent = "0.00%";
      } else {
        this.yieldInRecentYearContent =
          Number(res.data.cumulative1Y * 100).toFixed(2) + "%";
      }
    },
    goToRules() {
      this.$router.push({
        path: "/international/dealRule",
        query: {
          id: this.$route.query.fundIsin,
          type: 2,
        },
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    agreeAgreement() {
      this.isAgree = !this.isAgree;
    },
    buyOrSell() {
      this.inputValue = this.MaximumRedemptionValue.replace(/,/g, "");
    },
    closeDialog(e) {
      this.submitShare = 0;
      this.isDialogShow = false;
      this.isKeyPasswordBoardShow = false;
      this.isPasswordError = false;
      this.passwordLegal = false;
    },
    
    submitOrder() {
      tradeRules(this.$route.query.fundIsin).then((res) => {
        // 交易规则
        if (res.code == 200) {
          if (res.data.sellFlag) {
            this.submitOrderLast();
          } else {
            this.$router.go(-1);
            Toast.info(this.$t("myAccount.redemptionIsNotSupported"));
          }
        }
      });
    },
    async submitOrderLast() {
      // 暂时跳过密码校验 直接调用申购接口
      let parameter = {
        fundIsin: this.$route.query.fundIsin,
        orderType: "Sell",
        tradeAccount: this.newAccountNuber,
      };
      parameter.quantity = this.inputValue.replace(/|,/g, "");
      try {
        await purchase(parameter).then((res) => {
          
          this.isLoading = false;
          if(!res || res && res.code != 200){
            Toast.info(res.msg);
            return
          }
          this.submitShare = 0;
          this.isDialogShow = false;
          this.isKeyPasswordBoardShow = false;
          this.isPasswordError = false;
          this.passwordLegal = false;
          if (window.$App) {
            this.$jsBridge.run("navBack", {
              isClosePage: true,
              callback: () => {},
            });
          }
          this.$jsBridge.run("startEdWeb", {
            url:
              window.location.origin +
              `/international/subscribeRedeem?orderId=${res.data.orderId}&nav=0`,
            callback: () => {},
          });
        });
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    showPopUp() {
      if (!this.isAgree) return Toast.info(this.$t("myAccount.pleaseChoose"), 3000, true);
      if (!this.inputValue) return Toast.info(this.$t("myAccount.redemptionPrompt"), 3000, true);

      // tradeRulesData  minHold  this.$route.query.quantity
      // 可与份额大于最小持有额  支持正常赎回 支持全部赎回  或者赎回后数量大于等于最小持有数量
      if (
        (Number(this.$route.query.quantity) <
          Number(this.tradeRulesData.minHold) ||
          Number(this.$route.query.quantity) <
            Number(this.tradeRulesData.minRansom)) &&
        Number(this.$route.query.quantity) != Number(this.inputValue)
      ) {
        return Toast.info(this.$t("myAccount.allTheRedemption"), 3000, true);
      } else if (
        (Number(this.tradeRulesData.minHold) <
          Number(this.$route.query.quantity) ||
          Number(this.tradeRulesData.minRansom) <
            Number(this.$route.query.quantity)) &&
        Number(this.inputValue) != Number(this.$route.query.quantity)
      ) {
        let num = this.$route.query.quantity - this.tradeRulesData.minHold;
        if (Number(this.inputValue) < Number(this.tradeRulesData.minRansom)) {
          return Toast.info(this.$t("myAccount.minimumRedeem"), 3000, true);
        }
        if (Number(this.inputValue) > Number(this.$route.query.quantity)) {
          return Toast.info(this.$t("myAccount.shareTooBig"), 3000, true);
        }
        if (Number(this.inputValue) > num) {
          console.log(this.inputValue, num, "ssss");
          return Toast.info(this.$t("myAccount.afterTheRedemptionShareIsSmall"), 3000, true);
        }
        // 可与份额小于最小持有额  只支持全部赎回
      } else if (
        Number(this.inputValue) != Number(this.$route.query.quantity)
      ) {
        console.log(
          Number(this.inputValue),
          Number(this.$route.query.quantity)
        );
        return Toast.info(this.$t("myAccount.afterTheRedemptionShareIsSmall"), 3000, true);
      }
      this.isDialogShow = true;
      // this.submitShare = Number(this.inputValue).toFixed(4);
      this.submitShare = this.inputValue;
      this.passwordListNew = "";
    },
    hidePopUp(type) {
      this.$set(this.isPopupShow, type, false);
    },
    onNumberEnter(val) {
      this.inputValue += val;
      this.$nextTick(() => {
        let num = this.tradeRulesData.unitPrecision;
        let numInteger, numDecimal;
        if (this.inputValue.split(".").length == 1) {
          numInteger = this.inputValue.split(".")[0];
          this.inputValue =
            numInteger.length > 10 ? numInteger.slice(0, 10) : numInteger;
        } else {
          numInteger = this.inputValue.split(".")[0];
          numDecimal = this.inputValue.split(".")[1];
          this.inputValue =
            numInteger.length > 10
              ? numDecimal.length > num
                ? numInteger.slice(0, 10) + "." + numDecimal.slice(0, num)
                : numInteger.slice(0, 10) + "." + numDecimal
              : numDecimal.length > num
              ? numInteger + "." + numDecimal.slice(0, num)
                : numInteger + "." + numDecimal;
        }
      });
    },
    onNumberDelete() {
      if (this.inputValue === "") {
        return;
      }
      this.inputValue = this.inputValue.replace(/,/g, "").substr(0, this.inputValue.length - 1);
    },
    enterNumbers() {
      let value = this.inputValue;
      this.inputValue = "";
      this.isPlaceholderShow = false;
      this.isKeyBoardShow = true;
      this.$refs.scanTextbox.setAttribute("readonly", "readonly") ||
        this.$refs.scanTextbox.trigger("blur");
      this.inputValue = value;
    },
    closeEnterNumbers() {
      this.isPlaceholderShow = true;
      this.isKeyBoardShow = false;
    },
  }
};
</script>

<style lang="scss" scoped>
.index-page {
  width: 100%;
  min-height: 100vh;
  background: var(--background);
  ::v-deep .md-input-item-fake-placeholder {
    left: 0px;
    font-size: 32px;
    border-right-color: var(--text_3rd);
  }
  ::v-deep .md-input-item-fake {
    width: 450px;
    color: var(--text_1st);
    padding: 0px 20px;
    font-size: 48px;
    // line-height: 72px;
    font-family: D-DINExp, D;
  }
  ::v-deep .md-field-item-content::before {
    border: none !important;
  }
  ::v-deep .md-field-item-content {
    width: 300px;
  }
  ::v-deep .md-field-item-content:before {
    border: none;
  }
  .yield-in-recentYear {
    font-size: 24px;
    color: var(--text_4rd);
    padding: 0 32px 16px;
    background: var(--background);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      &:first-child {
        color: var(--text_4rd);
        margin-right: 16px;
      }
    }
  }
  .dialog-main {
    background: var(--mask);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    .dialog {
      position: absolute;
      top: 8%;
      left: 30px;
      width: calc(100% - 60px);
      font-size: 32px;
      color: var(--text_1st);
      border-radius: 30px;
      background: var(--background);

      .title {
        text-align: center;
        position: relative;
        font-weight: 500;
        font-size: 34px;
        img {
          z-index: 5000;
          width: 32px;
          height: 32px;
          position: absolute;
          top: 50%;
          right: 30px;
          margin-top: -16px;
        }
        .text {
          line-height: 132px;
        }
      }
      .num {
        text-align: center;
        vertical-align: bottom;
        .text1 {
          font-size: 28px;
          padding: 18px 0;
          margin-right: 16px;
        }
        .text2 {
          font-size: 48px;
          line-height: 100px;
        }
      }
      .account {
        padding: 0 50px;
        margin-top: 32px;
        font-size: 28px;
        .text1 {
          color: var(--text_3rd);
          margin-bottom: 8px;
        }
      }
      .product-name {
        padding: 0 50px;
        font-size: 28px;
        margin: 24px 0;
        .text1 {
          color: var(--text_3rd);
          margin-bottom: 8px;
        }
      }
      .row-tip {
        font-size: 22px;
        color: var(--text_3rd);
        padding: 20px 32px;
        background: var(--background);
        .red {
          color: var(--red);
        }
      }
      .tips {
        color: var(--text_3rd);
        font-size: 22px;
        padding: 16px 20px 12px;
        margin: 0 40px;
        background: var(--brand_06);
      }
      .password {
        margin: 32px 40px 16px 40px;
        padding: 0 24px;
        border-radius: 10px;
        background: var(--gray_05);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .eye-passord {
          width: 48px;
        }
        .input-box {
          appearance: button;
          background: var(--gray_05);
          color: var(--text_1st);
          border: none;
          outline: none;
          font-size: 28px;
          width: 100%;
          height: 100px;
        }
      }
      .error-input {
        border: 1px solid var(--error);
      }
      .password-prompt {
        margin: 0 50px;
        font-size: 28px;
        display: flex;
        justify-content: flex-end;
        position: relative;
        .password-err {
          position: absolute;
          top: 0px;
          left: 0px;
          color: var(--error);
        }
        .password-forget {
          color: var(--brand_02);
        }
      }
      .bounced-operation {
        border-top: 1px solid var(--line_01);
        margin-top: 40px;
        padding: 24px 40px 46px;
        display: flex;
        justify-content: space-around;
        div {
          width: 50%;
          line-height: 90px;
          text-align: center;
          font-size: 28px;
        }
        .cancel {
          color: var(--text_1st);
          border: 1px solid var(--line_02);
          margin-right: 42px;
        }
      }
    }
  }
  .page-title {
    padding: 16px 32px;
    text-align: center;
    font-size: 26px;
    background: var(--background);
    .text-right {
      color: var(--brand_02);
      text-align: center;
    }
  }
  .row-money-page {
    border-top: 32px solid var(--gray_05);
    padding: 32px;
    font-size: 34px;
    font-weight: PingFangSC-Medium, PingFang SC, sans-serif;
    color: var(--text_1st);
    background: var(--background);
  }
  .row-num-page {
    position: relative;
    padding: 0px 32px;
    background: var(--background);
    font-size: 32px;
    .content-border {
      border-bottom: 1px solid var(--line_01);
      display: flex;
      justify-content: space-between;
      align-items: center;
      vertical-align: top;
    }
    .left {
      display: inline-block;
      position: relative;
      max-width: calc(100% - 130px);
      border: none;
      .amout {
        width: 100%;
      }
      ::v-deep .md-field-item-content {
        width: 100%;
      }
      ::v-deep .md-field-item-content:before {
        border: none !important;
      }
      .placeholder {
        border: none;
        outline: none;
        font-size: 32px;
        width: 450px;
        overflow: hidden;
      }
      .masker {
        position: absolute;
        left: 90px;
        top: 0px;
        width: 450px;
        height: 100px;
        background-color: chartreuse;
        z-index: 6000;
        opacity: 0;
      }
    }
    .content-right {
      color: var(--brand_02);
      text-align: right;
    }
  }
  .row-num2-page {
    padding: 20px 30px;
    font-size: 28px;
    background: var(--background);
    .title {
      color: var(--text_3rd);
    }
    .content-right {
      color: var(--brand_07);
      padding-left: 20px;
    }
  }
  .row-tip {
    font-size: 22px;
    color: var(--text_3rd);
    padding: 20px 32px;
    background: var(--background);
    div {
      padding: 16px 20px;
      border-radius: 10px;
      background: var(--brand_06);
      &:last-child {
        padding-top: 0;
      }
    }
    .red {
      color: var(--red);
    }
  }
  .padding-20 {
    padding: 16px 32px;
    background: var(--background);
    .sell-button {
      color: var(--text_1st);
      width: 100%;
      height: 90px;
      line-height: 90px;
      font-size: 30px;
      font-weight: 400;
      text-align: center;
    }
  }
  .row-agree {
    padding: 80px 30px 24px;
    font-size: 28px;
    background: var(--background);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    img {
      width: 36px;
      height: 36px;
      margin-right: 24px;
      vertical-align: middle;
    }
    .title {
      color: var(--text_3rd);
    }
    .content-right {
      color: var(--brand_01);
    }
  }
}
</style>
