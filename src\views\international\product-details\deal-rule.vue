<template>
  <div class="deal-rule-box">
    <ul class="deal-rule-select">
      <li class="deal-rule-tab">
        <div v-for="(item, index) in $t('fundMessage.dealRuleTabs')" :key="index" @click="checkType(item)"
             :class="{ activeType: dealRuleCurrent == item.name }">
          <div>{{ item.label }}</div>
          <div :key="index" class="bottom-border" :class="{ activeBorder: dealRuleCurrent == item.name }"></div>
        </div>
      </li>
      <template v-if="tradeRulesData && JSON.stringify(tradeRulesData) !== '{}'">
        <template v-if="dealRuleCurrent != 3">
          <!-- 申购规则 -->
          <template v-if="dealRuleCurrent == 1">
            <div class="deal-rule-rate-border">
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list deal-rule-top-border">
                  <label>{{ $t('fundMessage.ruleData')[0].label }}</label>
                  <span>
                    {{ dealRuleValue(tradeRulesData.minFirstInvestment,tradeRulesData.currency) }}
                  </span>
                </div>
              </li>
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list">
                  <label>{{ $t('fundMessage.ruleData')[1].label }}</label>
                  <div class="discount">
                    <template v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                      <div class="ladder-charge">
                        <span>{{ $t("fundMessage.ladderCharge") }}</span>
                        <img :src="require(`@/assets/images/international/${showLadderCharge ? 'fold' : 'unfold'}.png`)" alt="" @click="showLadderCharge = !showLadderCharge">
                      </div>
                    </template>
                    <template v-else>
                      <div class="activeColor">
                        {{
                          tradeRulesData.subscriptionRatePreferential[0].rate ? (tradeRulesData.subscriptionRatePreferential[0].rate*100).toFixed(2) : (tradeRulesData.subscriptionRate[0].rate*100).toFixed(2)
                        }}%
                      </div>
                      <div class="line-through" v-if="tradeRulesData.subscriptionRatePreferential[0].rate">
                        {{ tradeRulesData.subscriptionRate[0].rate ? (tradeRulesData.subscriptionRate[0].rate*100).toFixed(2) + '%' : '0.00' }}
                      </div>
                    </template>
                  </div>
                </div>
                <!--  阶梯式费率 -->
                <div class="ladder-charge-table" v-if="showLadderCharge">
                  <ul>
                    <li>
                      <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                      <div>{{ $t("myAccount.purchaseRates") }}</div>
                    </li>
                    <template v-if="tradeRulesData.subscriptionRatePreferential && tradeRulesData.subscriptionRatePreferential.length > 1">
                      <template v-for="(item, index) in tradeRulesData.subscriptionRatePreferential">
                        <!-- <template v-for="(item1, index1) in tradeRulesData.subscriptionRatePreferential"></template> -->
                        <li :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                          <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                          <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                          <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                          <div>
                            <span>{{ (item.rate * 100).toFixed(2) }}%</span>
                            <span class="discounts-price">{{ (tradeRulesData.subscriptionRate[index].rate * 100).toFixed(2) }}%</span>
                          </div>
                        </li>
                      </template>
                      <!-- <li v-for="(item, index) in tradeRulesData.subscriptionRatePreferential" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                        <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                        <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                      </li> -->
                    </template>
                    <template v-else>
                      <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                        <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                        <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                        <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                      </li>
                    </template>
                  </ul>
                </div>
              </li>
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list">
                  <label>{{ $t('fundMessage.ruleData')[2].label }}</label>
                  <span>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t('common.tradingDay')}}</span>
                </div>
              </li>
            </div>
          </template>
          <!-- 赎回规则 -->
          <template v-else>
            <div class="deal-rule-rate-border">
              <li class="deal-rule-rate-time deal-rule-rate-top">
                <div class="deal-rule-rate-time-list deal-rule-top-border">
                  <label>{{ $t('fundMessage.redeemData')[0].label }}</label>
                  <span>
                    {{ dealRuleValueRedem(tradeRulesData.minRansom) }}
                  </span>
                </div>
              </li>
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list">
                  <label>{{ $t('fundMessage.redeemData')[1].label }}</label>
                  <div v-if="tradeRulesData.redemptionRatePreferential" class="discount">
                    <div class="activeColor">
                      {{
                        (tradeRulesData.redemptionRatePreferential*100).toFixed(2)
                      }}%
                    </div>
                    <div class="line-through">
                      {{ (tradeRulesData.redemptionRate*100).toFixed(2) }}%
                    </div>
                  </div>
                  <div v-else>
                    <div class="activeColor">
                      {{ (tradeRulesData.redemptionRate*100).toFixed(2) }}%
                    </div>
                    <div class="line-through"></div>
                  </div>
                </div>
              </li>
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list">
                  <label>{{ $t('fundMessage.redeemData')[2].label }}</label>
                  <span>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t('common.tradingDay')}}</span>
                </div>
              </li>
              <li class="deal-rule-rate-time">
                <div class="deal-rule-rate-time-list">
                  <label>{{ $t('fundMessage.redeemData')[3].label }}</label>
                  <span>T+{{ tradeRulesData.redemptionPlusDay }}{{ $t('common.tradingDay')}}</span>
                </div>
              </li>
            </div>
          </template>
          <!-- 申购流程 -->
          <li class="deal-rule-rate-time deal-rule-rate-top">
            <DealRuleComponent :dealRuleCurrent="dealRuleCurrent" :shareConfirmedDay="shareConfirmedDay" :toTheAccount="toTheAccount" :endOfTradingDay="tradeRulesData.endOfTradingDay"></DealRuleComponent>
            <div class="rule-describe">
              <p>{{ $t('fundMessage.subscribeDescribe.content', { time: tradeRulesData.endOfTradingDay }) }}</p>
              <p>{{ $t('fundMessage.subscribeDescribe.content1', { timeEnd: tradeRulesData.cancellationDeadline}) }}</p>
            </div>
          </li>
          <li class="deal-rule-rate-time deal-rule-rate-top">
            <div class="fund-rest">{{ $t('common.fundClose')}}</div>
            <div v-for="(item, index) in closeDayData" :key="index" class="fund-rest-list">
              {{ item.closeDate }}
            </div>
            <div class="all-list-data">
              <!-- <span v-if="dataTotal" @click="uploadData">{{
                $t('fundMessage.moreMessage')
              }}</span>
              <span v-else>{{ $t('fundMessage.updateMoreMessage') }}</span> -->
            </div>
          </li>
        </template>
        <!-- 费用说明 -->
        <template v-else>
          <li class="deal-rule-rate-time deal-rule-expense">
            <div class="deal-border-radius">
              <div class="expense-table">
                <p>{{ $t("fundMessage.subscribe") }}{{ $t("fundMessage.rate") }} ({{ $t('fundMessage.discounts') }})</p>
                <p class="ladder-charge"  v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                  <span>{{ $t("fundMessage.ladderCharge") }}</span>
                  <img :src="require(`@/assets/images/international/${subscriptCost ? 'unfold' : 'fold'}.png`)" alt="" @click="subscriptCost = !subscriptCost">
                </p>
                <p :class="{ activeColor: !tradeRulesData.subscriptionRate[0].rate }" class="subscription-rate" v-else>
                  {{
                    tradeRulesData.subscriptionRatePreferential[0].rate ? formatRate(tradeRulesData.subscriptionRatePreferential[0].rate) :
                    tradeRulesData.subscriptionRate[0].rate ? formatRate(tradeRulesData.subscriptionRate[0].rate) : '--'
                  }}
                </p>
              </div>
              <!-- 申购--阶梯式收费 -->
              <template v-if="subscriptCost">
                <div class="ladder-charge-table-box" v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                  <div class="ladder-charge-table">
                    <ul>
                      <li>
                        <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                        <div>{{ $t("myAccount.purchaseRates") }}</div>
                      </li>
                      <template v-if="showLodder()">
                        <template v-for="(item, index) in tradeRulesData.subscriptionRatePreferential">
                          <li :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                            <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                            <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                            <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                            <div >
                              <span>{{ (item.rate * 100).toFixed(2) }}%</span>
                              <span class="discounts-price">{{ (tradeRulesData.subscriptionRate[index].rate * 100).toFixed(2) }}%</span>
                            </div>
                          </li>
                        </template>
                      </template>
                      <template v-else>
                        <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                          <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                          <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                          <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                          <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                        </li>
                      </template>
                    </ul>
                  </div>
                </div>
              </template>
              <div class="expense-table">
                <p>{{ $t('fundMessage.platformFeeRate') }}</p>
                <p v-if="tradeRulesData.platformFeeRate.length > 1" class="ladder-color">
                  <span>{{ $t("fundMessage.ladderCharge") }}</span>
                  <img :src="require(`@/assets/images/international/${platformCost ? 'unfold' : 'fold'}.png`)" alt="" @click="platformCost = !platformCost">
                </p>
                <p :class="{ activeColor: !tradeRulesData.platformFeeRate[0].rate }" v-else>
                  {{
                    tradeRulesData.platformFeeRate[0].rate
                      ? formatRate(tradeRulesData.platformFeeRate[0].rate)
                      : '--'
                  }}
                </p>
              </div>
              <!-- 平台费--阶梯式收费 -->
              <template v-if="platformCost">
                <div class="ladder-charge-table-box" v-if="tradeRulesData.platformFeeRate.length > 1">
                  <div class="ladder-charge-table">
                      <ul>
                        <li>
                          <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                          <div>{{ $t("myAccount.platformFeeRate") }}</div>
                        </li>
                        <li v-for="(item, index) in tradeRulesData.platformFeeRate" :key="index" :class="{'background-grey': index % 2 != 0 && index != 0}">
                          <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;金额≤{{item.end}}</div>
                          <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;金额≤{{item.end}}</div>
                          <div v-if="item.end =='inf'">金额&gt;{{item.from}}</div>
                          <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                        </li>
                      </ul>
                    </div>
                </div>
              </template>
              <div class="fees-contnet" v-if="renderFees">
                <p class="fees-title">{{ $t('fundMessage.fees') }}</p>
                <p v-html="renderFees"></p>
              </div>
            </div>
            <div class="expense-remask">
              <div>
                {{ $t('fundMessage.expenseRemark') }}
              </div>
            </div>
            <!-- 费率规则 -->
            <!-- <div class="charge-rule">
              <ul>
                <li v-for="(item, index) in $t('fundMessage.ladderChargeLabelArr')" :key="index">
                  {{item}}
                </li>
              </ul>
            </div> -->
          </li>
        </template>
      </template>
    </ul>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import DealRuleComponent from '@/components/international/dealRuleComponent'
import Loading from '@/components/loading/index'
import { tradeRules, performance } from '@/services/fund'
import { getClosedDays } from '@/services/account'

import moment from 'moment'
import { fmoney } from '@/utils/util.js'
import { mapState } from "vuex";
export default {
  components: {
    DealRuleComponent,
    Loading,
  },
  data() {
    return {
      isLoading: true,
      tradeRulesData: {},
      dealRuleCurrent: 1,
      shareConfirmedDay: null,
      toTheAccount: 0,
      closeDayData: [],
      dataTotal: false,
      pageNum: 0,
      pageSize: 365,
      showLadderCharge: false,
      subscriptCost: false,
      platformCost: false,
      langObj: {
        'en': 'us',
        'zh-hans': 'cn',
        'zh-hant': 'hk'
      }
    }
  },
  computed: {
    ...mapState(["locale"]),
    renderFees() {
      let lang = this.langObj[this.locale]
      return this.tradeRulesData?.disclosedContent?.[lang] ?? ''
    },
  },
  methods: {
    formatRate(item) {
      return fmoney(item*100,2) + '%'
    },
    checkType(data) {
      // tabs
      this.dealRuleCurrent = data.name;
      this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
      if(data.name === 1) {
        this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
      }else {
        this.toTheAccount = this.tradeRulesData.redemptionPlusDay
      }
    },
    dealRuleValue(item1, currency) {
      if (item1 && currency == 'HKD') {
        return fmoney(item1,2) + this.$t('myAccount.' + 'HKDollar')
      } else if(item1 && currency == 'USD'){
        return fmoney(item1,2) + this.$t('myAccount.' + 'dollar')
      } else {
        return '0.00'
      }
    },
    dealRuleValueRedem(item1) {
      if (item1) {
        return fmoney(item1,4) + '份'
      } else {
        return '0.00份'
      }
    },
    performance() {
      this.isLoading = true;
      let params = {
        page: this.pageNum, 
        size: this.pageSize,
        closeDateEnd: (new Date().getFullYear() + '-12-31'),
        closeDateStart: (new Date().getFullYear() + '-01-01')
      }
      getClosedDays(
        params,
        this,
        this.$route.query.id,
        this.$jsBridge.isSupported('getAppInfo')
      ).then((res) => {
        // 基金休息日
        if (res.code == 200) {
          if(res.data.totalElements <= this.pageSize) {
            this.dataTotal = false;
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
          }else {
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
            this.dataTotal = true;
          }
          this.isLoading = false
        }
      })
    },
    uploadData() {
      // 加载更多
      this.pageSize = this.pageSize + 15;
      this.performance()
    },
    showLodder () {
      if (this.tradeRulesData.subscriptionRatePreferential && (this.tradeRulesData.subscriptionRatePreferential.length > 1) && this.tradeRulesData.subscriptionRatePreferential[0].rate) {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.dealRuleCurrent = this.$route.query.type ?  parseInt(this.$route.query.type) : 1
    tradeRules(this.$route.query.id).then((res) => {
      if (res.code == 200) {
        let timeData = res.data.endOfTradingDay.split(':');
        res.data.endOfTradingDay = timeData[0] + ':' + timeData[1];
        let timeEndData = res.data.cancellationDeadline.split(':');
        res.data.cancellationDeadline = timeEndData[0] + ':' + timeEndData[1];
        this.tradeRulesData = res.data
        this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
        this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
      }
      this.isLoading = false
    })
    this.performance()
  },
}
</script>

<style lang="scss" scoped>
.deal-rule-box {
  height: 100%;
  color: var(--text_1st);
  background: var(--gray_05);
  overflow: auto;
  .deal-rule-select {
    .deal-rule-tab {
      padding: 22px 30px 8px;
      display: flex;
      background: var(--background);
      justify-content: space-around;
      align-items: center;
      font-size: 30px;
      color: var(--text_3rd);
      text-align: center;
      li {
        margin-right: 50px;
      }
    }
    .deal-rule-rate-border {
      margin-top: 20px;
      background: var(--background);
    }
    .deal-rule-rate-time {
      padding: 0 32px;
      font-size: 28px;
      .deal-rule-rate-time-list {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px 0;
        border-top: 1px solid var(--line_01);
        .discount {
          display: flex;
          justify-content: space-between;
          align-items: top;
          .ladder-charge {
            width: 100%;
            margin: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            span {
              color: var(--text_1st);
              font-size: 26px;
            }
            img { 
              width: 36px;
              height: 36px;
              margin-left: 15px;
            }
          }
        }
        .activeColor {
          color: var(--red);
          // margin-right: 20px;
          max-width: 200px;
          text-align: center;
        }
        .line-through {
          text-decoration: line-through;
          text-align: center;
          max-width: 200px;
          color: var(--text_3rd);
          margin-left: 20px;
        }
      }
      .deal-rule-top-border {
        border: 0;
      }
      .deal-rule-rate-time-border {
        border: 0;
      }
      .rule-describe {
        font-size: 26px;
        line-height: 39px;
        padding: 41px 0 40px;
        p {
          &:last-child {
            margin-top: 50px;
          }
        }
      }
      .fund-rest {
        color: var(--text_1st);
        font-size: 34px;
        font-weight: 500;
        padding: 20px 0 22px;
      }
      .fund-rest-list {
        font-size: 28px;
        padding: 22px 0;
        border-bottom: 1px solid var(--line_01);
        &:last-child {
          border: 0;
        }
      }
      .expense-table {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        padding: 28px 0;
        font-size: 28px;
        border-top: 1px solid var(--line_01);
        &:first-child {
          border: 0;
        }
        p {
          width: 50%;
          &:last-child {
            text-align: right
          }
        }
        .ladder-color {
          color: var(--text_1st);
          display: flex;
          justify-content: flex-end;
          align-items: center;
          img { 
            width: 36px;
            height: 36px;
            margin-left: 15px;
          }
        }
        .subscription-rate {}
        .ladder-charge {
          margin: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          span {
            color: var(--text_1st);
          }
          img { 
            width: 36px;
            height: 36px;
            margin-left: 15px;
          }
        }
      }
      .fees-contnet {
        margin-top: 24px;
        .fees-title {
          color: var(--text_1st);
          font-weight: bold;
          margin-bottom: 24px;
        }
      }
      .expense-remask {
        padding: 48px 32px 30px;
        background: var(--background);
        div {
          font-size: 24px;
          color: var(--text_3rd);
          background: var(--gray_05);
          padding: 24px;
          border-radius: 10px;
          line-height: 36px;
        }
      }
      .all-list-data {
        padding: 27px 0;
        text-align: center;
        @include font_color(second-text);
        img {
          width: 28px;
        }
      }
    }
    .deal-rule-rate-top {
      margin-top: 20px;
      border-top: 0;
      background: var(--background);
    }
    // 费用说明
    .deal-rule-expense {
      padding: 0;
      .deal-border-radius {
        padding: 0 30px;
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
        background: var(--background);
      }
      .ladder-charge-table-title {
        margin: 50px 0 30px;
        font-size: 26px;
        font-weight: 600;
        @include font_color(text-color);
      }
      // .ladder-charge-table-box {
      //   margin-top: 30px;
      // }
      .charge-rule {
        margin-top: 30px;
        font-size: 24px;
        font-weight: 400;
        line-height: 42px;
        @include font_color(second-text);
      }
    }
  }

  // 阶梯式收费样式
  .ladder-charge-table {
    ul {
      background: var(--background);
      li {
        font-size: 28px;
        font-weight: 400;
        color: var(--text_1st);
        padding: 20px 24px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &:first-child {
          font-weight: 500;
          color: var(--text_3rd);
          background: var(--gray_05);
        }
        div {
          width: 70%;
          &:last-child {
            width: 30%;
            text-align: right;
          }
        }
      }
    }
    .background-grey {
      background: var(--gray_05);
    }
  }
  .discounts-price {
    margin-left: 16px;
    color: var(--text_3rd);
    text-decoration: line-through;
  }
}
</style>
