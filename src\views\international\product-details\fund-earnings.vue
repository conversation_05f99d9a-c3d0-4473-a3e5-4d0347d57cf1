<template>
  <!-- 万元收益--分页数据页面 -->
  <div class="fund-earnings-box">
    <div class="fund-earnings-bg">
      <div class="fund-earnings-header">
        <span>{{ $t("fundMessage.timeText") }}</span>
        <span>{{ $t("fundMessage.earnings") }}</span>
      </div>
      <ul class="fund-earnings-data">
        <li
          v-for="(item, index) in fundTimeData"
          :key="index"
          class="fund-earnings-li"
          :class="{'border-none': index == fundTimeData.length - 1}"
        >
          <span>{{ item.profitDate }}</span>
          <span
            :class="
              klineTheme === 'redRiseGreenFall'
                ? Number(item.cumulative10Thousand) < 0
                  ? 'output'
                  : Number(item.cumulative10Thousand) == 0
                  ? 'entry1'
                  : 'entry'
                : Number(item.cumulative10Thousand) > 0
                ? 'output'
                : Number(item.cumulative10Thousand) == 0
                ? 'entry1'
                : 'entry'
            "
          >
            {{
              Number(item.cumulative10Thousand) > 0
                ? "+" + Number(item.cumulative10Thousand).toFixed(4)
                : Number(item.cumulative10Thousand).toFixed(4)
            }}
          </span>
        </li>
        <div @click="uploadData" class="fund-earnings-text" v-if="fundTimeData.length > 0">
          <span>{{ dataList ? $t("fundMessage.updateMoreMessage") : $t("fundMessage.moreMessage") }}</span>
        </div>
        <div v-else class="fund-earnings-text">
          <span>{{ $t("fundMessage.notMessage") }}</span>
        </div>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { performance } from "@/services/fund";
export default {
  props: {
    investDetails: {
      type: Boolean,
    },
  },
  components: {},
  data() {
    return {
      fundTimeData: [],
      dataList: false,
      pageNum: 0,
      pageSize: 15
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    uploadData() {
      this.performance();
      if (!this.dataList) {
        this.pageNum += 1;
        // this.pageSize += 15;
      }
    },
    performance() {
      if (!this.dataList) {
        performance(
          { page: this.pageNum, size: this.pageSize },
          this.$route.query.id,
          "",
          "ten-thousand"
        ).then((res) => {
          // console.log('万元收益=====', res)
          if (res.code == 200) {
            if (this.fundTimeData.length > res.data.totalElements) {
              this.dataList = true;
            } else {
              this.fundTimeData = this.fundTimeData.concat(res.data.content);
            }
          }
        });
      }
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.fund-earnings-box {
  min-height: 100vh;
  overflow: auto;
  background: var(--gray_05);
  .fund-earnings-bg {
    padding: 0 30px;
  }
  .fund-earnings-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    color: var(--text_3rd);
    border: 1px solid var(--line_01);
    border-left: 0;
    border-right: 0;
  }
  .fund-earnings-data {
    font-size: 26px;
    color: var(--text_1st);
    .fund-earnings-li {
      padding: 24px 0;
      border-bottom: 1px solid var(--line_01);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .border-none {
      border: 0;
    }
  }
  .fund-earnings-text {
    text-align: center;
    padding: 24px 0;
    span {
      font-size: 24px;
      color: var(--brand_01);
      border-bottom: 1px solid var(--brand_01);
    }
  }
}
</style>
