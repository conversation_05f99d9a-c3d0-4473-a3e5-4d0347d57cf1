<template>
  <div class="fund-possess-box">
    <ul class="fund-possess-select">
      <li class="fund-possess-tab">
        <div
          v-for="(item, index) in $t('fundMessage.chartTypeTabs')"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: chartCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: chartCurrent == item.name }"
          ></div>
        </div>
      </li>
    </ul>
    <template v-if="chartCurrent != 2">
      <div class="fund-possess-chart-box">
        <div class="canvas-title">
          {{ $t('fundMessage.propertyStructure') }}
        </div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.assets &&
              compositionsPie.assets.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.assetUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.assetWeek}`) }}
          </div>
          <div class="myChart-box">
            <canvas id="assetName"></canvas>
            <ul>
              <li v-for="(item,index) in assetName_pie" :key="index">
                <span class="round" :style="{'background': assetName_color[index]}"></span>
                <span class="text">{{item.assetName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
      <div class="fund-possess-chart-box">
        <div class="canvas-title">
          {{ $t('fundMessage.industryDistribute') }}
        </div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.sectors &&
              compositionsPie.sectors.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.sectorUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.sectorWeek}`) }}
          </div>
          <div class="myChart-box">
            <canvas id="sectorName"></canvas>
            <ul>
              <li v-for="(item,index) in sectorName_pie" :key="index">
                <span class="round" :style="{'background': sectorName_color[index]}"></span>
                <span class="text">{{item.sectorName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
      <div class="fund-possess-chart-box">
        <div class="canvas-title">{{ $t('fundMessage.territory') }}{{ $t('fundMessage.distribute') }}</div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.regionals &&
              compositionsPie.regionals.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.regionalUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.regionalWeek}`) }}
          </div>
          
          <div class="myChart-box">
            <canvas id="regionalName"></canvas>
            <ul>
              <li v-for="(item,index) in regionalName_pie" :key="index">
                <span class="round" :style="{'background': sectorName_color[index]}"></span>
                <span class="text">{{item.regionalName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
    </template>
    <template v-else>
      <div
        v-if="
          compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length
        "
        class="entrepot-number"
      >
        <holdPosition
          :chartTitle="true"
          :compositionsPie="compositionsPie"
        ></holdPosition>
      </div>
      <div v-else class="invest-chart-height1">
        <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
        <div>{{ $t('fundMessage.notMessage') }}</div>
      </div>
    </template>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import F2 from '@antv/f2'
import { mapState } from 'vuex'
import holdPosition from '@/components/international/fundComponent/hold-position.vue'
import Loading from '@/components/loading/index'
import { fundCompositions } from '@/services/fund'
export default {
  components: {
    holdPosition,
    Loading,
  },
  data() {
    return {
      chartCurrent: 1,
      columnFundIsin: '',
      compositionsPie: {},
      isLoading: true,
      assetName_pie: [],
      assetName_color: [ '#5B8FF9', '#61DDAA', '#65789B', '#F6BD16', '#7262FD', '#78D3F8' ],
      sectorName_pie: [],
      sectorName_color: [ '#5B8FF9', '#61DDAA', '#65789B', '#F6BD16', '#7262FD', '#78D3F8' ],
      regionalName_pie: [],
    }
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    checkType(data) {
      this.isLoading = true
      this.chartCurrent = data.name
      if (data.name == 2) {
        this.isLoading = false
      } else {
        this.fundPieCloumn(this.$route.query.id)
      }
    },
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    chartPie(ID, Data, chartColor) {
      if (Data && Data.length) {
        Data = Data.sort(this.compare('proportion'))
        let residue_data = Data.slice(5, Data.length)
        Data = Data.slice(0, 5)
        let residue_proportion = 0;
        switch (ID) {
          case 'assetName':
            this.assetName_pie = Data.slice(0, 5) // 资产结构
            break;
          case 'sectorName':
            this.sectorName_pie = Data.slice(0, 5) // 行业分布
            break;
          case 'regionalName':
            this.regionalName_pie = Data.slice(0, 5) // 地域分布
            break;
          default:
            break;
        }
        if (residue_data && residue_data.length) {
          residue_data.forEach((item) => {
            residue_proportion += Number(item.proportion);
          })
          // 要处理--length > 5 时才会显示 其他状态
          let object = {
            proportion: residue_proportion + ''
          }
          object[ID] = '其他'
          this[ID + '_pie'] = this[ID + '_pie'].concat(object)
        }
        Data.forEach(function (obj) {
          // 对 proportion 字段数字化处理
          obj.proportion = Math.abs(Number(obj.proportion))
        })
      }

      const chart = new F2.Chart({
        id: ID,
        pixelRatio: window.devicePixelRatio,
        width: 180,
        height: 150,
        padding: 0,
      })
      chart.source(Data)
      chart.legend(
        false
        // {
        //   position: 'right',
        //   itemFormatter: function itemFormatter(val) {
        //     return val + '       ' + map[val]
        //   },
        // }
      )
      chart.coord('polar', {
        transposed: true,
        innerRadius: 0.7,
        radius: 0.85,
      })
      chart.axis(false) // 去除坐标轴
      chart
        .interval()
        .position('1*proportion')
        .color(ID, chartColor)
        .adjust('stack')
      chart.render()
    },
    fundPieCloumn(ID) {
      this.isLoading = true
      fundCompositions(ID, '').then((res) => {
        if (res.code == 200) {
          if (this.chartCurrent == 1) {
            this.compositionsPie = res.data
            this.isLoading = false
            this.$nextTick(() => {
              if (res.data && res.data.assets && res.data.assets.length) {
                this.chartPie('assetName', res.data.assets, this.assetName_color)
              }
              if (res.data && res.data.sectors && res.data.sectors.length) {
                this.chartPie('sectorName', res.data.sectors, this.sectorName_color)
              }
              if (res.data && res.data.regionals && res.data.regionals.length) {
                this.chartPie('regionalName', res.data.regionals, this.sectorName_color)
              }
            })
          }
        }
      })
    }
  },
  mounted() {
    this.checkType(this.$t('fundMessage.chartTypeTabs')[0])
  },
}
</script>

<style lang="scss" scoped>
.fund-possess-box {
  min-height: 100vh;
  overflow: auto;
  background: var(--gray_05);
  .fund-possess-select {
    color: var(--text_3rd);
    background: var(--background);
    .fund-possess-tab {
      padding: 12px 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 30px;
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .product-components {
      padding: 20px 30px 34px;
      height: 620px;
    }
  }
  .invest-chart-height1 {
    margin-top: 20px;
    padding: 230px 0 256px;
    width: 100%;
    text-align: center;
    background: var(--background);
    .pic-empty {
      width: 331px;
      // height: 336px;
    }
    div {
      color: var(--text_3rd);
      font-size: 28px;
      height: 72px;
      line-height: 72px;
      text-align: center;
    }
  }
  .fund-possess-chart-box {
    padding: 16px 32px;
    margin-top: 20px;
    background: var(--background);
    &:first-child {
      margin-top: 0;
      padding-top: 64px;
    }
    .invest-chart-height1 {
      padding: 230px 0 256px;
      width: 100%;
      text-align: center;
      background: var(--background);
      .pic-empty {
        width: 331px;
        // height: 336px;
      }
      div {
        color: var(--text_3rd);
        font-size: 28px;
        height: 72px;
        line-height: 72px;
        text-align: center;
      }
    }
    .canvas-title {
      color: var(--text_1st);
      font-size: 30px;
      font-weight: bold;
      line-height: 34px;
    }
    #myChart,
    #myChart1,
    #myChart2 {
      width: 100%;
      height: 100%;
    }
    .fund-possess-pie-update-time {
      font-size: 22px;
      color: var(--text_3rd);
    }
    .myChart-box {
      height: 332px;
      position: relative;
      #assetName, #sectorName, #regionalName {
        position: absolute;
        left: -70px;
        top: 10px;
      }
      // overflow-y: auto;
      display: flex;
      justify-content: space-between;
      align-items: top;
      ul {
        position: absolute;
        right: 30px;
        top: 30px;
        height: 332px;
        // width: 428px;
        width: 55%;
        li {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: var(--text_1st);
          span {
            display: inline-block;
            height: 42px;
            line-height: 42px;
          }
          .round {
            width: 14px;
            height: 14px;
            border-radius: 50% 50%;
            margin-left: 10px;
          }
          .text {
            font-size: 22px;
            padding-left: 14px;
            padding-right: 17px;
            width: 70%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .right-percent {
            font-size: 22px;
            width: 20%;
            text-align: left;
          }
        }
      }
    }
  }
  .entrepot-number {
    padding: 0 30px;
    background: var(--background);
  }
}
</style>
