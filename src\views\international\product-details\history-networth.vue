<template>
  <!-- 历史净值 -->
  <div class="history-net-worth-box">
    <div class="history-net-worth-header">
      <span>{{ $t("fundMessage.timeArea") }}</span>
      <span>{{ $t("fundMessage.netValue") }}</span>
      <span>{{ $t("fundMessage.upDown") }}</span>
    </div>
    <ul class="history-net-worth-data">
      <li
        v-for="(item, index) in fundTimeData"
        :key="index"
        class="history-net-worth-li"
      >
        <span>{{ item.transDate }}</span>
        <span>{{ Number(item.netValue).toFixed(4) }}</span>
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(item.changePercent) < 0
                ? 'output'
                : Number(item.changePercent)
                ? 'entry'
                : 'entry1'
              : Number(item.changePercent) > 0
              ? 'output'
              : Number(item.changePercent)
              ? 'entry'
              : 'entry1'
          "
        >
          {{ Number(item.changePercent) > 0 ? "+" + (item.changePercent * 100).toFixed(2) : Number(item.changePercent) ? (item.changePercent * 100).toFixed(2) : "0.00" }}%
        </span>
      </li>
      <div class="fund-earnings-text" v-if="fundTimeData.length > 0">
        <span @click="showAllNetWorth">{{ dataList ? $t("fundMessage.updateMoreMessage") : $t("fundMessage.moreMessage") }}</span>
      </div>
      <div v-else class="fund-earnings-text">
        <span>{{ $t("fundMessage.notMessage") }}</span>
      </div>
    </ul>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Loading from "@/components/loading/index";
import { performance } from "@/services/fund";
export default {
  components: {
    Loading,
  },
  data() {
    return {
      isLoading: true,
      dataList: false,
      fundTimeData: [],
      pageNum: 0,
      pageSize: 15,
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showAllNetWorth() {
      // 更多数据
      if (!this.dataList) {
        this.pageNum += 1;
        // this.pageSize += 15;
      }
      this.performance();
    },
    performance() {
      this.isLoading = true;
      if (!this.dataList) {
        performance(
          { page: this.pageNum, size: this.pageSize },
          this.$route.query.id,
          "",
          "netValues",
          "networth"
        ).then((res) => {
          // console.log('历史净值====', res);
          if (res.code == 200) {
            if (this.fundTimeData.length > res.data.totalElements) {
              this.dataList = true;
            } else {
              this.fundTimeData = this.fundTimeData.concat(res.data.content);
            }
          }
          this.isLoading = false;
        });
      }
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.history-net-worth-box {
  min-height: 100vh;
  padding: 0 32px;
  background: var(--gray_05);
  .history-net-worth-header {
    padding: 18px 29px 18px 31px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    color: var(--text_3rd);
    border: 1px solid var(--line_01);
    border-left: 0;
    border-right: 0;
  }
  .history-net-worth-data {
    font-size: 26px;
    color: var(--text_1st);
    .history-net-worth-li-header,
    .history-net-worth-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 33.3%;
        &:nth-child(2) {
          text-align: center;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
    .history-net-worth-li {
      padding: 24px 0;
      border-bottom: 1px solid var(--line_01);
    }
    .fund-earnings-text {
      text-align: center;
      padding: 32px 0;
      span {
        font-size: 24px;
        color: var(--brand_01);
        border-bottom: 1px solid var(--brand_01);
      }
    }
  }
}
</style>
