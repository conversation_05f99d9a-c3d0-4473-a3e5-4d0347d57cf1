<template>
  <!-- 历史业绩 -->
  <div class="history-erformance-box">
    <div class="history-erformance-bg">
      <div class="history-erformance-header">
        <span>{{ $t("fundMessage.timeArea") }}</span>
        <span>{{ $t("fundMessage.upDown") }}</span>
      </div>
      <ul class="history-erformance-data">
        <template v-for="(item, index) in fundTimeData">
          <li class="history-erformance-li" :key="index">
            <span>{{ $t(`fundMessage.dateTime.${index}`) }}</span>
            <span
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? item < 0
                    ? 'output'
                    : Number(item)
                    ? 'entry'
                    : 'entry1'
                  : item > 0
                  ? 'output'
                  : Number(item)
                  ? 'entry'
                  : 'entry1'
              "
            >
              {{ performanceNum(item) }}
            </span>
          </li>
        </template>
      </ul>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Loading from "@/components/loading/index";
import { performance } from "@/services/fund";
export default {
  components: {
    Loading,
  },
  data() {
    return {
      isLoading: true,
      fundTimeData: {},
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    performanceNum(data) {
      if (Number(data) == 0 || !data) {
        return "0.00%";
      }
      if (Number(data) > 0) {
        return "+" + (data * 100).toFixed(2) + "%";
      } else {
        return (data * 100).toFixed(2) + "%";
      }
    },
  },
  mounted() {
    // console.log(this.$route.query);
    performance({}, this.$route.query.id, "", "performance").then((res) => {
      // console.log(res);
      if (res.code == 200) {
        // this.fundTimeData = res.data
        // console.log(this.fundTimeData);
        let newObj = {};
        Object.keys(res.data).forEach((item, i) => {
          if (
            [
              "cumulative1Week",
              "cumulative1M",
              "cumulative3M",
              "cumulative6M",
              "cumulative1Y",
              "cumulative3Y",
              "cumulative5Y",
              "cumulativeMonthly",
              "cumulativeQuarterly",
              "cumulativeYearly",
              "cumulativeSinceLaunch",
            ].includes(item)
          ) {
            newObj[item] = Object.values(res.data)[i];
          }
        });
        this.fundTimeData = newObj;
      }
      this.isLoading = false;
    });
  },
};
</script>

<style lang="scss" scoped>
.history-erformance-box {
  min-height: 100vh;
  overflow: auto;
  color: var(--text_1st);
  background: var(--gray_05);
  .history-erformance-bg {
    padding: 0 30px;
  }
  .history-erformance-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    color: var(--text_3rd);
    border: 1px solid var(--line_01);
    border-left: 0;
    border-right: 0;
  }
  .history-erformance-data {
    font-size: 26px;
    .history-erformance-li {
      padding: 27px 0;
      border-bottom: 1px solid var(--line_01);
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:last-child {
        border-bottom: 0;
      }
    }
  }
}
</style>
