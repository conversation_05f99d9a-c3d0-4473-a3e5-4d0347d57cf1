<template>
  <div class="product-detail-box">
    <div class="download-app-box" v-if="isApp">
      <download-app type="fundDetail"></download-app>
    </div>
    <!-- 产品基本信息 -->
    <ul class="product-detail-header">
      <!-- <li class="product-detail-title">{{ fundsNameFun(productDetails) }}</li> -->
      <li class="product-detail-title">
        {{ (productDetails.name && productDetails.name[langType]) || "-" }}
      </li>
      <li class="product-detail-number">
        <span>ISIN:&nbsp;</span>
        <span>{{ productDetails.fundIsin }}</span>
      </li>
      <li class="product-detail-type">
        <span>{{ productDetails.currencyType }}</span>
        <span>{{ bondType(productDetails.assetClassType, $t("fundMessage.bondType")) }}</span>
        <span>
          {{ isFirst ? tradeRulesData.minFirstInvestment : tradeRulesData.minNextInvestmentAmount }}&nbsp;
          {{ productDetails.currencyType ? $t(`fundMessage.currencyType.${productDetails.currencyType}`) : "" }}&nbsp;
          {{ isFirst ? $t("myAccount.buyPlaceholder") : $t("myAccount.additional") }}
        </span>
        <span class="last-span-box" @click="handleGoRiskInfo">
          {{ riskLevel(productDetails.riskLevel, $t("fundMessage.fundRiskLevel")) }} 
          <img src="@/assets/images/international/arrow.png" alt=""></span>
      </li>
      <li class="product-detail-ratio-netValue">
        <div class="product-detail-fund-data">
          <div class="product-detail-ratio">
            <!-- 其他---七日年化 -->
            <!-- <template v-if="urlDetail.currentType == 'MONEY_MARKET'">
              <p
                :class="
                  klineTheme === 'redRiseGreenFall'
                    ? Number(productDetails.annualised7Day) > 0
                      ? 'fund-up-value'
                      : Number(productDetails.annualised7Day)
                      ? 'fund-drop-value'
                      : 'fund-drop-value1'
                    : Number(productDetails.annualised7Day) < 0
                    ? 'fund-up-value'
                    : Number(productDetails.annualised7Day)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                "
              >
                {{
                  Number(productDetails.annualised7Day) > 0
                    ? "+" + (productDetails.annualised7Day * 100).toFixed(4)
                    : Number(productDetails.annualised7Day)
                    ? (productDetails.annualised7Day * 100).toFixed(4)
                    : "0.0000"
                }}%
              </p>
              <p>
                {{ $t("fundMessage.annualYield") }}&nbsp;
                <img
                  src="@/assets/images/international/instruction2.png"
                  @click="showTermDescribe('seven')"
                  alt="pic"
                />
              </p>
            </template> -->
            <!-- 货币--近一年涨幅 -->
            <template>
              <!-- :class="Number(productDetails.cumulative1Y) > 0 ? 'fund-up-value' : Number(productDetails.cumulative1Y) ? 'fund-drop-value' : 'fund-drop-value1'" -->
              <p
                :class="
                  klineTheme === 'redRiseGreenFall'
                    ? Number(productDetails.cumulativeSinceLaunch) > 0
                      ? 'fund-up-value'
                      : Number(productDetails.cumulativeSinceLaunch)
                      ? 'fund-drop-value'
                      : 'fund-drop-value1'
                    : Number(productDetails.cumulativeSinceLaunch) < 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulativeSinceLaunch)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                "
              >
                {{
                  Number(productDetails.cumulativeSinceLaunch) > 0
                    ? "+" + (productDetails.cumulativeSinceLaunch * 100).toFixed(2)
                    : Number(productDetails.cumulativeSinceLaunch)
                    ? (productDetails.cumulativeSinceLaunch * 100).toFixed(2)
                    : "0.00"
                }}%
              </p>
              <p>
                {{ $t("fundMessage.timeTileView") }}
              </p>
            </template>
          </div>
          <div class="product-detail-netValue">
            <!-- 其他--万元收益  -->
            <template v-if="urlDetail.currentType == 'MONEY_MARKET'">
              <p
                :class="
                  klineTheme === 'redRiseGreenFall'
                    ? Number(productDetails.cumulative10Thousand) > 0
                      ? 'fund-up-value'
                      : Number(productDetails.cumulative10Thousand)
                      ? 'fund-drop-value'
                      : 'fund-drop-value1'
                    : Number(productDetails.cumulative10Thousand) < 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulative10Thousand)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                "
              >
                {{
                  Number(productDetails.cumulative10Thousand) > 0
                    ? "+" + Number(productDetails.cumulative10Thousand).toFixed(4)
                    : Number(productDetails.cumulative10Thousand)
                    ? Number(productDetails.cumulative10Thousand).toFixed(4)
                    : "0.0000"
                }}
              </p>
              <p>
                {{ $t("fundMessage.earnings") }}({{
                  stringToArr(productDetails.profitDate)
                }})&nbsp;
                <img
                  src="@/assets/images/international/instruction2.png"
                  @click="showTermDescribe('tenThousandYuan')"
                  alt=""
                />
              </p>
            </template>
            <!-- 货币-- 日涨跌幅 -->
            <template v-else>
              <!-- :class="Number(productDetails.cumulative1Day) > 0 ? 'fund-up-value' : Number(productDetails.cumulative1Day) ? 'fund-drop-value' : 'fund-drop-value1'" -->
              <p
                :class="
                  klineTheme === 'redRiseGreenFall'
                    ? Number(productDetails.cumulative1Day) > 0
                      ? 'fund-up-value'
                      : Number(productDetails.cumulative1Day)
                      ? 'fund-drop-value'
                      : 'fund-drop-value1'
                    : Number(productDetails.cumulative1Day) < 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulative1Day)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                "
              >
                {{
                  Number(productDetails.cumulative1Day) > 0
                    ? "+" + (productDetails.cumulative1Day * 100).toFixed(2)
                    : Number(productDetails.cumulative1Day)
                    ? (productDetails.cumulative1Day * 100).toFixed(2)
                    : "0.00"
                }}%
              </p>
              <p>{{ $t("fundMessage.cumulative1Day") }}</p>
            </template>
          </div>
          <!-- 单位净值 -->
          <div
            class="product-detail-netValue"
            v-if="urlDetail.currentType != 'MONEY_MARKET'"
          >
            <p class="last-net-value">
              {{ Number(productDetails.netValue || 0).toFixed(4)}}
            </p>
            <p>
              {{ $t("fundMessage.netValue") }}({{
                stringToArr(productDetails.transDate)
              }})
            </p>
          </div>
        </div>
        <!-- 申购赎回按钮 -->
        <div class="product-button-box">
          <div class="product-apply-redeem">
            <md-button type="primary" round @click="goTotTansaction(1)">{{ $t("myAccount.apply") }}</md-button>
            <md-button type="bulletin" round @click="goTotTansaction(0)">{{ $t("myAccount.redeem") }}</md-button>
          </div>
          <div class="product-add-optional">
            <span @click="addOptional(productDetails)" :class="{'delete-optional': isOptional}">{{ isOptional ? $t('fundMessage.addOptionalOver') : $t('fundMessage.addOptional') }}</span>
          </div>
        </div>
      </li>
    </ul>
    <div class="dialog-main" v-show="showDialog">
      <div class="dialog">
        <!-- 七日年化 -->
        <template v-if="isSevenDays">
          <p class="content-describe" v-for="item in $t('myAccount.SevenDayAnnualization')" :key="item.id + 'SevenDay'">
            {{ item.value }}
          </p>
        </template>
        <!-- 万元收益 -->
        <template v-if="!isSevenDays">
          <p class="content-describe" v-for="item in $t('myAccount.TenThousandYuanRevenue')" :key="item.id + 'TenThous'">
            {{ item.value }}
          </p>
        </template>
        <div class="button-bottom">
          <md-button type="primary" round @click="showDialog = false">{{ $t("myAccount.confim") }}</md-button>
        </div>
      </div>
    </div>
    <!-- 产品数据 -->
    <ul class="product-type-select">
      <li class="product-type-tab">
        <div
          v-for="(item, index) in typeTabs"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: productCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: productCurrent == item.name }"
          ></div>
        </div>
      </li>
      <!-- 图表数据 -->
      <div class="product-components">
        <component
          :is="componentName"
          :fundIsin="urlDetail.fundIsin"
          :type="urlDetail.currentType"
        ></component>
      </div>
    </ul>
    <!-- 基金持仓 -->
    <ul class="fund-public-box">
      <div class="fund-public-header">
        <span>{{ $t("fundMessage.fundPulic") }}</span>
        <span @click="possessFund">
          {{ $t("fundMessage.checkDetails") }}
          <img src="@/assets/images/international/more.png" alt="" />
        </span>
      </div>
      <!-- 投资分布 -->
      <div class="invest-distribution">
        <div class="invest-title">{{ $t("fundMessage.investDistribute") }}</div>
        <div
          v-if="
            compositionsPie &&
            compositionsPie.assets &&
            compositionsPie.assets.length
          "
          class="invest-chart"
        >
          <investPie :compositionsPie="compositionsPie"></investPie>
        </div>
        <div v-else class="invest-chart-height1">
          <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
          <div>{{ $t("fundMessage.notMessage") }}</div>
        </div>
      </div>
      <!-- 十大持仓 -->
      <div class="invest-distribution">
        <div class="invest-title">{{ $t("fundMessage.entrepotNumber") }}</div>
        <div
          v-if="
            compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length
          "
          :class="!investDetails ? 'invest-chart-height2' : 'invest-chart-open'"
        >
          <holdPosition
            :chartTitle="false"
            :investDetails="investDetails"
            :compositionsPie="compositionsPie"
          ></holdPosition>
        </div>
        <div v-else class="invest-chart-height1">
          <!-- <img class="pic-empty" :src="require('@/assets/images/international/no_content.png')" alt="pic" /> -->
          <div>{{ $t("fundMessage.notMessage") }}</div>
        </div>
        <div
          class="unflod-chart"
          v-if="
            compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length > 5
          "
        >
          <span @click="investDetails = !investDetails">
            {{ !investDetails ? $t("fundMessage.checkText") : $t("fundMessage.checkClose") }}
          </span>
          <img v-if="!investDetails" src="@/assets/images/international/fold.png" alt="" />
          <img v-else src="@/assets/images/international/unfold.png" alt="" />
        </div>
      </div>
    </ul>
    <!-- 交易规则 -->
    <ul class="fund-public-box">
      <!--  :tradeRulesData="this.tradeRulesData" -->
      <deal-rule :tradeRulesDetails="tradeRulesData" v-if="tradeRulesData && JSON.stringify(tradeRulesData) !== '{}'" :ID="$route.query.fundIsin"></deal-rule>
    </ul>
    <!-- 基金档案 -->
    <ul class="fund-public-box">
      <fund-record :ID="urlDetail.fundIsin"></fund-record>
    </ul>
    <fundIntroduce class="margin-bottom-69"></fundIntroduce>
    <!-- 申购 / 赎回按钮 -->
    <!-- <div class="transaction-button" v-if="!isApp">
      <div class="apply" @click="goTotTansaction(1)">
        {{ $t("myAccount.apply") }}
      </div>
      <div class="sell" @click="goTotTansaction(0)">
        {{ $t("myAccount.redeem") }}
      </div>
      <div class="optional">
        <div @click="focus" class="img">
          <img
            :src="
              !isFocusOn
                ? require('@/assets/images/icon_optional_default.png')
                : require('@/assets/images/icon_optional_selected.png')
            "
            alt="pic"
          />
        </div>
        <div class="font">
          <span>{{ $t("myAccount.optional") }}</span>
        </div>
      </div>
      <div @click="share" class="optional">
        <div class="img">
          <img src="@/assets/images/icon_share.png" alt="pic" />
        </div>
        <div class="font">
          <span>{{ $t("myAccount.fundShare") }}</span>
        </div>
      </div>
    </div> -->
    <div class="dialog-main" v-show="isRisklevel">
      <div class="dialog">
        <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
        <div class="button-bottom-risk">
          <md-button type="default" round @click="isRisklevel = false">{{ $t("common.btns.cancel") }}</md-button>
          <md-button type="primary" round @click="toMeasure">{{ $t("myAccount.reEvaluation") }}</md-button>
        </div>
      </div>
    </div>
    <div class="dialog-main" v-show="isComplexProduct">
      <div class="dialog">
        <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
        <div class="button-bottom-risk">
          <md-button type="default" round @click="isComplexProduct = false">{{ $t("common.btns.cancel") }}</md-button>
          <md-button type="primary" round @click="toSuitabilityMatchingRecord">{{ $t("common.btns.examinecause") }}</md-button>
        </div>
      </div>
    </div>
    <!-- 底部button -->
    <div class="dialog-main" v-show="isComplexProductQualified">
      <div class="dialog">
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct1") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct2") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct3") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct4") }}
        </p>
        <div class="button-bottom-risk">
          <md-button type="default" round @click="isComplexProductQualified = false">{{ $t("common.btns.cancel") }}</md-button>
          <md-button type="primary" round @click="toBuy">{{ $t("myAccount.goOn") }}</md-button>
        </div>
      </div>
    </div>
    <!-- 风险测评 dialog -->
    <div class="dialog-main" v-if="ExpiredShow">
      <div class="dialog">
        <p class="content-describe">{{  expired_ExpiringSoon }}</p>
        <div class="button-bottom-risk button-bottom-risk-no-flex">
          <div @click="onBasicConfirm">{{ $t('common.SubmitQuestionnaire') }}</div>
          <div class="first-class" @click="onBasicCancel">{{ ExpiredBtn === 'later' ? this.$t('common.Later') : $t('common.btns.cancel') }}</div>
        </div>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import fundIntroduce from "@/components/international/footer/fund-introduce.vue";
import annual from "@/components/international/fundComponent/annual";
import historyPerformance from "@/components/international/fundComponent/history-performance";
import fundEarnings from "@/components/international/fundComponent/fund-earnings";
import performanceTrend from "@/components/international/fundComponent/performance-trend";
import historyNetworth from "@/components/international/fundComponent/history-networth";
import investPie from "@/components/international/fundChart/invest-pie.vue";
import holdPosition from "@/components/international/fundComponent/hold-position.vue";
import Loading from "@/components/international/loading/index";
import downloadApp from "@/components/international/downloadApp/index.vue"
import dealRule from "@/components/international/fundComponent/deal-rule.vue"
import fundRecord from "@/components/international/fundComponent/fund-record.vue"
import { riskLevel, bondType } from "@/utils/fund-type";
import { fundProductFun, queryAllCodes, fundCompositions, tradeRules } from "@/services/fund";
import moment from "moment";
import {
  getAccountPosition,
  fundFocus,
  getAccountRiskLevel,
  cancelFundFocus,
  getAccountNumber,
  getOptionalList,
  getFundInformation,
  queryOptionsGroups,
  fundCashRiskAssessment
} from "@/services/account";
import { Toast } from "mand-mobile";
import { refreshAppToken } from "@/utils/auth";
import { delParam } from '@/utils/urlParams';
import { compareVersion } from '@/utils/util';
import { shareDark, shareLight } from '@/assets/base64'
export default {
  components: {
    fundIntroduce,
    annual,
    historyPerformance,
    fundEarnings,
    performanceTrend,
    historyNetworth,
    investPie,
    holdPosition,
    Loading,
    downloadApp,
    dealRule,
    fundRecord
  },
  data() {
    return {
      isApp: false,
      isComplexProductQualified: false,
      isComplexProduct: false,
      isFirst: false,
      isRisklevel: false,
      tradeAccountStatus: "",
      isSevenDays: false,
      optionalList: [],
      isFocusOn: false,
      urlDetail: {}, // 查询初始数据--传递过来的参数
      productCurrent: 1,
      typeTabs: [],
      componentName: "",
      investDetails: false,
      isLoading: true,
      productDetails: {},
      tradeRulesData: {},
      compositionsPie: {},
      showDialog: false,
      langType: "cn",
      isProfessionalInvestor: "", // 增加专业投资者校验 Y("Y", "是专业投资者"), N("N", "非专业投资者");
      showLadderCharge: false, // 阶梯式收费
      AccountNumber: {},
      isOptional: false,
      isOldShare: true,
      ExpiredBtn: '',
      ExpiredShow: false,
      riskFundIsin: "",
      expired_ExpiringSoon: '',
      contenrUri: ''
    };
  },
  computed: {
    ...mapState(["locale", "theme", "accountNumber", "klineTheme"]),
  },
  created() {
    // 初始化查询登录状态  如果登陆了 查询自选状态   没有没在自选
    // 首次投资额后台给
    this.urlDetail = this.$route.query;
    fundCompositions(this.$route.query.fundIsin, "").then((res) => {
      // 基金持仓
      if (res.code == 200) {
        this.compositionsPie = res.data;
      }
    });
    this.$jsBridge.run("getLoginState", {
      callback: ({ loginStatus }) => {
        if (loginStatus) {
          this.getFundOptionalList();
        } else {
          this.isFocusOn = false;
        }
      },
    });
  },
  beforeMount() {
    // this.$jsBridge.run('getAppInfo', {
    //   callback: ({ appVersion }) => {
    //     // this.isOldShare = appVersion && !compareVersion(appVersion, '3.18.0');
    //     // !this.isOldShare && 
    //     this.$setNavBar.config({
    //       right: [{
    //         showType: 'img',
    //         img: this.theme === 'dark' ? shareDark : shareLight,
    //         callback: () => {
    //           this.share()
    //         }
    //       }]
    //     })
    //   }
    // })
  },
  async mounted() {
    await this.getTradeRules() 
    this.langType =
      this.locale === "zh-hans"
        ? "cn"
        : this.locale === "zh-hant"
        ? "hk"
        : "us";
    if (this.$jsBridge.isSupported("startEdWeb")) {
      this.isApp = false;
    } else {
      this.isApp = true;
    }

    this.componentName = "annual";
    // this.productDetailsFun()

    if (this.urlDetail.currentType == "MONEY_MARKET") {
      this.typeTabs = this.$t("fundMessage.interTypeTabs");
    } else {
      this.typeTabs = this.$t("fundMessage.typeTabs1");
    }
    // 默认组件
    this.componentName = "annual";
    this.urlDetail = this.$route.query;
    this.productDetailsFun();
    setTimeout(() => {
      this.isLoading = false;
      if (sessionStorage.getItem("access_token")) {
        queryAllCodes().then((res => {
          if (res.code == 200) {
            this.isOptional = res.data && res.data.length && res.data.includes(this.$route.query.fundIsin)
          }
        }))
      }
    }, 500);
  },

  methods: {
    getTradeRules() {
      tradeRules(this.$route.query.fundIsin).then((data) => {
        console.log('2---->', 2)
        // 交易规则
        if (data && data.code == '200') {
          this.tradeRulesData = data.data;
          if (data.data.firstInvestment === false) {
            this.isFirst = false;
          } else {
            this.isFirst = true;
          }
        }
      });
    },
    handleGoRiskInfo() {
      this.$router.push({
        path: "/international/riskLevelInfo",
      });
    },
    addOptional(data) { // 添加自选
      this.$jsBridge.run("getLoginState", {
        callback: ({ loginStatus }) => {
          if (loginStatus) { // already logged in
            this.focusReset();
          } else { // go to login in
            this.loginFund();
          }
        },
      });
    },
    toBuy() {
      this.isComplexProductQualified = false;
      this.routerRun(1);
    },
    toSuitabilityMatchingRecord() {
      this.isComplexProduct = false;
      this.$router.push({
        path: "/international/suitabilityMatchingRecord",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    toMeasure() {
      this.isRisklevel = false;
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/risk/assessment?nav=0`,
        callback: () => {},
      });
    },
    getPositionInfo() {
      // 交易规则
      tradeRules(this.$route.query.fundIsin).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          if (res.data.firstInvestment === false) {
            this.isFirst = false;
          } else {
            this.isFirst = true;
          }
        }
      });
    },
    showLodder () {
      if (this.tradeRulesData.subscriptionRatePreferential && this.tradeRulesData.subscriptionRatePreferential.length > 0 && this.tradeRulesData.subscriptionRatePreferential[0].rate) {
        return true
      }else {
        return false
      }
    },
    share() {
      const { href, origin } = window.location;
      const { name } = this.productDetails;
      let url = delParam({ paramKey: ['access_token', 'source', 'pageType', 'shareId', 'shareChannel', 'shareType'] });
      url += `${url.includes('?') ? '&' : '?'}pageType=fundDetail`;

      !this.isOldShare
        ? this.$jsBridge.run('startShareUrlAndPicture', {
            type: 'C',
            title: this.$t('fundMessage.shareTitle'),
            content: name && name[this.langType] ? name[this.langType] : '-',
            imageUrl: origin + '/logo-share.png',
            url,
            contentType: '基金详情页',
            pageType: 'fundDetail',
            callback: body => {
              this.$sensor.common.contentShare({
                content_type: '基金详情页',
                content_title: this.$t('router.productDetails'),
                activity_url: href,
                share_channel: body.channel
              })
            },
          })
        : this.$jsBridge.run("startShareUrl", {
            title: this.$t('fundMessage.shareTitle'),
            content: name && name[this.langType] ? name[this.langType] : '-',
            imageUrl: origin + '/logo-share.png',
            url,
            contentType: '基金详情页',
            pageType: 'fundDetail',
            callback: body => {
              this.$sensor.common.contentShare({
                content_type: '基金详情页',
                content_title: this.$t('router.productDetails'),
                activity_url: href,
                share_channel: body.channel
              })
            },
          });
    },
    // h5掉用该方法传入的参数如{callback:"**"} 原生会以callBack值为js的回调方法将登陆状态值返回给h5

    showTermDescribe(flag) {
      if (flag === "seven") {
        this.isSevenDays = true;
      } else {
        this.isSevenDays = false;
      }
      this.showDialog = true;
    },
    getFundOptionalList() {
      // let object = {
      //   marketTab: "FUND",
      // };
      getOptionalList({}, this).then((res) => {
        this.optionalList = res.data.datas;
        if (!this.optionalList.length) {
          this.isFocusOn = false;
        } else {
          this.isFocusOn = false;
          this.optionalList.forEach((item) => {
            if (item.code === this.$route.query.fundIsin) {
              this.isFocusOn = true;
            }
          });
        }
      });
    },
    focusReset() {
      // 国际版--改版后: 添加&删除自选直接调用app Api即可
      this.$jsBridge.run("addUserOptional", {
        code: this.$route.query.fundIsin,
        exchangeType: this.productDetails.assetClassType,
        marketType: "FUND",
        name: this.productDetails.name.cn || this.productDetails.name.hk || this.productDetails.name.us,
        callback: (res => {
          // console.log('addUserOptional-----', res);
          if (res.status === 'success') {
            queryOptionsGroups(this.$route.query.fundIsin).then(res1 => {
              if (res1.code === 200) {
                let newItem = res1.data && res1.data.length && res1.data.some(item => {
                  return item.addStatus === 'Added'
                })
                if (newItem) {
                  this.isOptional = true
                }else {
                  this.isOptional = false
                }
              }
            })
            // this.isOptional = !this.isOptional
          }
        })
      })
    },
    checkType(data) {
      this.productCurrent = data && data.name;
      switch (
        this.productCurrent // 更新组件
      ) {
        case 1:
          this.componentName = "annual";
          break;
        case 2:
          this.componentName = "historyPerformance";
          break;
        case 3:
          this.componentName =
            this.urlDetail.currentType == "MONEY_MARKET"
              ? "fundEarnings"
              : "historyNetworth";
          break;
        default:
          break;
      }
    },
    productDetailsFun() {
      // 产品详情
      fundProductFun(this.urlDetail.fundIsin, "").then((res) => {
        if (res.code == 200) {
          this.productDetails = res.data;
        }
      });
    },
    stringToArr(data) {
      if (data) {
        let createArr = data.split("-");
        createArr.shift();
        return createArr.join("-");
      }
    },
    bondType(type, data) {
      // // 基金类型
      let newText = "";
      newText = bondType(type, data);
      return newText;
    },
    riskLevel(type, data) {
      // 基金风险等级
      let newText = "";
      newText = riskLevel(type, data);
      return newText;
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data.name && data.name.cn ? data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        return data.name && data.name.hk ? data.name.hk : "-";
      } else {
        return data.name && data.name.us ? data.name.us : "-";
      }
    },
    closeDetails(e) {
      this.investDetails = e;
    },
    possessFund() {
      this.$router.push({
        path: "/international/fundPossess",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    showFundRecord() {
      // 进入基金档案
      this.$router.push({
        path: "/international/fundRecord",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    // 申购赎回前判定当前基金是否支持交易 判断登录是否  先判断是否登录  在判断当前基金是否支持交易
    goTotTansaction(flag) {
      // 判断登陆状态
      this.$jsBridge.run("getLoginState", {
        callback: ({ loginStatus }) => {
          if (loginStatus) {
            // 判断开户状态
            getAccountNumber(this).then((res) => {
              this.AccountNumber = res.data.user;
              this.isProfessionalInvestor = res.data.user.isProfessionalInvestor;
              if (
                res.data.tradingAccountList &&
                res.data.tradingAccountList.length
              ) {
                let object = res.data.tradingAccountList.find((item) => {
                  return item.type === "FUND";
                });
                if (object && object.tradeAccountNumber) {
                  // 有开过基金账户
                  /*
                    风险测评日期有效状态 --> 风险测评结果已过期，则不允许用户申购
                    （1）如有登录校验、基金开户校验，则放在登录校验、基金开户校验后面。
                    （2）如没有登录校验、基金开户校验，则放在第一步校验
                  */
                  fundCashRiskAssessment(object.tradeAccountNumber).then(riskStatus => {
                    this.tradeAccountStatus = object.status;
                    this.$store.commit("setAccountNumber", object.tradeAccountNumber);
                    if (riskStatus.data.assessmentStatus === 'Expired') {
                      this.ExpiredShow = true
                      this.ExpiredBtn = 'cancel'
                      this.expired_ExpiringSoon = this.$t('common.Expired')
                      return
                    } else if (riskStatus.data.assessmentStatus === 'ExpiringSoon') {
                      this.ExpiredShow = true
                      this.ExpiredBtn = 'later'
                      this.expired_ExpiringSoon = this.$t('common.ExpiringSoon', { time: moment(riskStatus.data.expiryDate).format("YYYY-MM-DD") })
                      return
                    } else { // 已登录 && 已开户 && 风险测评未过期
                      this.accountAttribute(flag)
                    }
                  })
                  
                } else {
                  // 有开过户 没有开基金账户
                  this.$jsBridge.run("toPage", {
                    jumpType: "NATIVE",
                    loginState: "JUDGMENT",
                    openAccountState: "JUDGMENT_FUND",
                    navigationContentCode: "FUND_ACCOUNT",
                    navigationUri: "FUND_ACCOUNT",
                    titleDisplay: "DISPALY",
                  });
                }
              } else {
                // 什么户都没开
                this.$jsBridge.run("toPage", {
                  jumpType: "NATIVE",
                  loginState: "JUDGMENT",
                  openAccountState: "JUDGMENT",
                  navigationContentCode: "FUND_ACCOUNT",
                  navigationUri: "FUND_ACCOUNT",
                  titleDisplay: "DISPALY",
                });
              }
            });
          } else {
            this.loginFund();
          }
        },
      });
    },
    accountAttribute(flag) {
      // 判定当前基金账户是否可用
      if (this.tradeAccountStatus != "OPEN") {
        Toast.info(this.$t("myAccount.abnormalAccount"));
        return;
      }
      if (flag === 0) {
        this.routerRun(flag);
      } else {     
        // 用户国籍是否在基金可申购地区范围
        if (this.productDetails.supportRegions && this.productDetails.supportRegions.length > 0) {
          if (!this.productDetails.supportRegions.includes(this.AccountNumber.issuingCountry) && flag) {
            Toast.info(this.$t("myAccount.regionIsNotSupported"));
            return;
          }
        }
        /**
         * @params
         * professionalInvestorFlag: true (仅限专业投资者), false (不做专业投资者限制)
         * isProfessionalInvestor: 'Y' (当前账户为专业投资者), 'N' (当前账户为非专业投资者)
         */
        // 该基金是否专业投资者才能申购
        if (this.isProfessionalInvestor !== 'Y' && this.productDetails.professionalInvestorFlag && flag) {
          Toast.info(this.$t("myAccount.specialtyInvestor1"));
          return;
        }
        // 判断是否是复杂产品
        getFundInformation(this.urlDetail.fundIsin).then((res) => {
          if (res.data.complexProductFlag) {
            // 复杂产品流程
            this.judgeTheRiskLevelComplex(flag);
          } else {
            this.testRouterRun(flag);
          }
        });
      }
    },
    
    onBasicConfirm() { // 跳转风险评测
      this.ExpiredShow = false
      // this.$router.push({
      //   path: "/international/risk/tips"
      // })
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/international/risk/tips?nav=0&isExpired=yes`,
        callback: () => {},
      });
    },
    onBasicCancel() { // 跳转基金申购页面
      console.log('取消按钮');
      this.ExpiredShow = false
      if (this.ExpiredBtn === 'later') {
        this.accountAttribute(1)
      }
    },

    loginFund() {
      this.$jsBridge.run("startLogin", {
        type: "1A",
        callback: ({ login_state }) => {
          if (login_state) {
            this.$jsBridge.run("generateNewToken", {
              callback: ({ accessToken }) => {
                sessionStorage.setItem(
                  "access_token",
                  accessToken ? `Bearer ${accessToken}` : ""
                );
                this.getPositionInfo();
                this.getFundOptionalList();
              },
            });
          }
        },
      });
    },
    judgeTheRiskLevelComplex(flag) {
      // 判断当前账号风险等级是否可以购买当前基金
      getAccountRiskLevel(this).then((res) => {
        if (
          this.productDetails.riskLevel <= Number(res.data.result[1]) &&
          res.data.isDerivative
        ) {
          this.isComplexProductQualified = true;
          return;
        } else {
          this.isComplexProduct = true;
        }
      });
    },
    testRouterRun(flag) {
      getAccountRiskLevel(this).then((res) => {
        if (
          Number(res.data.result[1]) < Number(this.productDetails.riskLevel) &&
          flag === 1
        ) {
          this.isRisklevel = true;
          return;
        }
        this.routerRun(flag);
      });
    },
    routerRun(flag) {
      if (flag && !this.tradeRulesData.buyFlag) {
        Toast.info(this.$t("myAccount.subscriptionIsNotSupported"));
        return;
      } else if (!flag && !this.tradeRulesData.sellFlag) {
        Toast.info(this.$t("myAccount.redemptionIsNotSupported"));
        return;
      }
      let parameter = {
        isRouteBuy: false,
        fundIsin: this.urlDetail.fundIsin,
        isProfessionalInvestor: this.isProfessionalInvestor || "N",
        professionalInvestorFlag: this.productDetails.professionalInvestorFlag
      };
      if (flag) {
        parameter.isRouteBuy = true;
        this.$router.push({
          path: "/international/transactionBuy",
          query: parameter,
        });
      } else {
        getAccountPosition(this.accountNumber, this.urlDetail.fundIsin).then(
          (res) => {
            if (res.code == 200) {
              parameter.quantity = res.data.availableQuantity;
              if (parameter.quantity > 0) {
                this.$router.push({
                  path: "/international/transactionSell",
                  query: parameter,
                });
              } else {
                Toast.info(this.$t("myAccount.noPsition"));
                return;
              }
            }
          }
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.product-detail-box {
  min-height: 100vh;
  overflow: auto;
  // 禁止页面文本可以选中
  moz-user-select: -moz-none;
  -moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: var(--gray_05);
  .download-app-box {
    height: 108px;
  }
  .product-detail-header {
    padding: 40px 30px;
    text-align: center;
    background: var(--background);
    .product-detail-title {
      color: var(--text_1st);
      font-size: 36px;
      font-weight: 500;
      word-wrap: break-word; // 数字或英文字母换行
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .product-detail-number {
      font-size: 22px;
      margin: 20px 0 17px;
      color: var(--text_3rd)
    }
    .product-detail-type {
      color: var(--brand_01);
      font-size: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      span {
        background: var(--text_3rd_opacity);
        margin-right: 20px;
        margin-bottom: 20px;
        padding: 4px 12px;
        border-radius: 6px;
        color: var(--text_3rd);
      }
      .last-span-box {
        display: flex;
        align-items: center;
        margin-right: 0;
        color: var(--warning);
        background: var(--warning_opacity);
        img {
          width: 14px;
          height: 16px;
        }
      }
    }
    .product-detail-ratio-netValue {
      margin-top: 13px;
      .product-detail-fund-data {
        display: flex;
        justify-content: space-around;
        align-items: center;
        .product-detail-ratio,
        .product-detail-netValue {
          height: 98px;
          // margin-right: 49px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          color: var(--text_1st);
          &:last-child {
            font-size: 24px;
          }
          p {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          img {
            width: 36px;
          }
          .fund-up-value {
            color: var(--red);
          }
          .fund-drop-value {
            color: var(--green);
          }
          .fund-drop-value1 {
            color: var(--text_3rd);
          }
        }
        .product-detail-ratio {
          p {
            &:first-child {
              font-size: 46px;
            }
          }
        }
        .product-detail-netValue {
          p {
            &:first-child {
              font-size: 46px;
            }
          }
          .last-net-value {
            color: var(--text_1st);
          }
        }
      }
      .product-button-box {
        .product-apply-redeem {
          display: flex;
          justify-content: space-around;
          align-items: center;
          margin: 52px 0 26px;
        }
        .product-add-optional {
          text-align: center;
          color: var(--brand_01);
          font-size: 24px;
          span {
            border-bottom: 1px solid var(--brand_01);
          }
          .delete-optional {
            color: var(--text_2nd);
            border-bottom: 1px solid var(--text_2nd);
          }
        }
      }
    }
  }
  .dialog-main {
    background: var(--mask);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog {
      width: 70%;
      max-width: 550px;
      padding: 48px 40px;
      border-radius: 30px;
      font-size: 28px;
      color: var(--text_1st);
      background: var(--background);
      position: relative;
      .content-describe {
        text-align: center;
        line-height: 45px;
      }
      .button-bottom-risk {
        margin-top: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        div {
          border-radius: 40px;
          width: 220px;
          height: 80px;
          line-height: 80px;
          background-color: var(--brand_01);
          color: var(--text_5th);
          border: 1px solid var(--brand_01);
          text-align: center;
        }
        .first-class {
          background-color: var(--background);
          color: var(--text_1st);
          border: 1px solid var(--line_01);
        }
      }
      // .button-bottom {
      //   width: 100%;
      //   margin-top: 40px;
      // }
      .button-bottom-risk-no-flex {
        display: block;
        div {
          width: 100%;
        }
        .first-class {
          border: 0;
          text-decoration: underline;
          text-decoration-color: var(--text_1st);
          margin-top: 20px;
        }
      }
    }
  }
  .product-type-select {
    margin-top: 20px;
    background: var(--background);
    color: var(--text_3rd);
    .product-type-tab {
      padding: 22px 30px 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28px;
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .product-components {
      padding: 20px 30px 0;
      // height: 620px;
    }
  }
  .fund-public-box {
    margin-top: 20px;
    padding: 0 30px;
    background: var(--background);
    color: var(--text_1st);
    .fund-public-header {
      padding: 28px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        &:first-child {
          font-size: 34px;
          font-weight: bold;
        }
        &:last-child {
          color: var(--text_1st);
          display: flex;
          align-items: center;
          img {
            width: 36px;
            margin-top: 5px;
            margin-left: 10px;
          }
        }
      }
    }
    .invest-distribution {
      padding: 20px 0;
      // height: 398px;
      // border-bottom: 1px solid;
      // &:last-child {
      //   border-bottom: 0;
      // }
      // @include border_color(line-color);
      .invest-title {
        font-size: 34px;
        font-weight: bold;
        color: var(--text_1st);
      }
      .invest-chart {
        overflow: hidden;
      }
      .invest-chart-height1 {
        // padding: 44px 0px 40px 13px;
        padding: 230px 0 256px;
        width: 100%;
        text-align: center;
        .pic-empty {
          width: 331px;
          // height: 187px;
        }
        div {
          @include font_color(second-text);
          font-size: 28px;
          height: 72px;
          line-height: 72px;
          text-align: center;
        }
      }
      .invest-chart-height {
        height: 280px;
      }
      .invest-chart-height2 {
        // height: 350px;
        overflow: hidden;
        height: 540px;
      }
      // .invest-chart-open {
      //   height: 780px;
      // }
      .unflod-chart {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        color: var(--text_1st);
        img {
          width: 27px;
          height: 27px;
          margin-left: 9px;
        }
      }
    }
    .fund-public-label {
      padding: 40px 0;
      @include font_color(text-color);
      div {
        padding-top: 34px;
        display: flex;
        &:first-child {
          padding-top: 0;
        }
        label {
          flex: 0 0 150px;
        }
        span {
          flex: 1;
          text-align: right;
          overflow: hidden;
          word-wrap: break-word;
        }
      }
    }
  }
  .margin-bottom-69 {
    padding-bottom: 140px;
  }
  .transaction-button {
    position: fixed;
    bottom: 0px;
    left: 0px;
    @include themeify {
      background: themed("bg-color");
      border-top-color: themed("line-color");
    }
    width: 100%;
    border-top: 1px solid;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 40px;
    .apply,
    .sell {
      width: 225px;
      line-height: 100px;
      font-size: 28px;
      color: #fff;
      text-align: center;
    }
    .apply {
      background-color: #2d60e0;
    }
    .sell {
      background-color: #ec8a01;
    }
    .optional {
      @include themeify {
        color: themed("second-text");
      }
      width: 150px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .img {
        padding: 3px 0px;
        img {
          width: 36px;
          height: 36px;
        }
      }
      .font {
        span {
          line-height: 28px;
          font-size: 22px;
        }
      }
    }
  }
}
</style>
