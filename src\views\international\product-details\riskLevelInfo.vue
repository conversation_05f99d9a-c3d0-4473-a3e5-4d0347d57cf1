<template>
  <div class="risk-level-info" v-html="contenrhtml"></div>
</template>

<script>
import { getRiskLevelInfo, getHtml } from "@/services/fund";
export default {
  data() {
    return {
      detail: '',
      contenrhtml: ''
    }
  },
  mounted() {
    this.getRiskLevelInfo();
  },
  methods: {
    getHtml(link) {
      if (!link) {
        return
      }
      let t = new Date().getTime()
      getHtml(link + `?t=${t}`).then(res => {
        let arr = res && res.data.split('<body>') || []
        let index = arr.length ? arr.length - 1 : 0
        let urihtml = arr[index] && arr[index].split('</body>')[0]
        this.contenrhtml = urihtml
      })
    },
    // 获取接口详情
    getRiskLevelInfo() {
      let params = {
        nounName: 'FUND_RPQ',
        equipmentType: 'APP',
        jumpType: 'H5',
      };
      getRiskLevelInfo(params).then((res) => {
        if(!res.data) return
        const data = res.data;
        this.getHtml(data?.[0]?.contenrUri)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.risk-level-info {
  padding: 20px;
  ::v-deep table {
    width: 100% !important;
  }
}


</style>
