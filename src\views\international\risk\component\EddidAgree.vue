<template>
  <div class="eddid-agree">
    <div class="agree-icon-wrap">
      <img
        @click="onChange"
        :src="require(`@/assets/images/international/${!value ? 'multi_select_default' : 'multi_select_selected'}.png`)"
        class="agree-icon"
      />
    </div>
    <slot></slot>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'InterEddidAgree',
  props: {
    iconType: {
      default: 'square',
      validator: value => ['square', 'circular'].includes(value)
    },
    value: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    onChange() {
      const newVal = !this.value;
      this.$emit('input', newVal);
    },
  }
}
</script>

<style lang="scss" scoped>
.eddid-agree {
  width: 100%;
  display: flex;
  align-items: flex-start;
  color: #121c32;
  font-size: 28px;
  line-height: 44px;
}
.agree-icon-wrap {
  padding: 6px 22px 0 0;
  line-height: 44px;
}
.agree-icon {
  width: 32px;
  height: 32px;
}
</style>