<template>
  <div class="eddid-input-item">
    <div class="eddid-input-item-content">
      <div class="eddid-input-item-control">
        <input
          v-model="inputValue"
          :maxlength="maxlength"
          :placeholder="placeholder"
          readonly
          type="text"
          autocomplete="off"
          class="eddid-input-item-input"
          @blur="onBlur"
          @input="onInput"
        />
      </div>
    </div>
    <div v-show="$attrs.errors.has($attrs['data-vv-name'])" class="md-field-item-children">
      <div class="md-input-item-msg">{{ $attrs.errors.first($attrs['data-vv-name']) }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EddidInput',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    maxlength: {
      type: Number,
      default: 0,
    },
    readonly: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      inputValue: '',
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        if(newVal === oldVal) return;

        this.inputValue = newVal;
      },
    }
  },
  methods: {
    onInput(e) {
      this.$emit('input', e.target.value);
    },
    onBlur() {
      let tempVal = this.inputValue ? this.inputValue.trim() : '';
      this.inputValue = tempVal;
      this.$emit('input', tempVal);
    }
  },
}
</script>

<style lang="scss" scoped>
.eddid-input-item {
  .eddid-input-item-content {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 0.8rem;
    padding: 0;
    box-sizing: border-box;

    &::before {
      content: '';
      z-index: 0;
      position: absolute;
      left: 0;
      top: 0;
      display: block;
      width: 100%;
      height: 100%;
      border-bottom: 1px solid var(--line_01);
      transform-origin: top left;
    }
  }

  .eddid-input-item-control {
    width: 100%;
  }

  .eddid-input-item-input {
    background: transparent;
    width: 100%;
    height: 100%;
    color: var(--text_1st);
    font-size: 30px;
    font-weight: 400;
    line-height: 1;
    border: none;
    outline: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    appearance: none;
  }

  input::-webkit-input-placeholder{
    font-size: 30px;
  }
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .eddid-input-item .eddid-input-item-content::before {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .eddid-input-item .eddid-input-item-content::before {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}
</style>