<template>
  <ul class="employ-list">
    <li
      v-for="item in selectList"
      :key="item.value"
      @click.stop="onChange(item)"
      :class="[innerValue && innerValue.value === item.value ? 'selected' : '']"
    >{{ item.label }}</li>
  </ul>
</template>

<script>
export default {
  name: 'EddidSelect',
  props: {
    value: {
      type: Object,
      default: () => null,
    },
    selectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      innerValue: null,
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        if(newVal === oldVal) return;

        this.innerValue = newVal;
      },
    }
  },
  methods: {
    onChange (row) {
      this.$emit('change', row);
    },
  },
}
</script>

<style lang="scss" scoped>
.employ-list {
  li {
    background: var(--brand_06);
    padding: 36px 30px;
    color: var(--text_1st);
    font-size: 28px;
    line-height: 40px;
    border-radius: 20px;
  }

  li + li {
    margin-top: 32px;
  }

  .selected {
    position: relative;
    color: var(--brand_01);

    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      border: 1px solid var(--brand_01);
      border-radius: 20px;
      transform: scale(1);
      transform-origin: 0 0;
      pointer-events: none;
    }
  }
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .employ-list .selected::after {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .employ-list .selected::after {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}
</style>
