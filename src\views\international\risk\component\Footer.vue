<template>
  <div class="inter-risk-footer">
    <slot></slot>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.inter-risk-footer {
  background-color: var(--background);
  position: fixed !important;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px 32px 56px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border-top: 1px solid var(--line_01);
    transform: scale(1);
    transform-origin: 0 0;
    pointer-events: none;
  }

  & .md-button {
    border-radius: 48px;

    &::after {
      content: none;
    }

    &.primary,
    &.primary:not(.plain) {
      background-color: var(--brand_01);
    }

    &.default::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      border: 1px solid var(--gray_02);
      border-radius: 48px;
      transform: scale(1);
      transform-origin: 0 0;
      pointer-events: none;
    }

    & .md-button-content {
      font-size: 30px;
      line-height: 46px;
    }

    &.primary .md-button-content {
      color: var(--text_5th);
    }
    &.default .md-button-content {
      color: var(--text_1st);
    }
  }
}

.international .inter-risk-footer .md-button.default {
  border: none;
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .inter-risk-footer::before {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .inter-risk-footer::before {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}
</style>