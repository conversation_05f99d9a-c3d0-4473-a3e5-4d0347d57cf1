<template>
  <div class="inter-question-card">
    <p class="question">{{ data.question }}</p>
    <ul @click="onClick">
      <li
        v-for="option in data.options || []"
        :key="`${data.questionNumber}-${option.value}`"
        :data-value="option.value"
        :data-single="option.single"
        :class="['option-item', getSelectedClass(option)]"
      >
        <div class="select-icon">
          <img
            :src="getImgStr(option)"
            :class="data.type === 'more' ? 'checkbox-icon' : 'radio-icon'"
          />
        </div>
        <div class="option-content">
          {{ option.value }}. {{ option.label }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'QuestionCard',
  props: {
    data: {
      type: Object,
    },
    value: {
      type: [String, Array],
    },
  },
  
  data() {
    return {
      innerVal: this.value,
      imageCache: {
        multi_select_default: null,
        multi_select_selected: null,
        multi_select_disable2: null,
        single_select_default: null,
        single_select_selected: null,
        single_select_disabled: null
      }
    }
  },
  computed:{
    isDisabled(){
      return  this.data.disabled
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal, oldVal) {
        if(newVal !== oldVal) {
          this.innerVal = newVal;
        }
      },
      immediate:true
    }
  },
  created() {
    // 预加载图片
    this.preloadImages();
  },
  methods: {
    getSelectedClass(option) {
      return this.isSelected(option) ? 'selected' : '';
    },
    preloadImages() {
      const imageNames = Object.keys(this.imageCache);
      imageNames.forEach(name => {
        this.imageCache[name] = require(`@/assets/images/international/${name}.png`);
      });
    },
    getImgStr(option) {
      if (this.data.type === 'more') {
        if (this.isDisabled && !this.isSelected(option)) {
          return this.imageCache.multi_select_disable2;
        }
        return this.imageCache[!this.isSelected(option) ? 'multi_select_default' : 'multi_select_selected'];
      }
      
      if (this.isDisabled && !this.isSelected(option)) {
        return this.imageCache.single_select_disabled;
      }
      return this.imageCache[!this.isSelected(option) ? 'single_select_default' : 'single_select_selected'];
    },
    onClick(e) {
      // 使用closest找到最近的li元素
      const target = e.target.closest('.option-item');
      if (!target) return;
      
      const current = target.getAttribute('data-value');
      
      const { type, questionNumber, options } = this.data;
      
      // 检查是否允许更改
      if (this.isDisabled) {
        return false;
      }
      
      // 优化值处理逻辑
      let tempVal;
      if (type === 'more') {
        const single = target.getAttribute('data-single') || false;
        if (single) {
          tempVal = [current];
        } else {
          tempVal = Array.isArray(this.value) ? [...this.value] : 
                (this.value ? [this.value] : []);
        
          const index = tempVal.indexOf(current);
          const selectedOptions = options.find((option)=>{
            return tempVal.includes(option.value) && option.single
          });
          if(selectedOptions){
            tempVal = [current];
          }else{
            if (index === -1) {
              tempVal.push(current);
            } else {
              tempVal.splice(index, 1);
            }
          }
        }
        // tempVal = Array.isArray(this.value) ? [...this.value] : 
        //         (this.value ? [this.value] : []);
        
        // const index = tempVal.indexOf(current);
        // if (index === -1) {
        //   tempVal.push(current);
        // } else {
        //   tempVal.splice(index, 1);
        // }
      } else {
        tempVal = this.value === current ? '' : current;
      }
      this.$emit('change', tempVal, questionNumber);
    },
    isSelected({ value }) {
      // 如果没有值或多选类型但数组为空，直接返回false
      if (!this.innerVal || (this.data.type === 'more' && Array.isArray(this.innerVal) && !this.innerVal.length)) {
        return false;
      }
      
      // 处理多选类型
      if (this.data.type === 'more') {
        // 确保innerVal是数组
        const valueArray = Array.isArray(this.innerVal) ? this.innerVal : [this.innerVal];
        return valueArray.includes(value);
      }
      
      // 处理单选类型
      return value === this.innerVal;
    },
  },
}
</script>

<style lang="scss" scoped>
.inter-question-card {
  width: 100%;

  ul {
    width: 100%;
  }
}
.question {
  margin-bottom: 40px;
  color: var(--text_1st);
  font-size: 30px;
  line-height: 46px;
  font-weight: 400;
}
.option-item {
  position: relative;
  background-color: var(--brand_06);
  width: 100%;
  padding: 36px 30px;
  color: var(--text_1st);
  border-radius: 20px;
  display: flex;
  align-items: center;
  will-change: transform, color; /* 提示浏览器优化渲染 */
  transition: color 0.15s ease; /* 添加平滑过渡 */
  
  & + & {
    margin-top: 32px;
  }
}
.select-icon {
  margin-right: 20px;
  flex-shrink: 0;
  
  .checkbox-icon,
  .radio-icon {
    width: 40px;
    height: 40px;
    vertical-align: middle;
  }
}
.option-content {
  flex: 1;
}
.selected {
  color: var(--brand_01);

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border: 1px solid var(--brand_01);
    border-radius: 20px;
    transform: scale(1);
    transform-origin: 0 0;
    pointer-events: none;
  }
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .selected::before {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .selected::before {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}
</style>