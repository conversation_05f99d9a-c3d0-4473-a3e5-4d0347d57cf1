<template>
  <div class="inter-question-card">
    <p class="question">{{ data.question }}</p>
    <ul @click="onClick">
      <li
        v-for="option in data.options || []"
        :key="`${data.questionNumber}-${option.value}`"
        :data-value="option.value"
        :class="['option-item', isSelected(option) ? 'selected' : '']"
      >
        {{ option.value }}. {{ option.label }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'QuestionCard',
  props: {
    data: {
      type: Object,
    },
    value: {
      type: [String, Array],
    },
    allowChange: {
      type: Boolean,
      default: true
    },
    allowAge: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      innerVal: this.value,
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal, oldVal) {
        if(newVal !== oldVal) {
          this.innerVal = newVal;
        }
      },
      immediate:true
    }
  },
  methods: {
    onClick(e) {
      if (e.target && e.target.className.includes('option-item')) {
        const current = e.target.getAttribute('data-value');
        const { type, questionNumber, answer  } = this.data;
        if (!this.allowChange && questionNumber===3) {
          return false
        }
        if (!this.allowAge && questionNumber === 1 && answer) {
          return false
        }
        let tempVal;
        if (type === 'more') {
          tempVal = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
          const index = tempVal.findIndex((item) => item === current);
          index === -1 ? tempVal.push(current) : tempVal.splice(index, 1);
        } else {
          tempVal = this.value === current ? '' : current;
        }

        this.$emit('change', tempVal, questionNumber)
      }
    },
    isSelected({ value }) {
      if (!this.innerVal || (this.data.type === 'more' && !this.innerVal.length)) return false;
      return this.data.type === 'more'
        ? (Array.isArray(this.innerVal) ? this.innerVal : [this.innerVal]).includes(value)
        : value === this.innerVal;
    },
  },
}
</script>

<style lang="scss" scoped>
.inter-question-card {
  width: 100%;

  ul {
    width: 100%;
  }
}
.question {
  margin-bottom: 40px;
  color: var(--text_1st);
  font-size: 30px;
  line-height: 46px;
  font-weight: 400;
}
.option-item {
  position: relative;
  background-color: var(--brand_06);
  width: 100%;
  padding: 36px 30px;
  color: var(--text_1st);
  border-radius: 20px;

  & + & {
    margin-top: 32px;
  }
}
.selected {
  color: var(--brand_01);

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border: 1px solid var(--brand_01);
    border-radius: 20px;
    transform: scale(1);
    transform-origin: 0 0;
    pointer-events: none;
  }
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .selected::before {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .selected::before {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}
</style>