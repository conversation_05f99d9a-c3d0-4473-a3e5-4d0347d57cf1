<template>
  <div class="tip-card">
    <div class="title">{{ title }}</div>
    <div class="description">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.tip-card {
  & + & {
    margin-top: 32px;
  }
}
.title {
  margin-bottom: 12px;
  color: var(--text_1st);
  font-size: 32px;
  line-height: 48px;
  font-weight: 500;
  font-family: PingFangSC-Medium, PingFang SC, sans-serif;
}
.description {
  background-color: var(--gray_05);
  padding: 24px;
  color: var(--text_3rd);
  line-height: 36px;
  font-size: 24px;
  font-weight: 400;
}
</style>