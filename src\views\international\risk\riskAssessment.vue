<template>
  <div class="inter-container">
    <nav-header :back="onBack"/>
    <div class="mgb30">
      <label class="label">{{ $t('risk.username') }}</label>
      <eddid-input
        v-model="name"
        :maxlength="20"
        :placeholder="$t('risk.placeholderUsername')"
        data-vv-name="riskUsername"
        v-validate="'required|compareUsername'"
        data-vv-validate-on="input"
        :errors="this.$validator.errors"
      />
    </div>
    <div class="mgb30">
      <label class="label">{{ $t('risk.idCard') }}</label>
      <eddid-input
        v-model="idCard"
        :maxlength="20"
        :placeholder="$t('risk.placeholderIDCard')"
        data-vv-name="riskIDCard"
        v-validate="'required|compareIDCard'"
        data-vv-validate-on="input"
        :errors="this.$validator.errors"
      />
    </div>

    <risk-footer>
      <md-button @click="onNext" type="primary">{{ $t('common.btns.next') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from '@/components/international/NavHeader.vue'
import EddidInput from './component/EddidInput.vue'
import RiskFooter from './component/Footer.vue'
import { mapState } from 'vuex'
import { Validator } from 'vee-validate'
import { getQuestionList } from '@/services/risk'

export default {
  name: 'interRiskAssessment',
  components: { NavHeader, EddidInput, RiskFooter },
  data() {
    return {
      validateUserInfo: null, // 证券开户的用户信息
      name: '',
      idCard: '',
      selectedInfo: null,
      isComplexProduct: 0
    }
  },
  computed: {
    ...mapState(['theme']),
  },
  beforeMount() {
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }

    this.getQuestionList()
    Validator.extend('compareUsername', {
      getMessage: () => this.$t('risk.validatorName'),
      validate: (value) => {
        if (value && this.validateUserInfo && this.validateUserInfo.name) {
          return value === this.validateUserInfo.name
        }
        return true
      },
    })
    Validator.extend('compareIDCard', {
      getMessage: () => this.$t('risk.validatorIDCard'),
      validate: (value) => {
        if (value && this.validateUserInfo && this.validateUserInfo.idNumber) {
          return value === this.validateUserInfo.idNumber
        }
        return true
      },
    })
  },
  methods: {
    onNext() {
      this.$validator.validate().then((success) => {
        if (!success) return false

        let riskModelData = sessionStorage.getItem('riskModel')
        riskModelData = riskModelData ? JSON.parse(riskModelData) : {}
        if (this.selectedInfo && Object.values(this.selectedInfo).filter((i) => !!i).length) {
          riskModelData.selectedInfo = this.selectedInfo
        }
        sessionStorage.setItem('riskModel', JSON.stringify(riskModelData))

        if(this.isComplexProduct === '1') {
          this.$router.push({path:'/international/risk/questions/1',query: { isComplexProduct: this.isComplexProduct }})
          return
        }
        this.$router.push(`/international/risk/questions/1`)
      })
    },
    onBack() {
      if(this.isComplexProduct === '1') {
        this.$jsBridge.isSupported('navBack')
          ? this.$jsBridge.run('navBack', { isClosePage: true })
          : this.$router.push('/international')
        return
      }

      this.$jsBridge.isSupported('navBack')
        ? this.$jsBridge.run('navBack', { isClosePage: false })
        : this.$router.go(-1)
    },
    getQuestionList() {
      this.$loading.show()
      getQuestionList()
        .then((res) => {
          if (!res || `${res.code}` !== '200') return
          const { user } = res.data
          this.validateUserInfo = user
          this.name = user && user.name ? user.name : ''
          this.idCard = user && user.idNumber ? user.idNumber : ''
          let riskModelData = sessionStorage.getItem('riskModel')
          const { selectedInfo } = riskModelData ? JSON.parse(riskModelData) : {}
          selectedInfo && (this.selectedInfo = selectedInfo)
          sessionStorage.removeItem('riskModel')
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.inter-container {
  padding: 120px 30px 230px;

  .label {
    display: block;
    color: var(--text_3rd);
    font-size: 26px;
    line-height: 40px;
    font-weight: 400;
  }
}
.mgb30 {
  margin-bottom: 30px;
}
</style>