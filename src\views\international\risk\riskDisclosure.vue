<template>
  <div :class="['inter-risk-container', $store.state.locale === 'en' ? 'lang-en': '']">
    <nav-header />

    <div v-for="(item, index) in $t('risk.disclosure')" :key="index" class="card">
      <p class="content">{{ !isEnglish ? item.zh : item.en }}</p>
      <p v-if="!isEnglish" class="description">{{ item.en }}</p>
    </div>

    <eddid-agree v-model="isAgree" class="agree">
      <div class="agree-content"><span>{{ $t('risk.agreePrefix') }}</span><a @click="onOpenAgree('fundRiskWarning')" class="link-text">「{{ agreeInfo.fundRiskWarning && agreeInfo.fundRiskWarning.name ? agreeInfo.fundRiskWarning.name : '' }}」</a>{{ $t('risk.and') }}<a @click="onOpenAgree('fundSales')" class="link-text">「{{ agreeInfo.fundSales && agreeInfo.fundSales.name ? agreeInfo.fundSales.name : '' }}」</a>
      </div>
    </eddid-agree>
    <eddid-agree v-model="isAgree2" class="agree">
      <div class="agree-content">{{ $t('risk.disclosureTip') }}</div>
    </eddid-agree>

    <risk-footer>
      <md-button @click="onSubmit" type="primary">{{ $t('common.btns.submit') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/international/NavHeader.vue";
import EddidAgree from './component/EddidAgree.vue';
import RiskFooter from './component/Footer.vue';
import { mapState } from 'vuex';
import { submitOpenAccount } from '@/services/risk';
import { agreementContent } from '@/services/common';
import { Toast } from 'mand-mobile';

export default {
  components: { NavHeader, RiskFooter, EddidAgree },
  data() {
    return {
      agreeInfo: {
        fundRiskWarning: null,
        fundSales: null
      },
      isAgree: false,
      isAgree2: false
    }
  },
  computed: {
    ...mapState(['theme', 'locale']),
    isEnglish() {
      return this.locale === 'en';
    }
  },
  beforeMount() {
    this.getAgreeURL('fundRiskWarning')
    this.getAgreeURL('fundSales')
  },
  methods: {
    onSubmit() {
      if (!this.isAgree) {
        const { fundRiskWarning, fundSales } = this.agreeInfo
        // return Toast.info(`${this.$t('risk.pleaseChoose')}「${this.$t('risk.agreePrefix')}${fundRiskWarning && fundRiskWarning.name ? fundRiskWarning.name : ''}${this.$t('risk.and')}${fundSales && fundSales.name ? fundSales.name : ''}」`);
        return Toast.info(`${this.$t('risk.agreeTip1', {
          Name1: fundRiskWarning && fundRiskWarning.name ? fundRiskWarning.name : '',
          Name2: fundSales && fundSales.name ? fundSales.name : ''
        })}`);
      }
      if (!this.isAgree2) {
        return Toast.info(`${this.$t('risk.pleaseChoose')}「${this.$t('risk.disclosureTip')}」`);
      }

      submitOpenAccount().then(res => {
        if(res.code === 200) {
          this.$router.push('/international/risk/result');
        }
      })
    },
    onOpenAgree(type) {
      const { name, path } = this.agreeInfo[type];
      if(!path) return;
      this.$router.push(`/international/risk/agreepage?name=${name || ''}&path=${path}`);
    },
    getAgreeURL(type) {
      agreementContent({ platform: 'H5', type })
        .then(res => {
          const { path, clauseName } = res.data
          path && (this.agreeInfo[type] = { name: clauseName, path })
        })
    }
  },
}
</script>

<style lang="scss" scoped>
.inter-risk-container {
  padding-top: 118px;
  padding-bottom: 272px;

  ::v-deep.inter-risk-footer {
    &::before {
      content: none;
    }
  }
}
.card {
  position: relative;
  padding: 0 32px;

  & + & {
    margin-top: 48px;
  }

  .content {
    margin-bottom: 32px;
    color: var(--text_1st);
    font-size: 28px;
    line-height: 44px;
    font-weight: 400;
  }
  .description {
    background-color: var(--gray_05);
    padding: 20px;
    color: var(--text_3rd);
    line-height: 40px;
    font-size: 28px;
  }
}

.agree {
  padding: 0 32px;
  color: var(--text_3rd);
  margin-top: 64px;

  & + & {
    margin-top: 32px;
  }
}
.link-text {
  color: var(--brand_01);
}

.lang-en.inter-risk-container {
  ::v-deep.inter-nav-header-container {
    .title {
      padding-left: 96px;

      span {
        max-width: calc(100% - 216px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>