<template>
  <div :class="['inter-container-level', $store.state.locale === 'en' ? 'lang-en': '']">
    <nav-header :back="onBack" />
    <div :class="['bg-level']">
      <div :class="[`bg-type${formValue.type || 'R1'}`, 'type-container']">
      <p class="name">{{ $t('risk.evaluationResult') }}</p>
      <div class="type">
        <div class="left-content">
          <span>{{ formValue.type ? $t(`risk.typeText${formValue.type}`) : '' }}</span>
        <span class="img-quotation" />
        </div>
        <div class="right-content">
            <img :src="require(`@/assets/images/international/risklevel${formValue.type || 'R1'}.png`)" alt="">
          </div>
      </div>
      <p class="description">{{ formValue.type ? $t(`risk.description${formValue.type}`) : '' }}</p>
    </div>
    </div>
    <ul class="content-info">
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName1') }}</label>
        <span class="text">{{ formValue.isProfessionalInvestor || '' }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName2') }}</label>
        <span class="text">{{ formValue.type ? $t(`risk.riskText${formValue.type}`) : '' }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName3') }}</label>
        <span class="text">{{ formValue.type ? $t(`risk.typeText${formValue.type}`) : '' }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName4') }}</label>
        <span class="text">{{ formValue.timeHorizon }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName5') }}</label>
        <span class="text">{{ formValue.declaredNetAssets }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName6') }}</label>
        <span class="text">{{ formValue.derivativesKnowledge }}</span>
      </li>
    </ul>
    <eddid-agree v-if="!isConfirm" v-model="isAgree" class="agree">
			<span>{{ $t('risk.agree') }}</span>
		</eddid-agree>
    <risk-footer v-if="showFooter">
      <!-- 2个 按钮 -->
      <md-button v-if="!isConfirm" @click="onOpenAccount" type="primary">{{ $t('risk.btnOpenAccount') }}</md-button>
      <div v-if="!isConfirm" @click="onRestart" class="text-btn">{{ $t('risk.btnReEvaluate') }}</div>
      <!-- 只有 1 个按钮 -->
      <md-button v-else @click="onRestart" type="primary">{{ $t('risk.btnReEvaluate') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from '@/components/international/NavHeader.vue'
import RiskFooter from "./component/Footer.vue";
import { getAccountNumber } from "@/services/account";
import { getRiskLevel, getOpenAccountStatus, confirmQuestions, professionalInvestorAgreement } from '@/services/risk';
import { queryLink } from '@/services/common'
import EddidAgree from './component/EddidAgree.vue';
import { Dialog, Toast } from 'mand-mobile';

export default {
  components: { NavHeader, RiskFooter, EddidAgree },
  data() {
    return {
      showFooter: false,
      formValue: {},
      isConfirm: false, // 是否确认结果
      accountStatus: '',
      isComplexProduct: 0,
      isAgree: false,
      pilink:'',
      vaObj: {}
    }
  },
  beforeMount() {
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }
    this.getRiskLevel();
  },
  mounted() {
    this.getAccountNumber()
    this.$jsBridge.run('navBackByAppSelf', { isClosePage: true })
    
  },
  methods: {
    async onOpenAccount() {
      if (!this.isAgree) return Toast.info(`${this.$t('risk.pleaseChoose')}「${this.$t('risk.agree')}。」`);
      // VA交易账户的客户，若该客户在进行基金的风险测评时，其基金风险等级的测评的分值<45分，则不允许客户进行【确认结果无误】的确认提交
      if(this.vaObj.type && this.vaObj.type === 'VIRTUAL_ASSET' && this.formValue.level !== '5'){
        const dialog = Dialog.confirm({
          // title: this.$t('risk.confirmExitPage'),
          content: this.$t('risk.hadVaAcount'),
          cancelText: this.$t('common.btns.cancel'),
          confirmText: this.$t('risk.btnReEvaluate'),
          onConfirm: () => {
            this.onRestart()
          },
        })
        const el = dialog.$el;
        el && el.classList.add('inter-dialog-confirm');
        return
      }

      if (sessionStorage.getItem('ispi')==='yes') {
        let res = await professionalInvestorAgreement()
        if (res?.code===200) {
          sessionStorage.removeItem('ispi')
          this.getPiLink()
        }
      }else{
        confirmQuestions().then(res => {
        if(this.accountStatus && this.accountStatus !== 'REJECT') {
          this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/international');
        } else {
          if (sessionStorage.getItem('isExpired') === 'yes') {
            this.$jsBridge.run("startEdWeb", {
              url: window.location.origin + `/international/fundKind`,
              callback: () => {},
            });
          } else {
            this.$router.push('/international/risk/disclosure');
          }
        }
      })
      }
    },
    onRestart() {
      const dialog = Dialog.confirm({
        title: this.$t('risk.btnReEvaluate'),
        content: this.$t('risk.confirmReEvaluation'),
        cancelText: this.$t('common.btns.cancel'),
        confirmText: this.$t('risk.btnToEvaluate'),
        onConfirm: () => {
          this.$router.push(`/international/risk/assessment`);
        },
      })
      const el = dialog.$el;
      el && el.classList.add('inter-dialog-confirm');
    },
    async getRiskLevel () {
      this.$loading.show();
      try {
        const statusRes = await getOpenAccountStatus({ispi:sessionStorage.getItem('ispi') || 'no'})
        const levelRes = await getRiskLevel({
          ispi:sessionStorage.getItem('ispi') || 'no',
          queryLatestSubmitResult: !!this.$route.query.isLatest
        })
        if(statusRes.code === 200 && statusRes.data){
          this.accountStatus = statusRes.data.status;
        }
        if(levelRes.code === 200){
          this.formValue = {
            isProfessionalInvestor: levelRes.data.isProfessionalInvestor,
            type: levelRes.data.result,
            level: levelRes.data.result ? levelRes.data.result.split('R')[1] : 0,
            timeHorizon: levelRes.data.timeHorizon,
            declaredNetAssets: levelRes.data.declaredNetAssets,
            derivativesKnowledge: levelRes.data.derivativesKnowledge,
          }
          this.isConfirm = levelRes.data.agreement;
          this.showFooter = true;
        }
      } catch (e) {
        console.log('error', e)
      } finally {
        this.$loading.hide();
      }
    },
    onBack () {
      this.$jsBridge.isSupported('navBack') ? this.$jsBridge.run('navBack', { isClosePage: true }) : this.$router.push('/');
    },
    async getPiLink(){
      let params = {
        key: "/H5_URLs",
      };
      let res = await queryLink(params);
      if (res && res.code === 200) {
        let pilinkArr =
          res.data.filter((item) => {
            return item.keyText === "pi_submit_new";
          }) || [];
        this.pilink = pilinkArr[0] && pilinkArr[0].valueText;
        this.$jsBridge.isSupported('navBack') ? this.$jsBridge.run('navBack', { isClosePage: true }) : '';
        this.$jsBridge.isSupported('navBack')
          ? this.$jsBridge.run('startEdWeb', {
              url: this.pilink,
              callback: () => {},
            })
          : window.open(this.pilink)
      }
    },
    getAccountNumber(){
      getAccountNumber().then(res => {
        let vaObj = res.data.tradingAccountList.find((item) => {
          return item.type === "VIRTUAL_ASSET";
        });
        this.vaObj = vaObj ? vaObj : {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.inter-container-level {
  padding: 108px 0 30px;

  ::v-deep.inter-risk-footer {
    position: static !important;
    padding-bottom: 0;
    text-align: center;

    &::before {
      content: none;
    }

    .md-button {
      line-height: 30px;
      font-size: 30px;
    }

    .text-btn {
      margin: 40px auto;
      padding: 30px;
      color: var(--brand_01);
      font-size: 28px;
      line-height: 40px;
      font-weight: 400;
      text-decoration: underline;
    }
  }
}
.bg-level {
  background-size: 100%;
  width: 100vw;
  padding: 0 32px;
  color: var(--background);
  .type-container {
    padding: 32px 32px 52px;
    border-radius: 15px;
  }
  .name {
    color: var(--text_opacity);
    font-size: 24px;
    line-height: 36px;
    font-weight: 400;
  }
  .type {
    position: relative;
    margin-bottom: 32px;
    font-size: 56px;
    line-height: 84px;
    font-weight: 500;
    vertical-align: text-top;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .right-content {
    width: 160px;
    height: 160px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-quotation {
    background-image: url(~@/assets/images/international/icon_quotation.png);
    background-size: 48px 48px;
    background-repeat: no-repeat;
    position: absolute;
    top: 50px;
    width: 48px;
    height: 48px;
    margin-left: 20px;
  }
  .description {
    font-size: 26px;
  }
}

.bg-typeR1 {
  background: linear-gradient( 135deg, #08A7E3 0%, #0076C1 100%);
}
.bg-typeR2 {
  background: linear-gradient( 135deg, #07C9D0 0%, #00AAB1 100%);
}
.bg-typeR3 {
  background: linear-gradient( 315deg, #1A51CE 0%, #3679FA 100%);
}
.bg-typeR4 {
  background: linear-gradient( 135deg, #FFA62B 0%, #F47305 100%);
}
.bg-typeR5 {
  background: linear-gradient( 315deg, #F64B41 0%, #FE9366 100%);
}

.content-info {
  position: relative;
  background-color: var(--background);
  margin: 48px 32px 80px;
  padding-bottom: 30px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border: 1px solid var(--line_01);
    border-radius: 30px;
    transform: scale(1);
    transform-origin: 0 0;
    pointer-events: none;
  }

  .row-item {
    @extend .x-between-y-center;
    padding: 24px 32px 0;
  }
  .label {
    color: var(--text_3rd);
    font: 400 28px/28px PingFangSC-Regular, PingFang SC;
  }
  .text {
    color: var(--text_1st);
    font: 400 28px/28px DINPro-Regular, DINPro;
    text-align: right;
  }
}
.agree {
	padding: 0 32px;
  margin-bottom: 40px;
}


@media screen and(-webkit-min-device-pixel-ratio:2) {
  .content-info::before {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .content-info::before {
    width: 300%;
    height: 300%;
    transform: scale(0.3333);
  }
}

.lang-en {
  .bg-level .description {
    font-size: 22px;
  }
}
</style>