<template>
  <div :class="['inter-container', isLastQuestion ? 'container-last-question' : '']" ref="container">
    <nav-header :close="onClose"/>
    <div v-if="questionList && !!questionList.length" ref="question">
      <p v-if="isFirstQuestion" class="title">{{ $t('risk.choose') }}</p>
      <p class="mgb70">
        <span class="current">{{ currentQuestion ? currentQuestion.questionNumber : '' }}</span>
        <span class="total">/{{ total }}</span>
      </p>

      <question-card
        v-if="!!currentQuestion"
        :value="selectedValue"
        :data="currentQuestion"
        @change="onSelectChange"
        data-vv-name="riskQuestion"
        v-validate="'required'"
        data-vv-validate-on="change"
        :errors="this.$validator.errors"
      />
    </div>

    <risk-footer>
      <md-button @click="onPre" plain class="btn-pre">
        {{ isFirstQuestion ? $t('risk.onBack') : $t('risk.btnPre') }}
      </md-button>
      <md-button v-if="!isLastQuestion" @click="onNext" :type="selectedValue && selectedValue.length ? 'primary' : 'disabled'">
        {{ $t('risk.btnNext') }}
      </md-button>
      <md-button v-else @click="onSubmit" :type="selectedValue && selectedValue.length ? 'primary' : 'disabled'">{{$t('fundMessage.submitText')}}</md-button>
    </risk-footer>

    <risk-card v-if="isLastQuestion" :title="$t('risk.title3')">
      <p>{{ $t('risk.description4') }}</p>
      <p class="mgt20">{{ $t('risk.description5') }}</p>
      <!-- <p class="mgt20">{{ $t('risk.description6') }}</p> -->
    </risk-card>
  </div>
</template>

<script>
import NavHeader from '@/components/international/NavHeader.vue'
import QuestionCard from './component/QuestionCard.vue';
// import EddidSelect from './component/EddidSelect.vue';
import RiskFooter from './component/Footer.vue';
import RiskCard from './component/RiskCard.vue';
import { mapState } from 'vuex';
import { Toast, Dialog } from 'mand-mobile';
import { getQuestionList, submitQuestions ,getPIQuestionList, submitPIQuestions } from '@/services/risk';

export default {
  name: 'interRiskQuestions',
  components: { NavHeader, QuestionCard, RiskFooter, RiskCard,  },
  data() {
    // this.ageGroup = {
    //   A: [18, 34],
    //   B: [35, 49],
    //   C: [50, 64],
    //   D: [65, 74],
    //   E: [75, Infinity],
    // }

    return {
      isQuestion: 1,
      questionList: [],
      total: 0,
      currentQuestion: null,
      name: '',
      idCard: '',
      age: 0,
      selectedInfo: {},
      error: false,
      thriderror:false,
      isComplexProduct: 0,
      employment: null,
    }
  },
  watch: {
    '$route': {
      deep: true,
      handler(newVal) {
        const { name, params: { id }, } = newVal;
        if (name === 'interRiskQuestions') {
          Toast.hide();
          id && this.getQuestionByNo(id);
          this.$nextTick(() => {
            if(this.$validator && this.$validator.errors) {
              const { items } = this.$validator.errors;
              items.length && this.$validator.errors.clear();
            }
          })
        }
      }
    },
  },
  computed: {
    selectedValue() {
      if (!this.currentQuestion) return '';
      const { questionNumber, type } = this.currentQuestion;
      return this.selectedInfo[questionNumber] || (type === 'more' ? [] : '');
    },
    isFirstQuestion() {
      return (
        !this.currentQuestion ||
        !this.currentQuestion.questionNumber ||
        this.currentQuestion.questionNumber === this.questionList[0].questionNumber
      )
    },
    isLastQuestion() {
      if (!this.currentQuestion) return false;
      return (
        this.currentQuestion.questionNumber &&
        this.currentQuestion.questionNumber === this.questionList[this.total - 1].questionNumber
      )
    },
    ...mapState(['theme']),
  },
  beforeMount() {
    this.getQuestionList();
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }
  },
  mounted(){
    if(sessionStorage.getItem('selectedInfo', this.selectedInfo)){
      this.selectedInfo = JSON.parse(sessionStorage.getItem('selectedInfo', this.selectedInfo))
    }
  },
  methods: {
    async onSubmit() {
      const success = await this.$validator.validate()
      if (!success) {
        Toast.info(this.$t('risk.choose'), 2000);
        return false;
      }
      const answerList = [];
      Object.keys(this.selectedInfo).map((key) => {
        const temp = {
          questionNumber: key,
          answer: Array.isArray(this.selectedInfo[key]) ? this.selectedInfo[key].join(',') : this.selectedInfo[key],
        }
        
        answerList.push(temp);
      })
      let params={
        name: this.name,
        idNumber: this.idCard,
        answerList,
        submitSource: 'CP_H5'
      }

      let res={}
      res =  await submitQuestions(params);
      if (res?.code === 200) {
        sessionStorage.setItem('selectedInfo', '')
        if(this.isComplexProduct === '1') {
          this.$router.push({path:'/international/risk/level',query: { isComplexProduct: this.isComplexProduct, isLatest: true }})
          return
        }
        // this.$router.push('/international/risk/level');
        this.$router.push({
            path: '/international/risk/level',
            query: {
              isLatest: true
            }
        });
      }
    },
    onNext() {
      this.$validator.validate().then(success => {
        if (!success) {
          Toast.info(this.$t('risk.choose'), 2000);
          return false;
        }
        const { questionNumber } = this.currentQuestion;
        const nextNo = this.questionList[questionNumber].questionNumber;
        this.$router.push(`/international/risk/questions/${nextNo}`);
      });
    },
    onClose() {
      if(!Object.values(this.selectedInfo).filter(i => !!i).length) {
        this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/international');
        return;
      }
      const dialog = Dialog.confirm({
        title: this.$t('risk.confirmExitPage'),
        content: this.$t('risk.exitPageTip'),
        cancelText: this.$t('common.btns.cancel'),
        confirmText: this.$t('common.btns.confirm'),
        onConfirm: () => {
          this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/international');
        },
      })
      const el = dialog.$el;
      el && el.classList.add('inter-dialog-confirm');
    },
    onPre() {
      if(this.isFirstQuestion) {
        sessionStorage.setItem('selectedInfo', '')
        this.$router.push('/international/risk/assessment');
        return;
      }
      const no = Number(this.currentQuestion.questionNumber) - 2;
      const preNo = this.questionList[no].questionNumber;
      this.$router.push(`/international/risk/questions/${preNo}`);
    },
    onSelectChange(value, key) {
      this.$set(this.selectedInfo, `${key}`, value);
      sessionStorage.setItem('selectedInfo', JSON.stringify(this.selectedInfo))
      console.log(this.selectedInfo, 'this.selectedInfo')
    },
    async getQuestionList() {
      try {
        this.$loading.show();
        let res = await getQuestionList()
        if (res?.code === 200) {
          let { jsonText = [], user, finishedList = [], isFinished } = res.data;
          const { name, idNumber, age, employmentStatus } = user || {};
          jsonText = this.setSingleOption(jsonText)
          console.log(jsonText, 'jsonText')
          this.questionList = jsonText;
          this.total = jsonText.length;
          this.name = name || '';
          this.idCard = idNumber || '';
          this.age = age || 0;
            
          const first = this.$route.params.id ? this.questionList[this.$route.params.id - 1] : this.questionList [0];
          this.currentQuestion = first;
          this.setFirstStauts()
          this.getPreSelected()
        }
      } catch (error) {
        console.log(error);
      } finally{
        this.$loading.hide();
      }
    },
    setSingleOption(jsonText) {
      jsonText.forEach(item => {
        if(item.questionNumber === 6){
          item.options.forEach(option => {
            if(option.value === 'F' || option.value === 'G'){
              option.single = true
            }
          })
        } 
        if(item.questionNumber === 7){
          item.options.forEach(option => {
            if(option.value === 'A'){
              option.single = true
            }
          })
        } 
      })
      return jsonText
    },
    setFirstStauts(){
      // 基金风险评测- 根据用户信息自动选择, 第一题-不允许修改
      // 基金风险评测- 根据用户信息自动选择, 第一题-不允许修改
      if (this.currentQuestion && this.currentQuestion.answer && this.currentQuestion.questionNumber ===1) {
        this.$set(this.selectedInfo, '1', this.currentQuestion.answer)
        this.currentQuestion.disabled = true
      }
    },
    getPreSelected() {
      this.questionList.forEach(item => {
        if(item.answer && !this.selectedInfo[item.questionNumber]) {
          this.$set(this.selectedInfo, item.questionNumber + '', item.answer.includes(',') ? item.answer.split(',') : item.answer)
        }
      })
    },
    getQuestionByNo(questionNumber) {
      const currentQuestion = this.questionList.find((item) => `${item.questionNumber}` === `${questionNumber}`);
      this.currentQuestion = currentQuestion || null;
      this.setFirstStauts();
    },
  },
}
</script>

<style lang="scss" scoped>
.inter-container {
  padding: 118px 30px 230px;

  .title {
    margin-bottom: 38px;
    color: var(--text_1st);
    line-height: 52px;
    font-size: 34px;
    font-weight: 500;
  }

  .current {
    color: var(--brand_01);
    line-height: 50px;
    font-size: 50px;
    font-weight: 500;
  }
  .total {
    color: var(--text_3rd);
    line-height: 28px;
    font-size: 28px;
    font-weight: 400;
  }

  ::v-deep.inter-risk-footer {
    display: flex;

    .md-button + .md-button {
      margin-left: 20px;
    }
  }
}

.card-employ {
  margin-top: 64px;
  @include font_color(text-color);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;
}


.inter-container.container-last-question {

  & ::v-deep.tip-card {
    margin-top: 64px;

    .description {
      padding-bottom: 54px;
    }
  }
}
</style>