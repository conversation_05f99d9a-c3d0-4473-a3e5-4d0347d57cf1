<template>
  <div>
    <nav-header :back="onBack" />
    <div class="content">
      <img class="img-wait" :src="require('@/assets/images/international/illustrations_auditing.png')" />
      <div class="title">{{$t('risk.underReview')}}</div>
      <div class="tip">{{$t('risk.waitMinutes')}}</div>
      <md-button @click="onBack" type="primary">{{$t('myAccount.return')}}</md-button>
    </div>
  </div>
</template>

<script>
import NavHeader from "@/components/international/NavHeader.vue";
export default {
  components: { NavHeader, },
  data() {
    return {
    }
  },
  mounted() {
    this.$jsBridge.run('navBackByAppSelf', { isClosePage: true })
  },
  methods: {
    onBack() {
      if(this.$jsBridge.isSupported('navBack')){
        this.$jsBridge.run('navBack', { isClosePage: true });
      }else {
        this.$router.push('/international');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 60px 30px 0;
  text-align: center;
}
.img-wait {
  width: 142px;
  height: 142px;
  margin: 118px auto 26px;
}
.title {
  color: var(--text_1st);
  font-size: 28px;
  text-align: center;
  font-weight: 500;
  font-family: PingFangSC-Medium, PingFang SC, sans-serif;
}
.tip {
  margin: 36px auto 212px;
  color: var(--text_1st);
  font-size: 28px;
  text-align: center;
  font-weight: 400;
}
::v-deep.md-button.primary {
  border-radius: 46px;

  .md-button-inner {
    color: var(--text_5th);
    font-size: 30px;
  }
}
</style>
