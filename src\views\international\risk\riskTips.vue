<template>
  <div class="inter-container">
    <nav-header :back="onBack" />
    <risk-card :title="$t('risk.title1')">
      <p class="tip-item-info" v-for="(item, index) in $t('risk.description1')" :key="index">{{ item }}</p>
    </risk-card>
    <risk-card :title="$t('risk.title2')">
      <p>{{$t('risk.description2')}}</p>
      <p class="mgt20">{{$t('risk.description3')}}</p>
    </risk-card>
    <risk-footer>
      <md-button @click="onToPath" type="primary">{{$t('risk.hadGreen')}}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/international/NavHeader.vue";
import RiskCard from "./component/RiskCard.vue";
import RiskFooter from "./component/Footer.vue";
import { urlParse } from '@/utils/util';
import { getTotalAttempts } from '@/services/risk';
import { Dialog } from 'mand-mobile';
export default {
  name: 'interRisk',
  components: { NavHeader, RiskCard, RiskFooter },
  data() {
    return {
      isFinish:false,
      attempts: {}
    };
  },
  created(){
    // 是否是PI认证
    const ispi = urlParse(window.location.search).ispi || 'no'
    sessionStorage.setItem('ispi', ispi)
    const isExpired = urlParse(window.location.search).isExpired || 'no'
    sessionStorage.setItem('isExpired', isExpired)
    this.getTotalAttempts()
  },
  methods: {
    async onToPath() {
      
      if (!this.isFinish) {
        this.$loading.show(); // 显示加载中
        await this.getTotalAttempts();
        this.$loading.hide(); // 隐藏加载中
      }
      if(this.attempts.last30DaysAttemptTotal >= this.attempts.last30DaysMaxAttemptTotal){
        let dialog = Dialog.confirm({
          content: this.$t('risk.hadFiveTimes', {number: this.attempts.last30DaysMaxAttemptTotal}),
          cancelText: this.$t('common.btns.cancel'),
          confirmText: this.$t('risk.continueAssessment'),
          onConfirm: () => {
            this.continueJudge()
          },
        })
        const el = dialog.$el;
        el && el.classList.add('inter-dialog-confirm');
        return
      }
      this.continueJudge()
    },
    continueJudge() {
      if(this.attempts.currentDaysAttemptTotal >= this.attempts.currentDaysMaxAttemptTotal){
        let dialog = Dialog.alert({
          content: this.$t('risk.useUpChange'),
          confirmText: this.$t('common.btns.gotIt'),
        })
        const el = dialog.$el;
        el && el.classList.add('inter-dialog-confirm');
      }else if(this.attempts.currentDaysAttemptTotal < this.attempts.currentDaysMaxAttemptTotal){
        let dialog = Dialog.confirm({
          content: this.$t('risk.haveChanges',{number:this.attempts.currentDaysMaxAttemptTotal - this.attempts.currentDaysAttemptTotal}),
          cancelText: this.$t('common.btns.cancel'),
          confirmText: this.$t('common.btns.goOn'),
          onConfirm: () => {
            this.$router.push('/international/risk/assessment');
          },
        })
        const el = dialog.$el;
        el && el.classList.add('inter-dialog-confirm');
      }
    },
    onBack () {
      this.$jsBridge.isSupported('navBack')
          ? this.$jsBridge.run('navBack', { isClosePage: true })
          : this.$router.push('/')
    },
    
    async getTotalAttempts() {
      try {
        const { data } = await getTotalAttempts();
        this.attempts = data;
        this.isFinish = true;
        return data; 
      } catch (error) {
        this.isFinish = false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.inter-container {
  padding: 120px 30px 230px;
}
.tip-item-info{
  margin-bottom: 16px;
}
.tip-item-info:last-child{
  margin-bottom: 0;
}
</style>