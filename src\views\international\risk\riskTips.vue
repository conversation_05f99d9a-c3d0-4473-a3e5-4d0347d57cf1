<template>
  <div class="inter-container">
    <nav-header :back="onBack" />
    <risk-card :title="$t('risk.title1')">
      <p>{{$t('risk.description1')}}</p>
    </risk-card>
    <risk-card :title="$t('risk.title2')">
      <p>{{$t('risk.description2')}}</p>
      <p class="mgt20">{{$t('risk.description3')}}</p>
    </risk-card>
    <risk-footer>
      <md-button @click="onToPath" type="primary">{{$t('risk.btnStartEvaluate')}}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/international/NavHeader.vue";
import RiskCard from "./component/RiskCard.vue";
import RiskFooter from "./component/Footer.vue";
import { urlParse } from '@/utils/util';
export default {
  name: 'interRisk',
  components: { NavHeader, RiskCard, RiskFooter },
  data() {
    return {};
  },
  created(){
    // 是否是PI认证
    const ispi = urlParse(window.location.search).ispi || 'no'
    sessionStorage.setItem('ispi', ispi)
    const isExpired = urlParse(window.location.search).isExpired || 'no'
    sessionStorage.setItem('isExpired', isExpired)
  },
  methods: {
    onToPath() {
      this.$router.push('/international/risk/assessment');
    },
    onBack () {
      this.$jsBridge.isSupported('navBack')
          ? this.$jsBridge.run('navBack', { isClosePage: true })
          : this.$router.push('/')
    }
  },
};
</script>

<style lang="scss" scoped>
.inter-container {
  padding: 120px 30px 230px;
}
</style>