<template>
  <div :class="['inter-risk-container', $store.state.locale === 'en' ? 'lang-en': '']">
    <nav-header />

    <div v-for="(item, index) in $t('risk.statement')" :key="index" class="card">
      <p class="content">{{ !isEnglish ? item.en : item.zh }}</p>
      <p v-if="!isEnglish" class="description">{{ item.zh }}</p>
    </div>

    <risk-footer>
      <md-button @click="onSubmit" type="primary">{{ $t('risk.readSign') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/international/NavHeader.vue";
import RiskFooter from './component/Footer.vue';
import { mapState } from 'vuex';

export default {
  components: { NavHeader, RiskFooter },
  data() {
    return {
      agreeInfo: {
        fundRiskWarning: null,
        fundSales: null
      },
    }
  },
  computed: {
    ...mapState(['theme', 'locale']),
    isEnglish() {
      return this.locale === 'en';
    }
  },
  beforeMount() {
    // this.getAgreeURL('fundRiskWarning')
    // this.getAgreeURL('fundSales')
  },
  methods: {
    onSubmit() {
      this.$router.push('/international/risk/sign')
    },
    // getAgreeURL(type) {
    //   agreementContent({ platform: 'H5', type })
    //     .then(res => {
    //       const { path, clauseName } = res.data
    //       path && (this.agreeInfo[type] = { name: clauseName, path })
    //     })
    // }
  },
}
</script>

<style lang="scss" scoped>
.inter-risk-container {
  padding-top: 118px;
  padding-bottom: 272px;

  ::v-deep.inter-risk-footer {
    &::before {
      content: none;
    }
  }
}
.card {
  position: relative;
  padding: 0 32px;

  & + & {
    margin-top: 48px;
  }

  .content {
    margin-bottom: 32px;
    color: var(--text_1st);
    font-size: 28px;
    line-height: 44px;
    font-weight: 400;
  }
  .description {
    background-color: var(--gray_05);
    padding: 20px;
    color: var(--text_3rd);
    line-height: 40px;
    font-size: 28px;
  }
}

.agree {
  padding: 0 32px;
  color: var(--text_3rd);
  margin-top: 64px;

  & + & {
    margin-top: 32px;
  }
}
.link-text {
  color: var(--brand_01);
}

.lang-en.inter-risk-container {
  ::v-deep.inter-nav-header-container {
    .title {
      padding-left: 96px;

      span {
        max-width: calc(100% - 216px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>