<template>
  <div class="md-example-child login">
    <img class="logo" v-if="theme === 'dark'" src="@/assets/images/dark/logo.png" />
    <img class="logo" v-else src="@/assets/images/logo.png" />
    <div class="logo-hr"></div>
    <div class="mark-title">
      <span class="mark"></span>
      <span class="text">{{$t('login.loginTitle')}}</span>
    </div>

    <md-field>
      <div class="mobile-phone">
        <div class="select">
          <md-field-item
            :placeholder="$t('login.chooseAreaCode')"
            :content="phoneCodeContent"
            @click="showPhoneCode"
            arrow="arrow-down"
            solid
          />
          <md-selector
            v-model="isPhoneCodeShow"
            :data="phoneCodeOptions"
            :default-value="phoneCode"
            max-height="320px"
            @choose="onPhoneCodeChoose"
          ></md-selector>
        </div>
        <md-input-item
          class="phone"
          :name="$t('login.phone')"
          v-validate="'required'"
          data-vv-validate-on="input"
          :error="errors.first($t('login.phone'))"
          :type="(['86'].includes(phoneCode))?'phone':'digit'"
          v-model.trim="phone" :maxlength="phoneCodeLimit[phoneCode]"
          :placeholder="`${$t('common.pleaseEnter')}${$t('login.phone')}`"
        ></md-input-item>
      </div>
      <!-- 密码 -->
      <md-input-item
        v-if="loginType === 'password'"
        class="password"
        :name="$t('login.password')"
        v-validate="'required'"
        data-vv-validate-on="input"
        :error="errors.first($t('login.password'))"
        type="password"
        v-model.trim="password"
        :maxlength="16"
        :placeholder="`${$t('common.pleaseEnter')}${$t('login.password')}`"
      ></md-input-item>
      <!-- 短信 -->
      <md-input-item
        v-if="loginType === 'code'"
        class="message"
        :name="$t('login.vcode')"
        v-validate="'required'"
        data-vv-validate-on="input"
        :error="errors.first($t('login.vcode'))"
        type="digit"
        :placeholder="`${$t('common.pleaseEnter')}${$t('login.vcode')}`"
        v-model.trim="vcode"
        maxlength="6"
        is-amount
      >
        <a class="input-operator" slot="right">
          <span v-if="sendable" @click="getVerCode">{{$t('login.getVerCode')}}</span>
          <span v-else>{{counter}}</span>
        </a>
      </md-input-item>
    </md-field>
    <!-- 提交 -->
    <md-button @click="login" class="btn" type="primary">
      {{$t('login.loginBtnLabel')}}
    </md-button>
  </div>
</template>

<script>
import { InputItem, Field, Button, DropMenu, FieldItem, Selector, Toast, } from 'mand-mobile';
import fingerprint2 from 'fingerprintjs2';
import exchangeSelectOpt from "@/utils/exchangeSelectOpt";
import { mapState } from 'vuex';
import { getSmsCode, login } from "@/services/auth";
import qs from 'qs';
import encodeStr from "@/utils/setjsencrypt";
export default {
  name: 'login',
  components: {
    [InputItem.name]: InputItem,
    [Field.name]: Field,
    [Button.name]: Button,
    [DropMenu.name]: DropMenu,
    [FieldItem.name]: FieldItem,
    [Selector.name]: Selector,
  },
  head() { // 设置页面title
    return {
      title: this.$t('login.loginTitle'),
    };
  },
  data() {
    return {
      isPhoneCodeShow: false,
      phoneCode: '86',
      phoneCodeContent: this.$t('login.phoneCodeOptions')['86'],
      phone: process.env.NODE_ENV === 'develop' || process.env.NODE_ENV === 'local' ? '13610330517' : '',
      vcode: '',
      deviceId: '',
      sendable: true,
      password: process.env.NODE_ENV === 'develop' || process.env.NODE_ENV === 'local' ? 'Abc123456' : '',
      maxCounter: 60,
      loginType: 'password',
      counter: 60,
      phoneCodeLimit: {
        86: '11',
        852: '8',
        886: '10',
        853: '8',
      },
    };
  },

  mounted() {
    this.loginType = this.$route.query.type ? this.$route.query.type : this.loginType
    if (this.loginType === 'code') {
      this.password = '1'
    }
    const options = {};
    // 通过 Fingerprint2 生成设备 Id
    if (window.requestIdleCallback) {
      requestIdleCallback(() => {
        fingerprint2.getV18(options, (id) => {
          this.deviceId = id;
        });
      });
    } else {
      setTimeout(() => {
        fingerprint2.getV18(options, (id) => {
          this.deviceId = id;
        });
      }, 500);
    }

  },
  computed: {
    ...mapState(["theme"]),
    phoneCodeOptions() {
      return this.exchangeSelectOpt(this.$t('login.phoneCodeOptions'));
    },
  },
  methods: {
    exchangeSelectOpt,
    showPhoneCode() {
      this.isPhoneCodeShow = true;
    },
    onPhoneCodeChoose({ value, text }) {
      this.phoneCode = value;
      this.phoneCodeContent = text;
    },
    tick() {
      this.counter = this.counter - 1;
      setTimeout(() => {
        if (this.counter === 0) {
          this.sendable = true;
          this.counter = this.maxCounter;
        } else {
          this.tick();
        }
      }, 1000);
    },
    async getVerCode() {
      if (!this.phone) {
        Toast({ content: '请正确输入手机号' })
        return;
      }
      if (!this.sendable) return;
      try {
        const deviceId = this.$route.query.deviceId
          ? this.$route.query.deviceId
          : "80d00dc13823e4373b02268d66412d13";
        const requestParams = {
          phone: this.phoneCode + this.phone,
          device_id: deviceId,
          send_type: 'login',
          sign: encodeStr(
            `send_type=login&device_id=${deviceId}&phone=${this.phoneCode}${this.phone}`
          ),
        };
        this.sendable = false;
        this.tick();
        const res = await getSmsCode(requestParams, this);
      } catch (err) {
        console.log(`error${err}`);
      }
    },
    getUrlParam(k) {
      let regExp = new RegExp(`([?]|&)${k}=([^&]*)(&|$)`);
      let STurl = window.sessionStorage.getItem('url');
      let url = STurl || window.location.href;
      let result = url.match(regExp);
      if (result) {
        return decodeURIComponent(result[2]);
      }
      return null;
    },
    async login() {
      console.log(this.deviceId, 'this.deviceId');

      let pass = await this.$validator.validate();
      if (!pass) return;

      // 短信验证
      let requestParams = {
        scope: 'basic',
        grant_type: 'password',
        login_type: 'phone',
        username: this.phoneCode + this.phone,
        pwd_type: 'secret',
        password: this.password,
        device_id: this.$route.query.deviceId ? this.$route.query.deviceId : '80d00dc13823e4373b02268d66412d13',
      };
      if (this.loginType === 'code') {
        requestParams = {
          scope: 'basic',
          grant_type: 'password',
          login_type: 'phone',
          username: this.phoneCode + this.phone,
          pwd_type: 'sms_code',
          password: this.vcode + '',
          device_id: this.$route.query.deviceId ? this.$route.query.deviceId : '80d00dc13823e4373b02268d66412d13',
        };
      }
      console.log('login >>>>>>>>>> params', requestParams);
      const res = await login(qs.stringify(requestParams), this);
      if (res.code !== 200) {
        Toast({ content: res.msg })
        return;
      }
      // this.$cookies.set('access_token', `${res.data.token_type} ${res.data.access_token}`,{
      //   path: '/',
      //   maxAge: 60 * 60 * 2
      // });
      console.log('login >>>>>>>>>> res', res);
      const { token_type, access_token } = res.data;
      sessionStorage.setItem('access_token', `${token_type} ${access_token}`);
      this.$router.push('/');
    },
  },
};
</script>

<style lang="scss" scoped>
.login {
  margin-top: 20px;
  padding: 0 28px 40px;
  min-height: calc(100vh - 40px);

  ::v-deep.md-input-item-msg,
  ::v-deep.md-textarea-item-msg {
    margin-left: 0 !important;
  }

  .logo {
    display: block;
    width: 300px;
    margin: 0 auto;
    text-align: center;
    padding: 84px 0;
  }

  .logo-hr {
    height: 2px;
    @include background_color(line-color);
  }

  .mark-title {
    padding-top: 40px;
    padding-bottom: 40px;
    .mark {
      vertical-align: middle;
      display: inline-block;
      width: 8px;
      height: 36px;
      background: #1d4ebb;
      margin: 8px 20px 8px 0;
    }

    .text {
      vertical-align: middle;
      display: inline-block;
      color: $primary-color;
      font-size: 36px;
      font-weight: 500;
    }
  }

  .mobile-phone {
    position: relative;
    width: 100%;
  }

  .select {
    display: inline-block;
    position: absolute;
    left: 0;
    width: 254px;
    height: 88px;
    margin: 0 60px 0 0;
    text-align: center;
    vertical-align: top;
    font-size: 30px;
    // line-height: 84px;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    @include border_color(input-line);
    border-radius: 6px;

    ::v-deep.md-field-item-placeholder {
      font-size: 28px;
      color: #7f859d;
    }

    ::v-deep.md-field-item-control {
      padding-left: 10px;
      font-size: 28px;
    }

    ::v-deep .md-icon-arrow-down {
      font-size: 20px;
      margin-right: 10px;
    }
  }
  .password {
    margin-top: 20px;
  }
  .phone {
    display: block;
    margin-left: 270px;
  }

  .input-operator {
    color: #3773e1;
  }

  .md-field {
    padding: 0;
  }

  ::v-deep.md-field-item-content {
    padding-top: 0;
    padding-bottom: 0;
  }

  .btn {
    margin-top: 80px;
  }
}
</style>
