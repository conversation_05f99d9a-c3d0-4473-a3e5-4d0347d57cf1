<template>
  <div class="private-equity-fund-page" :class="[theme]">
    <div v-if="privateEquityList && privateEquityList.length" class="private-equity-data">
      <div class="private-equity-header-bg">
        <img :src="require(`@/assets/images/underlining_${theme}.png`)" alt="" class="header-bg-image">
        <div class="header-bg-title">{{ $t('privateEquity.privateEquity') }}</div>
      </div>
      <ul class="private-equity-ul">
        <template v-for="(item, index) in privateEquityList">
          <li class="private-equity-li" @click="goPrivateEquityDetails(item)" :key="index">
            <div class="private-equity-data-image">
              <div class="company-message">
                <div class="company-photo-title">
                  <div class="company-photo" v-if="item.logoUrl">
                    <img :src="item.logoUrl" alt="">
                    <!-- <img src="https://middleware-fund-develop-public.oss-cn-shenzhen.aliyuncs.com/6a6820ef-85ec-43a5-b95b-bcdbf20aeb30.png?t=icon_jjph.png" alt=""> -->
                  </div>
                  <div class="company-title-box">
                    <div class="company-title">{{ item.agencyName || '--' }}</div>
                    <div class="company-tags">
                      <span v-if="item.slogan">{{ item.slogan }}</span>
                    </div>
                  </div>
                </div>
                <div class="company-abstract">{{ item.agencyIntroduction || '--' }}</div>
              </div>
            </div>
            <div class="private-equity-data-content">
              <div class="product-header">
                <span class="product-name-zh">
                  {{ $t('privateEquity.privateEquityProduct') }}
                  <img class="right-top-icon" :src="require(`@/assets/images/circle_${theme}.png`)" alt="">
                </span>
              </div>
              <div class="product-message-box">
                <div class="product-name">{{ item.name || '--' }}</div>
                <div class="product-tax-rate">{{ item.historicalPerformance || '--' }}</div>
              </div>
            </div>
          </li>
        </template>
      </ul>
      <private-equity-footer></private-equity-footer>
    </div>
    <empty-component v-else></empty-component>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import EmptyComponent from '@/components/empty-component.vue'
import privateEquityFooter from '@/components/footer/privateEquityFooter.vue'
import { fundsPageList } from '@/services/privateEquity'
import Loading from '@/components/loading/index.vue'

export default {
  components: {
    EmptyComponent,
    privateEquityFooter,
    Loading
  },
  data () {
    return {
      privateEquityList: [],
      isLoading: false,
      pageNum: 0,
      pageSize: 20,
    }
  },
  computed: mapState(['theme']),
  methods: {
    goPrivateEquityDetails(data) {
      this.$router.push({
        path: '/privateEquityFundDetails',
        query: { id: data.privatelyOfferedFundId }
      })
    },
    goBack() {
      this.$jsBridge.run('navBack', { isClosePage: true })
    },
  },
  mounted() {
    this.isLoading = true
    fundsPageList({
      page: this.pageNum,
      size: this.pageSize
    }).then(res => {
      if (res.code == 200) {
        this.privateEquityList = res.data.content
        this.isLoading = false
      }
    })
  } 
}
</script>

<style lang="scss" scoped>
.private-equity-fund-page {
  padding-top: 40px;
  min-height: 100vh;
  background: #FFFFFF;
  .private-equity-data {
    .private-equity-header-bg {
      position: relative;
      .header-bg-image {
        width: 100%;
        height: 204px;
      }
      .header-bg-title {
        color: #012169;
        font-size: 56px;
        font-weight: bold;
        position: absolute;
        left: 62px;
        bottom: 24px;
      }
    }
    .private-equity-ul {
      color: #4B596B;
      padding: 40px 32px;
      font-size: 24px;
      .private-equity-li {
        margin-top: 40px;
        border-radius: 20px;
        padding: 40px 32px;
        background: #F7F8FA;
        &:first-child {
          margin-top: 0;
        }
        .private-equity-data-image {
          .company-message {
            padding-bottom: 32px;
            border-bottom: 1px solid #E5E6EB;
            .company-photo-title {
              display: flex;
              justify-content: flex-start;
              align-items: flex-start;
              .company-photo {
                img {
                  width: 96px;
                  height: 96px;
                  margin-right: 24px;
                  border-radius: 50%;
                  overflow: hidden;
                  background: blueviolet;
                }
              }
              .company-title-box {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                .company-title {
                  color: #1C212A;
                  font-size: 32px;
                  font-weight: bold;
                  line-height: 48px;
                }
                .company-tags {
                  margin-top: 8px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: flex-end;
                  flex-wrap: wrap;
                  span {
                    color: #FF9900;
                    font-size: 24px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    padding: 8px 16px;
                    background: #FFF0D6;
                    border-radius: 10px;
                    margin-bottom: 24px;
                  }
                }
              }
            }
            .company-abstract {
              width: 99%;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break:break-all;
              display: -webkit-box;
              -webkit-line-clamp: 3;  
              -webkit-box-orient: vertical;
              font-family: D-DINExp, D;
              color: #4B596B;
              font-size: 28px;
              line-height: 44px;
              // text-align: center;
              margin-top: 30px;
            }
          }
        }
        .private-equity-data-content {
          padding-top: 30px;
          font-size: 28px;
          color: #4B596B;
          .product-header {
            color: #1C212A;
            font-size: 32px;
            font-weight: bold;
            text-align: left;
            margin-bottom: 12px;
            .product-name-zh {
              position: relative;
              .right-top-icon {
                width: 36px;
                height: 36px;
                position: absolute;
                right: -15px;
                top: -15px;
              }
            }
          }
          .product-message-box {
            padding: 24px;
            border-radius: 10px;
            background: #FFFFFF;
            .product-name {
              width: 99%;
              padding: 0 0 16px;
              font-size: 28px;
              line-height: 48px;
              font-weight: bold;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .product-tax-rate {
              font-size: 24px;
              color: #83909D;
              line-height: 48px;
              width: 99%;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break:break-all;
              display: -webkit-box;
              -webkit-line-clamp: 3;  
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
  }
}
.dark.private-equity-fund-page {
  background: #1B1D28;
  .private-equity-data{
    .private-equity-header-bg {
      .header-bg-title {
        color: #fff;
      }
    }
    .private-equity-ul {
      .private-equity-li {
        background: #05060A;
        .company-message {
          border-bottom: 1px solid #22262C;
          .company-photo-title {
            .company-title-box {
              .company-title {
                color: #F0F3F7;
              }
              .company-tags {
                span {
                 color: #FF9900;
                 background: #4C2F19;
                }
              }
            }
          }
          .company-abstract {
            color: #B6C2D0;
          }
        }
        .product-header {
          color: #F0F3F7;
        }
        .product-message-box {
          color: #B6C2D0;
          background: #1B1D28;
          .product-tax-rate {
            color: #8693A0;
          }
        }
      }
    }
  }
}
</style>
