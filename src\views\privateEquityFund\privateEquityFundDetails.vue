<template>
  <div class="private-equity-fund-details-page" :class="[theme]">
    <div class="details-content">
      <div class="product-header-message">
        <div class="product-logo" v-if="privateEquityFundDetails && (privateEquityFundDetails.blackLogoUrl || privateEquityFundDetails.whiteLogoUrl)">
          <img :src="privateEquityFundDetails && (theme == 'dark' ? privateEquityFundDetails.blackLogoUrl : privateEquityFundDetails.whiteLogoUrl)" alt="">
        </div>
        <h3>{{ privateEquityFundDetails && privateEquityFundDetails.agencyName ? privateEquityFundDetails.agencyName : '--' }}</h3>
        <span>
          {{ $t('privateEquity.investmentLightspot') }}
          <img src="@/assets/images/arrow1_left_disabled.png" alt="">
        </span>
        <ul v-if="investmentHighlightLineFeed && investmentHighlightLineFeed.length">
          <!-- privateEquityFundDetails.Slogan -->
          <li v-for="item in investmentHighlightLineFeed" :key="item">{{ item }}</li>
        </ul>
      </div>
      <div class="organization-introduce">
        <label-header :labelTitle="$t('privateEquity.organizationIntroduce')"></label-header>
        <div class="content">
          <p>{{ privateEquityFundDetails && privateEquityFundDetails.agencyIntroduction ? privateEquityFundDetails.agencyIntroduction : '--' }}</p>
        </div>
      </div>
      <div class="product-introduce">
        <label-header :labelTitle="$t('privateEquity.productIntroduce')"></label-header>
        <ul class="product-name-type">
          <li class="product-code-message">
            <div class="product-code-title-box">
              <span>{{ (privateEquityFundDetails && privateEquityFundDetails.currencyType) ? privateEquityFundDetails.currencyType : '--' }}</span>
              <span>{{ (privateEquityFundDetails && privateEquityFundDetails.name) ? privateEquityFundDetails.name : '--' }}</span>
            </div>
            <div class="product-history">
              <p>{{ $t('privateEquity.historyExpression') }}</p>
              <p>{{ (privateEquityFundDetails && privateEquityFundDetails.historicalPerformance) ? privateEquityFundDetails.historicalPerformance : '--' }}</p>
            </div>
          </li>
          <li class="product-label-li">
            <div class="product-header">
              <span class="product-name-zh">
                {{ $t('privateEquity.fundElement') }}
                <img class="right-top-icon" :src="require(`@/assets/images/circle_${theme}.png`)" alt="">
              </span>
            </div>
            <div class="product-table-box">
              <template v-for="item in fundElement">
                <tr class="product-table" :key="item.key" v-if="(item.isShow || ['fundStructure', 'dividendCycle', 'propertyOccupancyRate', 'repo'].includes(item.key)) && privateEquityFundDetails && privateEquityFundDetails[item.key]">
                  <td class="table-left">{{ item.label && item.label == 'shortName' ? privateEquityFundDetails.shortName + $t('privateEquity.charge') : item.label }}</td>
                  <td class="table-right" v-if="item.key == 'netValueSettlementFrequency'">
                    {{ privateEquityFundDetails && privateEquityFundDetails[item.key] ? $t(`privateEquity.netValueSettlementFrequencyLable.${privateEquityFundDetails[item.key]}`) : '--' }}
                  </td>
                  <td class="table-right" v-if="item.key == 'dividendCycle'">
                    {{ privateEquityFundDetails && privateEquityFundDetails[item.key] ? $t(`privateEquity.dividendCycleLabel.${privateEquityFundDetails[item.key]}`) : '--' }}
                  </td>
                  <td class="table-right" v-if="item.key !== 'netValueSettlementFrequency' && item.key !== 'dividendCycle'">
                    {{ privateEquityFundDetails && privateEquityFundDetails[item.key] ? privateEquityFundDetails[item.key] : '--' }}
                  </td>
                </tr>
              </template>
            </div>
          </li>
          <li class="product-label-li">
            <div class="product-header">
              <span class="product-name-zh">
                {{ $t('privateEquity.investmentWay') }}
                <img class="right-top-icon" :src="require(`@/assets/images/circle_${theme}.png`)" alt="">
              </span>
            </div>
            <div class="product-investment-way">{{ (privateEquityFundDetails && privateEquityFundDetails.investmentMethod) ? privateEquityFundDetails.investmentMethod : '--' }}</div>
          </li>
          <li class="product-label-li">
            <div class="product-header">
              <span class="product-name-zh">
                {{ (privateEquityFundDetails && privateEquityFundDetails.channelName) ? privateEquityFundDetails.channelName : '--' }}
                <img class="right-top-icon" :src="require(`@/assets/images/circle_${theme}.png`)" alt="">
              </span>
            </div>
            <div class="product-investment-way">{{ privateEquityFundDetails && privateEquityFundDetails.channelIntroduction ? privateEquityFundDetails.channelIntroduction : '--' }}</div>
          </li>
        </ul>
      </div>
    </div>
    <private-equity-footer></private-equity-footer>
    <div class="private-botton">
      <md-button type="primary" round @click="subscribeDialog">{{ $t('router.transactionBuy') }}</md-button>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import LabelHeader from '@/components/privateEquityFund/label-header.vue'
import privateEquityFooter from '@/components/footer/privateEquityFooter.vue'
import { privateEquityDetails } from '@/services/privateEquity'
import Loading from '@/components/loading/index.vue'
import { Dialog, Toast } from 'mand-mobile';
export default {
  components: {
    LabelHeader,
    privateEquityFooter,
    Loading
  },
  data() {
    return {
      isLoading: false,
      fundElement: [
        { label: this.$t('privateEquity.assetSize'), key: 'assetSize', isShow: true },
        { label: 'shortName', key: 'charge', isShow: true },
        { label: this.$t('privateEquity.leverageRate'), key: 'leverageRate', isShow: true },
        { label: this.$t('privateEquity.netValueSettlementFrequency'), key: 'netValueSettlementFrequency', isShow: true },
        { label: this.$t('privateEquity.fundStructure'), key: 'fundStructure', isShow: false },
        { label: this.$t('privateEquity.dividendCycle'), key: 'dividendCycle', isShow: false },
        { label: this.$t('privateEquity.repo'), key: 'repo', isShow: false },
        { label: this.$t('privateEquity.propertyOccupancyRate'), key: 'propertyOccupancyRate', isShow: false },
        { label: this.$t('privateEquity.minInvestmentAmount'), key: 'minInvestmentAmount', isShow: true },
      ],
      privateEquityFundDetails: null,
      investmentHighlightLineFeed: []
    }
  },
  computed: mapState(['theme']),
  methods: {
    subscribeDialog() {
      const dialog = Dialog.alert({
        title: this.$t('privateEquity.hint'),
        content: this.$t('privateEquity.hintContent'),
        confirmText: this.$t('privateEquity.iSee'),
        closable: false,
        onConfirm: () => {},
      })
      const el = dialog.$el;
      el && el.classList.add('dialog-confirm-private');
    }
  },
  mounted() {
    this.isLoading = true
    let { id } = this.$route.query
    this.$nextTick(() => {
      privateEquityDetails(id).then(res => {
        if (res.code == 200) {
          console.log('res.data---------', res.data);
          this.privateEquityFundDetails = res.data
          this.investmentHighlightLineFeed =  (res.data && res.data.investmentHighlight && res.data.investmentHighlight.includes('\n')) ? res.data.investmentHighlight.split('\n') : (res.data.investmentHighlight ? [res.data.investmentHighlight] : [])
          this.isLoading = false
        }
      })
    })
  },
}
</script>

<style lang="scss" scoped>
.private-equity-fund-details-page {
  color: #012169;
  font-size: 28px;
  min-height: 100vh;
  background: #FFFFFF;
  .details-content {
    padding-bottom: 120px;
    .product-header-message {
      padding: 32px 32px 24px;
      .product-logo {
        width: 440px;
        height: 100px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      background: url('~@/assets/images/underlining2_light.png') no-repeat;
      // background-size: contain;
      background-size: cover;
      h3 {
        font-size: 56px;
        margin: 54px 0 68px;
      }
      span {
        // display: flex;
        // justify-content: flex-start;
        align-items: center;
        padding: 8px 56px 8px 32px;
        border-radius: 30px;
        color: #F1F3F7;
        font-size: 32px;
        background: #012169;
        margin-bottom: 24px;
        position: relative;
        img {
          width: 36px;
          height: 36px;
          position: absolute;
          right: 20px;
          top: 10px;
          margin-left: 20px;
        }
      }
      ul {
        margin-top: 24px;
        font-size: 26px;
        line-height: 52px;
        li {
          margin-left: 40px;
          list-style: disc;
        }
      }
    }
    .organization-introduce {
      padding: 0 32px;
      background: url('~@/assets/images/underlining3_light.png') no-repeat;
      // background-size: contain;
      background-size: cover;
      .content {
        color: #4B596B;
        font-size: 28px;
        line-height: 44px;
        p {
          margin-top: 40px;
          &:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .product-introduce {
      padding: 0 32px;
      .product-name-type {
        margin-top: 32px;
        .product-code-message {
          border-radius: 20px;
          color: #fff;
          padding: 28px 24px 24px;
          background: #012169;
          font-size: 24px;
          .product-code-title-box {
            span {
              &:first-child {
                color: #F1F3F7;
                font-size: 24px;
                padding: 4px 14px;
                border-radius: 6px;
                background: #3D63A5;
                margin-right: 16px;
              }
              &:first-child {
                font-size: 28px;
                line-height: 48px;
              }
            }
          }
          .product-history {
            font-size: 24px;
            color: #83909D;
            margin-top: 24px;
            padding: 24px;
            border-radius: 20px;
            background: #fff;
            p {
              line-height: 48px;
              &:first-child {
                color: #1C212A;
                font-weight: bold;
                font-family: PingFangSC-Semibold, PingFang SC;
              }
              &:last-child {
                font-family: D-DINExp, D;
              }
            }
          }
        }
        .product-label-li {
          padding-top: 48px;
          .product-header {
            // color: #1C212A;
            font-size: 32px;
            font-weight: bold;
            text-align: left;
            margin-bottom: 28px;
            .product-name-zh {
              position: relative;
              .right-top-icon {
                width: 36px;
                height: 36px;
                position: absolute;
                right: -15px;
                top: -15px;
              }
            }
          }
          .product-table-box {
            border: 1px solid #E5E6EB;
            border-radius: 20px;
            overflow: hidden;
            .product-table {
              font-size: 26px;
              color: #4B596B;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              background: #fff;
              border-bottom: 1px solid #E5E6EB;
              &:last-child {
                border-bottom: 0;
              }
              .table-left {
                width: 30%;
                border-right: 1px solid #E5E6EB;
              }
              .table-right {
                width: 70%;
                margin-left: -1px;
                border-left: 1px solid #E5E6EB;
              }
              .table-left, .table-right {
                display: flex;
                align-items: center;
                padding: 26px 18px;
              }
              &:last-child {
                .table-left, .table-right {
                  border-bottom: 0;
                }
              }
            }
          }
          .product-investment-way {
            color: #4B596B;
            font-size: 28px;
            line-height: 44px;
          }
        }
      }
    }
  }
  .private-botton {
    border-top: 1px solid #E5E6EB;
    background: #fff;
    padding: 16px 32px;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    .md-button {
      border-radius: 100px;
      font-size: 30px;
    }
    .md-button.primary {
      color: #F1F3F7;
      background: #012169;
      .md-button-inner {
        border: none;
      }
    }
  }
}

.dark  {
  .private-equity-fund-details-page {
    color: #F0F3F7;
    background: #1B1D28;
    .details-content {
      .product-header-message {
        background: url('~@/assets/images/underlining2_dark.png') no-repeat;
        background-size: cover;
      }
      .organization-introduce {
        background: url('~@/assets/images/underlining3_dark.png') no-repeat;
        background-size: cover;
        .content {
          color: #B6C2D0;
        }
      }
      .product-name-type {
        .product-code-message {
          background: #173A87;
          .product-history {
            background: #1B1D28;
            p {
              color: #8693A0;
              &:first-child {
                color: #F0F3F7;
              }
            }
          }
        }
        .product-label-li {
          .product-table-box {
            // background: #0C0F15;
            border: 1px solid #22262C;
            .product-table {
              color: #B6C2D0;
              border-bottom: 1px solid #22262C;
              background: #0C0F15;
              .table-left {
                border-right: 1px solid #22262C;
              }
              .table-right {
                margin-left: -1px;
                border-left: 1px solid #22262C;
              }
            }
          }
          .product-investment-way {
            color: #B6C2D0;
          }
        }
      }
    }
    .private-botton {
      border-top: 1px solid #22262C;
      background: #1B1D28;
      .md-button.primary {
        background: #173A87;
      }
    }
  }
}
</style>