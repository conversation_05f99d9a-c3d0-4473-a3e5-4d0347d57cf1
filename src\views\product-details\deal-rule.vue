<template>
  <div class="deal-rule-box">
    <ul class="deal-rule-select" v-if="(tradeRulesData && JSON.stringify(tradeRulesData) !== '{}')">
      <li class="deal-rule-tab">
        <div v-for="(item, index) in $t('fundMessage.dealRuleTabs')" :key="index" @click="checkType(item)"
             :class="{ activeType: dealRuleCurrent == item.name }">
          <div>{{ item.label }}</div>
          <div :key="index" class="bottom-border" :class="{ activeBorder: dealRuleCurrent == item.name }"></div>
        </div>
      </li>
      <template v-if="dealRuleCurrent != 3">
        <!-- 申购规则 -->
        <template v-if="dealRuleCurrent == 1">
          <div class="deal-rule-rate-border">
            <li class="deal-rule-rate-time">
              <div class="deal-rule-rate-time-list">
                <label>{{ $t('fundMessage.ruleData')[0].label }}</label>
                <span>
                  {{ dealRuleValue(tradeRulesData.minFirstInvestment,tradeRulesData.currency) }}
                </span>
              </div>
            </li>
            <li class="deal-rule-rate-time" :class="{'deal-rule-rate-time-border': showLadderCharge}">
              <div class="deal-rule-rate-time-list" :class="{'deal-rule-rate-time-border': showLadderCharge}">
                <label>{{ $t('fundMessage.ruleData')[1].label }}</label>
                <div class="discount">
                  <template v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                    <div class="ladder-charge">
                      <span>{{ $t("fundMessage.ladderCharge") }}</span>
                      <img :src="require(`@/assets/images/unfold_${showLadderCharge}.png`)" alt="" @click="showLadderCharge = !showLadderCharge">
                    </div>
                  </template>
                  <template v-else>
                    <div class="activeColor">
                      {{
                        tradeRulesData.subscriptionRatePreferential[0].rate ? (tradeRulesData.subscriptionRatePreferential[0].rate*100).toFixed(2) : (tradeRulesData.subscriptionRate[0].rate*100).toFixed(2)
                      }}%
                    </div>
                    <div class="line-through" v-if="tradeRulesData.subscriptionRatePreferential[0].rate">
                      {{ tradeRulesData.subscriptionRate[0].rate ? (tradeRulesData.subscriptionRate[0].rate*100).toFixed(2) + '%' : '0.00' }}
                    </div>
                  </template>
                </div>
                <!-- <div v-else  class="discount">
                  <div class="activeColor">
                    {{ (tradeRulesData.subscriptionRate*100).toFixed(2) }}%
                  </div>
                  <div class="line-through"></div>
                </div> -->
              </div>
              <!--  阶梯式费率 -->
              <div class="ladder-charge-table" v-if="showLadderCharge">
                <ul>
                  <li>
                    <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                    <div>{{ $t("myAccount.purchaseRates") }}</div>
                  </li>
                  <template v-if="showLodder()">  
                    <li v-for="(item, index) in tradeRulesData.subscriptionRatePreferential" :key="index">
                      <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                      <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                    </li>
                  </template>
                  <template v-else>
                    <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index">
                      <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                      <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                      <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                    </li>
                  </template>
                </ul>
              </div>
            </li>
            <li class="deal-rule-rate-time">
              <div class="deal-rule-rate-time-list">
                <label>{{ $t('fundMessage.ruleData')[2].label }}</label>
                <span>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t('common.tradingDay')}}</span>
              </div>
            </li>
          </div>
        </template>
        <!-- 赎回规则 -->
        <template v-else>
          <li class="deal-rule-rate-time deal-rule-rate-top">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[0].label }}</label>
              <span>
                {{ dealRuleValueRedem(tradeRulesData.minRansom) }}
              </span>
            </div>
          </li>
          <li class="deal-rule-rate-time">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[1].label }}</label>
              <div v-if="tradeRulesData.redemptionRatePreferential" class="discount">
                <div class="activeColor">
                  {{
                    (tradeRulesData.redemptionRatePreferential*100).toFixed(2)
                  }}%
                </div>
                <div class="line-through">
                  {{ (tradeRulesData.redemptionRate*100).toFixed(2) }}%
                </div>
              </div>
              <div v-else>
                <div class="activeColor">
                  {{ (tradeRulesData.redemptionRate*100).toFixed(2) }}%
                </div>
                <div class="line-through"></div>
              </div>
            </div>
          </li>
          <li class="deal-rule-rate-time">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[2].label }}</label>
              <span>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t('common.tradingDay')}}</span>
            </div>
          </li>
          <li class="deal-rule-rate-time">
            <div class="deal-rule-rate-time-list">
              <label>{{ $t('fundMessage.redeemData')[3].label }}</label>
              <span>T+{{ tradeRulesData.redemptionPlusDay }}{{ $t('common.tradingDay')}}</span>
            </div>
          </li>
        </template>
        <li class="deal-rule-rate-time deal-rule-rate-top">
          <DealRuleComponent :dealRuleCurrent="dealRuleCurrent" :shareConfirmedDay="shareConfirmedDay" :toTheAccount="toTheAccount" :endOfTradingDay="tradeRulesData.endOfTradingDay"></DealRuleComponent>
          <div class="rule-describe">
            <p>{{ $t('fundMessage.subscribeDescribe.content', { time: tradeRulesData.endOfTradingDay }) }}</p>
            <p>{{ $t('fundMessage.subscribeDescribe.content1', { timeEnd: tradeRulesData.cancellationDeadline}) }}</p>
          </div>
        </li>
        <li class="deal-rule-rate-time deal-rule-rate-top">
          <div class="fund-rest">{{ $t('common.fundClose')}}</div>
          <div v-for="(item, index) in closeDayData" :key="index" class="fund-rest-list">
            {{ item.closeDate }}
          </div>
          <div class="all-list-data">
            <!-- <span v-if="dataTotal" @click="uploadData">{{
              $t('fundMessage.moreMessage')
            }}</span>
            <span v-else>{{ $t('fundMessage.updateMoreMessage') }}</span> -->
          </div>
        </li>
      </template>
      <!-- 费用说明 -->
      <template v-else>
        <li class="deal-rule-rate-time deal-rule-expense">
          <div class="deal-border-radius">
            <div class="expense-table expense-table-header">
              <p>{{ $t('fundMessage.expenseName') }}</p>
              <p>{{ $t('fundMessage.rate') }}</p>
            </div>
            <div class="expense-table">
              <p>{{ $t('fundMessage.subscriptionRate') }}</p>
              <p class="ladder-charge"  v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
                <span>{{ $t("fundMessage.ladderCharge") }}</span>
              </p>
              <p :class="{ activeColor: !tradeRulesData.subscriptionRate[0].rate }" class="subscription-rate" v-else>
                {{
                  tradeRulesData.subscriptionRatePreferential[0].rate ? formatRate(tradeRulesData.subscriptionRatePreferential[0].rate) :
                  tradeRulesData.subscriptionRate[0].rate ? formatRate(tradeRulesData.subscriptionRate[0].rate) : '--'
                }}
              </p>
            </div>
            <div class="expense-table">
              <p>{{ $t('fundMessage.redemptionRate') }}</p>
              <p :class="{ activeColor: !tradeRulesData.redemptionRate }">
                {{
                  tradeRulesData.redemptionRate
                    ? formatRate(tradeRulesData.redemptionRate)
                    : '--'
                }}
              </p>
            </div>
            <div class="expense-table">
              <p>{{ $t('fundMessage.platformFeeRate') }}</p>
              <p v-if="tradeRulesData.platformFeeRate.length > 1" class="ladder-color">{{ $t("fundMessage.ladderCharge") }}</p>
              <p :class="{ activeColor: !tradeRulesData.platformFeeRate[0].rate }" v-else>
                {{
                  tradeRulesData.platformFeeRate[0].rate
                    ? formatRate(tradeRulesData.platformFeeRate[0].rate)
                    : '--'
                }}
              </p>
            </div>
            <div class="expense-table">
              <p>{{ $t('fundMessage.managementFeeRate') }}</p>
              <p :class="{ activeColor: !tradeRulesData.managementFeeRate }">
                {{
                  tradeRulesData.managementFeeRate
                    ? formatRate(tradeRulesData.managementFeeRate)
                    : '--'
                }}
              </p>
            </div>
          </div>
          <!--  阶梯式费率 -->
          <div class="ladder-charge-table-title" v-if="isShowTitle">{{ $t("myAccount.ladderChargeTableTitle") }}:</div>
          <div class="ladder-charge-table-box" v-if="showLodder() || (tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate)">
            <div class="ladder-charge-table">
              <ul>
                <li>
                  <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                  <div>{{ $t("myAccount.purchaseRates") }}</div>
                </li>
                <template v-if="showLodder()">  
                  <li v-for="(item, index) in tradeRulesData.subscriptionRatePreferential" :key="index">
                    <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                    <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                  </li>
                </template>
                <template v-else>
                  <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index">
                    <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                    <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                  </li>
                </template>
              </ul>
            </div>
          </div>
          <div class="ladder-charge-table-box" v-if="tradeRulesData.platformFeeRate.length > 1">
            <div class="ladder-charge-table">
                <ul>
                  <li>
                    <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
                    <div>{{ $t("myAccount.platformFeeRate") }}</div>
                  </li>
                  <li v-for="(item, index) in tradeRulesData.platformFeeRate" :key="index">
                    <div v-if="item.from==0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                    <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                    <div>{{ (item.rate * 100).toFixed(2) }}%</div>
                  </li>
                </ul>
              </div>
          </div>
          <div class="fees-contnet" v-if="renderFees">
                <p class="fees-title">{{ $t('fundMessage.fees') }}</p>
                <p v-html="renderFees"></p>
              </div>
          <div class="expense-remask">
            {{ $t('fundMessage.expenseRemark') }}
          </div>
          <!-- 费率规则 -->
          <div class="charge-rule">
            <ul>
              <li v-for="(item, index) in $t('fundMessage.ladderChargeLabelArr')" :key="index">
                {{item}}
              </li>
            </ul>
          </div>
        </li>
      </template>
    </ul>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import DealRuleComponent from '@/components/dealRuleComponent'
import Loading from '@/components/loading/index'
import { tradeRules, performance } from '@/services/fund'
import { getClosedDays } from '@/services/account'

import moment from 'moment'
import { fmoney } from '@/utils/util.js'
import { mapState } from "vuex";
export default {
  components: {
    DealRuleComponent,
    Loading,
  },
  data() {
    return {
      isLoading: true,
      tradeRulesData: {},
      dealRuleCurrent: 1,
      shareConfirmedDay: null,
      toTheAccount: 0,
      closeDayData: [],
      dataTotal: false,
      pageNum: 0,
      pageSize: 365,
      showLadderCharge: false,
      langObj: {
        'en': 'us',
        'zh-hans': 'cn',
        'zh-hant': 'hk'
      }
    }
  },
  computed: {
    ...mapState(["locale"]),
    renderFees() {
      let lang = this.langObj[this.locale]
      return this.tradeRulesData?.disclosedContent?.[lang] ?? ''
    },
    isShowTitle() {
      const { subscriptionRatePreferential, subscriptionRate, platformFeeRate } = this.tradeRulesData;
      const hasPreferentialRates = subscriptionRatePreferential?.length > 1 && subscriptionRatePreferential[0]?.rate;
      const hasRegularRates = subscriptionRate?.length > 1 && subscriptionRate[0]?.rate;
      const hasPlatformFeeRate = platformFeeRate?.length > 1;
      return hasPreferentialRates || hasRegularRates || hasPlatformFeeRate;
    }
  },
  methods: {
    formatRate(item) {
      return fmoney(item*100,2) + '%'
    },
    checkType(data) {
      // tabs
      this.dealRuleCurrent = data.name;
      this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
      if(data.name === 1) {
        this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
      }else {
        this.toTheAccount = this.tradeRulesData.redemptionPlusDay
      }
    },
    dealRuleValue(item1, currency) {
      if (item1 && currency == 'HKD') {
        return fmoney(item1,2) + this.$t('myAccount.' + 'HKDollar')
      } else if(item1 && currency == 'USD'){
        return fmoney(item1,2) + this.$t('myAccount.' + 'dollar')
      } else {
        return '0.00'
      }
    },
    dealRuleValueRedem(item1) {
      if (item1) {
        return fmoney(item1,4) + this.$t('myAccount.copies')
      } else {
        return '0.00' + this.$t('myAccount.copies')
      }
    },
    performance() {
      this.isLoading = true;
      let params = {
        page: this.pageNum, 
        size: this.pageSize,
        closeDateEnd: (new Date().getFullYear() + '-12-31'),
        closeDateStart: (new Date().getFullYear() + '-01-01')
      }
      getClosedDays(
        params,
        this,
        this.$route.query.id,
        this.$jsBridge.isSupported('getAppInfo')
      ).then((res) => {
        // 基金休息日
        // console.log('基金休息日====', res);
        if (res.code == 200) {
          if(res.data.totalElements <= this.pageSize) {
            this.dataTotal = false;
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
          }else {
            this.closeDayData = res.data.content;
            this.closeDayData.reverse();
            this.dataTotal = true;
          }
          this.isLoading = false
        }
      })
    },
    uploadData() {
      // 加载更多
      this.pageSize = this.pageSize + 15;
      this.performance()
    },
    showLodder () {
      if (this.tradeRulesData.subscriptionRatePreferential && (this.tradeRulesData.subscriptionRatePreferential.length > 1) && this.tradeRulesData.subscriptionRatePreferential[0].rate) {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.dealRuleCurrent = this.$route.query.type ?  parseInt(this.$route.query.type) : 1
    tradeRules(this.$route.query.id).then((res) => {
      if (res.code == 200) {
        // console.log("详情数据========", res.data.subscriptionRate.length > 0);
        let timeData = res.data.endOfTradingDay.split(':');
        res.data.endOfTradingDay = timeData[0] + ':' + timeData[1];
        let timeEndData = res.data.cancellationDeadline.split(':');
        res.data.cancellationDeadline = timeEndData[0] + ':' + timeEndData[1];
        this.tradeRulesData = res.data
        this.shareConfirmedDay = this.tradeRulesData.confirmationPlusDay;
        this.toTheAccount = this.tradeRulesData.moneyConfirmedPlusDay;
      }
      this.isLoading = false
    })
    this.performance()
  },
}
</script>

<style lang="scss" scoped>
.deal-rule-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    // background: themed("background-color");
    background: themed("bg-color-base");
    color: themed("text-color");
  }
  .deal-rule-select {
    // @include background_color(bg-color);
    @include font_color(second-text);
    .deal-rule-tab {
      @include background_color(bg-color);
      padding: 22px 30px 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28px;
      li {
        margin-right: 50px;
        // &:last-child {
        //   margin-right: 0;
        // }
      }
      .bottom-border {
        margin: 8px auto 0;
        width: 16px;
        height: 5px;
        border-radius: 3px;
      }
      .activeType {
        text-align: center;
        @include font_color(text-color);
        .activeBorder {
          @include background_color(fund-bg);
        }
      }
    }
    .deal-rule-rate-border {
      padding: 0 30px;
      @include background_color(second-bg);
      .deal-rule-rate-time {
        padding: 0;
        border-bottom: 1px solid;
        @include border_color(line-color);
        // margin-top: 20px;
        &:last-child {
          border-bottom: 0;
        }
        .ladder-charge-table {
          ul {
            // margin: 10px 0;
            border: 2px solid;
            // border-top: 0;
            // border-bottom: 0;
            @include border_color(bg-color-base1);
            @include background_color(bg-color-bg);
            border-radius: 10px;
            li {
              font-size: 24px;
              font-weight: 400;
              @include font_color(text-color);
              // height: 80px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              &:first-child {
                font-weight: 500;
                @include background_color(bg-color-base1);
              }
              div {
                width: 70%;
                padding: 28px 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                &:last-child {
                  width: 30%;
                  border-left: 2px solid;
                  @include border_color(bg-color-base1);
                }
              }
            }
          }
        }
      }
      .deal-rule-rate-time-border {
        border: 0;
      }
    }
    .deal-rule-rate-time {
      padding: 0 30px;
      @include background_color(second-bg);
      @include font_color(text-color);
      font-size: 28px;
      .deal-rule-rate-time-list {
        display: flex;
        justify-content: space-between;
        align-items: top;
        padding: 36px 0;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border: 0;
        }
        .discount {
          display: flex;
          justify-content: space-between;
          align-items: top;
          .ladder-charge {
            width: 100%;
            margin: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            span {
              @include font_color(text-red);
              font-size: 26px;
            }
            img { 
              width: 24px;
              height: 24px;
              margin-left: 15px;
            }
          }
        }
        .activeColor {
          @include font_color(error-color);
          // margin-right: 20px;
          max-width: 200px;
          text-align: center;
        }
        .line-through {
          text-decoration: line-through;
          text-align: center;
          max-width: 200px;
          margin-left: 20px;
        }
      }
      .deal-rule-rate-time-border {
        border: 0;
      }
      .rule-describe {
        font-size: 26px;
        line-height: 39px;
        padding: 41px 0 40px;
        p {
          &:last-child {
            margin-top: 50px;
          }
        }
      }
      .fund-rest {
        font-size: 34px;
        font-weight: bold;
        padding: 28px 0;
      }
      .fund-rest-list {
        font-size: 26px;
        padding: 27px 0;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border: 0;
        }
      }
      .expense-table {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        border-top: 0;
        border-bottom: 1px solid;
        @include border_color(lam-bg);
        p {
          width: 50%;
          text-align: center;
          padding: 20px;
          &:last-child {
            border-left: 1px solid;
            @include border_color(lam-bg);
          }
          @include background_color(second-bg);
        }
        .ladder-color {
          @include font_color(text-red);
        }
        .subscription-rate {}
        .ladder-charge {
          margin: 0;
          span {
            @include font_color(text-red);
            font-size: 26px;
          }
          img { 
            width: 24px;
            height: 24px;
            margin-left: 15px;
          }
        }
      }
      .expense-table-header {
        p {
          // height: 80px;
          text-align: center;
          // line-height: 80px;
          border: 1px solid;
          border-left: 0;
          border-right: 0;
          border-bottom: 0;
          @include border_color(lam-bg);
          @include background_color(lam-bg);
          @include font_color(text-color);
        }
      }
      .fees-contnet {
        margin-top: 24px;
        .fees-title {
          color: var(--text_1st);
          font-weight: bold;
          margin-bottom: 24px;
        }
      }
      .expense-remask {
        font-size: 24px;
        line-height: 42px;
        margin-top: 80px;
        @include font_color(second-text);
      }
      .all-list-data {
        padding: 27px 0;
        text-align: center;
        @include font_color(second-text);
        img {
          width: 28px;
        }
      }
    }
    .deal-rule-rate-top {
      margin-top: 20px;
    }
    .deal-rule-expense {
      @include background_color(bg-color);
      .deal-border-radius {
        border-radius: 10px;
        border: 1px solid;
        border-top: 0;
        @include border_color(lam-bg);
        overflow: hidden;
      }
      .ladder-charge-table-title {
        margin: 50px 0 30px;
        font-size: 26px;
        font-weight: 600;
        @include font_color(text-color);
      }
      .ladder-charge-table-box {
        margin-top: 30px;
        .ladder-charge-table {
          ul {
            // margin: 10px 0;
            border: 2px solid;
            // border-top: 0;
            // border-bottom: 0;
            @include border_color(bg-color-base1);
            @include background_color(bg-color-bg);
            border-radius: 10px;
            li {
              font-size: 24px;
              font-weight: 400;
              @include font_color(text-color);
              // height: 80px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              &:first-child {
                font-weight: 500;
                @include background_color(bg-color-base1);
              }
              div {
                width: 70%;
                padding: 28px 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                &:last-child {
                  width: 30%;
                  border-left: 2px solid;
                  @include border_color(bg-color-base1);
                }
              }
            }
          }
        }
      }
      .charge-rule {
        margin-top: 30px;
        font-size: 24px;
        font-weight: 400;
        line-height: 42px;
        @include font_color(second-text);
      }
    }
  }
}
</style>
