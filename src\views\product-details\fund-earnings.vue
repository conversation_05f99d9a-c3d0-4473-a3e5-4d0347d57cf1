<template>
  <!-- 万元收益--分页数据页面 -->
  <div class="fund-earnings-box">
    <div class="fund-earnings-bg">
      <div class="fund-earnings-header">
        <span>{{ $t("fundMessage.timeText") }}</span>
        <span>{{ $t("fundMessage.earnings") }}</span>
      </div>
      <ul class="fund-earnings-data">
        <li
          v-for="(item, index) in fundTimeData"
          :key="index"
          class="fund-earnings-li"
        >
          <span>{{ item.profitDate }}</span>
          <!-- :class="Number(item.cumulative10Thousand) < 0 ? 'output' : (Number(item.cumulative10Thousand) == 0 ? '':'entry')" -->
          <span
            :class="
              klineTheme === 'redRiseGreenFall'
                ? Number(item.cumulative10Thousand) < 0
                  ? 'output'
                  : Number(item.cumulative10Thousand) == 0
                  ? 'second-text'
                  : 'entry'
                : Number(item.cumulative10Thousand) > 0
                ? 'output'
                : Number(item.cumulative10Thousand) == 0
                ? 'second-text'
                : 'entry'
            "
          >
            {{
              Number(item.cumulative10Thousand) > 0
                ? "+" + Number(item.cumulative10Thousand).toFixed(4)
                : Number(item.cumulative10Thousand).toFixed(4)
            }}
          </span>
        </li>
        <div
          @click="uploadData"
          class="fund-earnings-text"
          v-if="fundTimeData.length > 0"
        >
          <span>{{
            dataList
              ? $t("fundMessage.updateMoreMessage")
              : $t("fundMessage.moreMessage")
          }}</span>
        </div>
        <div v-else class="fund-earnings-text">
          {{ $t("fundMessage.notMessage") }}
        </div>
      </ul>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Loading from "@/components/loading/index";
import { performance } from "@/services/fund";
export default {
  props: {
    investDetails: {
      type: Boolean,
    },
  },
  components: {
    Loading,
  },
  data() {
    return {
      fundTimeData: [],
      dataList: false,
      pageNum: 0,
      pageSize: 15,
      isLoading: true,
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    uploadData() {
      this.performance();
      if (!this.dataList) {
        this.pageNum += 1;
        // this.pageSize += 15;
      }
    },
    performance() {
      this.isLoading = true;
      if (!this.dataList) {
        performance(
          { page: this.pageNum, size: this.pageSize },
          this.$route.query.id,
          "",
          "ten-thousand"
        ).then((res) => {
          // console.log('万元收益=====', res)
          if (res.code == 200) {
            if (this.fundTimeData.length > res.data.totalElements) {
              this.dataList = true;
            } else {
              this.fundTimeData = this.fundTimeData.concat(res.data.content);
            }
          }
          this.isLoading = false;
        });
      }
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.fund-earnings-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  .fund-earnings-bg {
    padding: 0 30px;
    @include background_color(second-bg);
  }
  .fund-earnings-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    @include font_color(second-text);
  }
  .fund-earnings-data {
    font-size: 26px;
    @include font_color(text-color);
    .fund-earnings-li {
      padding: 27px 0;
      border-bottom: 1px solid;
      @include border_color(line-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .entry {
        @include font_color(buy-color);
      }
      .output {
        @include font_color(sell-color);
      }
      .second-text {
        @include font_color(second-text);
      }
    }
    div {
      padding: 27px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 28px;
      }
    }
  }
  .fund-earnings-text {
    // margin-top: 53px;
    font-size: 24px;
    @include font_color(second-text);
  }
}
</style>
