<template>
  <div class="fund-possess-box">
    <ul class="fund-possess-select">
      <li class="fund-possess-tab">
        <div
          v-for="(item, index) in $t('fundMessage.chartTypeTabs')"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: chartCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: chartCurrent == item.name }"
          ></div>
        </div>
      </li>
    </ul>
    <template v-if="chartCurrent != 2">
      <div class="fund-possess-chart-box">
        <div class="canvas-title">
          {{ $t('fundMessage.propertyStructure') }}
        </div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.assets &&
              compositionsPie.assets.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.assetUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.assetWeek}`) }}
            <!-- weekText(compositionsPie.assetWeek) -->
          </div>
          <div class="myChart-box">
            <canvas id="assetName"></canvas>
            <ul>
              <li v-for="(item,index) in assetName_pie" :key="index">
                <span class="round" :style="{'background': assetName_color[index]}"></span>
                <span class="text">{{item.assetName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <img
            class="pic-empty"
            :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
            alt="pic"
          />
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
      <div class="fund-possess-chart-box">
        <div class="canvas-title">
          {{ $t('fundMessage.industryDistribute') }}
        </div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.sectors &&
              compositionsPie.sectors.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.sectorUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.sectorWeek}`) }}
            <!-- weekText(compositionsPie.sectorWeek) -->
          </div>
          <div class="myChart-box">
            <canvas id="sectorName"></canvas>
            <ul>
              <li v-for="(item,index) in sectorName_pie" :key="index">
                <span class="round" :style="{'background': sectorName_color[index]}"></span>
                <span class="text">{{item.sectorName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <img
            class="pic-empty"
            :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
            alt="pic"
          />
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
      <div class="fund-possess-chart-box">
        <div class="canvas-title">{{ $t('fundMessage.territory') }}{{ $t('fundMessage.distribute') }}</div>
        <template
          v-if="
            compositionsPie &&
              compositionsPie.regionals &&
              compositionsPie.regionals.length
          "
        >
          <div class="fund-possess-pie-update-time">
            {{ $t('fundMessage.updateTime') }}:&nbsp;{{
              compositionsPie.regionalUpdateDate
            }}&nbsp;{{ $t(`common.weekTime.${compositionsPie.regionalWeek}`) }}
            <!-- weekText(compositionsPie.regionalWeek) -->
          </div>
          
          <div class="myChart-box">
            <canvas id="regionalName"></canvas>
            <ul>
              <li v-for="(item,index) in regionalName_pie" :key="index">
                <span class="round" :style="{'background': sectorName_color[index]}"></span>
                <span class="text">{{item.regionalName}}</span>
                <span class="right-percent">{{(item.proportion*100).toFixed(2)}}%</span>
              </li>
            </ul>
          </div>
        </template>
        <div v-else class="invest-chart-height1">
          <img
            class="pic-empty"
            :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
            alt="pic"
          />
          <div>{{ $t('fundMessage.notMessage') }}</div>
        </div>
      </div>
    </template>
    <template v-else>
      <div
        v-if="
          compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length
        "
        class="entrepot-number"
      >
        <holdPosition
          :chartTitle="true"
          :compositionsPie="compositionsPie"
        ></holdPosition>
      </div>
      <div v-else class="invest-chart-height1">
        <img
          class="pic-empty"
          :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
          alt="pic"
        />
        <div>{{ $t('fundMessage.notMessage') }}</div>
      </div>
    </template>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import F2 from '@antv/f2'
import { mapState } from 'vuex'
import holdPosition from '@/components/fundComponent/hold-position.vue'
import Loading from '@/components/loading/index'
import { fundCompositions } from '@/services/fund'
export default {
  components: {
    holdPosition,
    Loading,
  },
  data() {
    return {
      chartCurrent: 1,
      // typeTabs: [
      //   { name: 1, label: '投资分布' },
      //   { name: 2, label: '十大持仓' },
      // ],
      columnFundIsin: '',
      compositionsPie: {},
      isLoading: true,
      assetName_pie: [],
      assetName_color: [ '#2D60E0', '#284AA1', '#304761', '#00C2AC', '#0B8A7F', '#184B4C', ],
      sectorName_pie: [],
      sectorName_color: [ '#2D60E0', '#517DF7', '#7155F6', '#BB72DD', '#EC8A01', '#F1C243', ],
      regionalName_pie: [],
    }
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    checkType(data) {
      // console.log('切换基金种类', data);
      this.isLoading = true
      this.chartCurrent = data.name
      if (data.name == 2) {
        this.isLoading = false
      } else {
        this.fundPieCloumn(this.$route.query.id)
      }
    },
    compare(property) {
      // 排序
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value2 - value1
      }
    },
    chartPie(ID, Data, chartColor) {
      // console.log(ID, Data, chartColor)
      if (Data && Data.length) {
        Data = Data.sort(this.compare('proportion'))
        let residue_data = Data.slice(5, Data.length)
        Data = Data.slice(0, 5)
        let residue_proportion = 0;
        switch (ID) {
          case 'assetName':
            this.assetName_pie = Data.slice(0, 5) // 资产结构
            break;
          case 'sectorName':
            this.sectorName_pie = Data.slice(0, 5) // 行业分布
            break;
          case 'regionalName':
            this.regionalName_pie = Data.slice(0, 5) // 地域分布
            break;
          default:
            break;
        }
        if (residue_data && residue_data.length) {
          residue_data.forEach((item) => {
            residue_proportion += Number(item.proportion);
          })
          // 要处理--length > 5 时才会显示 其他状态
          let object = {
            proportion: residue_proportion + ''
          }
          object[ID] = this.$t('fundMessage.other')
          this[ID + '_pie'] = this[ID + '_pie'].concat(object)
          // console.log(this[ID + '_pie'] + '====', this[ID + '_pie']);
        }
        Data.forEach(function (obj) {
          // 对 proportion 字段数字化处理
          obj.proportion = Math.abs(Number(obj.proportion))
        })
      }
      
      // newArr.forEach(item => {
      //   map[item.assetName] = item.proportion + '%'
      // })
      // console.log(map);

      const chart = new F2.Chart({
        id: ID,
        pixelRatio: window.devicePixelRatio,
        width: 180,
        height: 150,
        padding: 0,
      })
      chart.source(Data) // 不要在动这个代码了
      // chart.tooltip(false);
      chart.legend(
        false
        // {
        //   position: 'right',
        //   itemFormatter: function itemFormatter(val) {
        //     return val + '       ' + map[val]
        //   },
        // }
      )
      chart.coord('polar', {
        transposed: true,
        innerRadius: 0.7,
        radius: 0.85,
      })
      chart.axis(false) // 去除坐标轴
      chart
        .interval()
        .position('1*proportion')
        .color(ID, chartColor)
        .adjust('stack')
      chart.render()
    },
    fundPieCloumn(ID) {
      this.isLoading = true
      fundCompositions(ID, '').then((res) => {
        if (res.code == 200) {
          if (this.chartCurrent == 1) {
            this.compositionsPie = res.data
            this.isLoading = false
            this.$nextTick(() => {
              if (res.data && res.data.assets && res.data.assets.length) {
                this.chartPie('assetName', res.data.assets, this.assetName_color)
              }
              if (res.data && res.data.sectors && res.data.sectors.length) {
                this.chartPie('sectorName', res.data.sectors, this.sectorName_color)
              }
              if (res.data && res.data.regionals && res.data.regionals.length) {
                this.chartPie('regionalName', res.data.regionals, this.sectorName_color)
              }
            })
          }
        }
      })
    },
    // weekText(data) {
    //   let text
    //   switch (data) {
    //     case 'MONDAY':
    //       text = '周一'
    //       break
    //     case 'TUESDAY':
    //       text = '周二'
    //       break
    //     case 'WEDNESDAY':
    //       text = '周三'
    //       break
    //     case 'THURSDAY':
    //       text = '周四'
    //       break
    //     case 'FRIDAY':
    //       text = '周五'
    //       break
    //     case 'SATURDAY':
    //       text = '周六'
    //       break
    //     case 'WEEKDAY':
    //       text = '周日'
    //       break
    //     default:
    //       break
    //   }
    //   return text
    // },
  },
  mounted() {
    // console.log(this.chartCurrent, this.$route.query);
    this.checkType(this.$t('fundMessage.chartTypeTabs')[0])
  },
}
</script>

<style lang="scss" scoped>
.fund-possess-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    background: themed('background-color');
    color: themed('text-color');
  }
  .fund-possess-select {
    @include background_color(bg-color);
    @include font_color(second-text);
    .fund-possess-tab {
      padding: 22px 30px 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28px;
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
      .bottom-border {
        margin: 8px auto 0;
        width: 16px;
        height: 5px;
        border-radius: 3px;
      }
      .activeType {
        text-align: center;
        @include font_color(text-color);
        .activeBorder {
          @include background_color(fund-bg);
        }
      }
    }
    .product-components {
      padding: 20px 30px 34px;
      height: 620px;
    }
  }
  .invest-chart-height1 {
    padding: 44px 0px 40px 13px;
    width: 100%;
    text-align: center;
    .pic-empty {
      width: 331px;
      height: 187px;
    }
    div {
      @include font_color(second-text);
      font-size: 28px;
      height: 72px;
      line-height: 72px;
      text-align: center;
    }
  }
  .fund-possess-chart-box {
    @include background_color(second-bg);
    padding: 0 30px;
    margin-top: 20px;
    &:first-child {
      margin-top: 0;
      padding-top: 64px;
    }
    .invest-chart-height1 {
      padding: 44px 0px 40px 13px;
      width: 100%;
      text-align: center;
      .pic-empty {
        width: 331px;
        height: 187px;
      }
      div {
        @include font_color(second-text);
        font-size: 28px;
        height: 72px;
        line-height: 72px;
        text-align: center;
      }
    }
    .canvas-title {
      @include font_color(text-color);
      font-size: 30px;
      font-weight: bold;
      line-height: 34px;
    }
    #myChart,
    #myChart1,
    #myChart2 {
      width: 100%;
      height: 100%;
    }
    .fund-possess-pie-update-time {
      font-size: 22px;
      @include font_color(second-text);
    }
    .myChart-box {
      height: 332px;
      position: relative;
      #assetName, #sectorName, #regionalName {
        position: absolute;
        left: -70px;
        top: 10px;
      }
      // overflow-y: auto;
      display: flex;
      justify-content: space-between;
      align-items: top;
      ul {
        position: absolute;
        right: 30px;
        top: 30px;
        height: 332px;
        // width: 428px;
        width: 55%;
        li {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          span {
            display: inline-block;
            height: 42px;
            line-height: 42px;
          }
          .round {
            width: 14px;
            height: 14px;
            border-radius: 50% 50%;
            margin-left: 10px;
          }
          .text {
            @include themeify {
              color: themed("second-text");
            }
            font-size: 22px;
            padding-left: 14px;
            padding-right: 17px;
            width: 70%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .right-percent {
            @include themeify {
              color: themed("second-text");
            }
            font-size: 22px;
            width: 20%;
            text-align: left;
          }
        }
      }
    }
  }
  .entrepot-number {
    padding: 0 30px;
    @include background_color(bg-color);
  }
}
</style>
