<template>
  <div class="fund-record-box">
    <ul class="fund-record-select">
      <li class="fund-record-tab">
        <div
          v-for="(item, index) in $t('fundMessage.fundRecordTabs')"
          :key="index"
          @click="checkType(item)"
          :class="{ activeType: fundRecordCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: fundRecordCurrent == item.name }"
          ></div>
        </div>
      </li>
    </ul>
    <template v-if="this.fundRecordCurrent == 1">
      <ul class="fund-record-list">
        <div class="fund-record-header">{{ $t('fundMessage.fundRecord') }}</div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundName') }}</label>
          <div class="fund-record-label-right">
            {{ fundsNameFun(managerData, 'name') }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>ISIN</label>
          <div class="fund-record-label-right">{{ managerData.fundIsin }}</div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundStockType') }}</label>
          <div class="fund-record-label-right">{{ managerData.stockType }}</div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundBondType') }}</label>
          <div class="fund-record-label-right">
            {{ $t(`fundMessage.bondType.${managerData.type}`) }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundEstablishPlace') }}</label>
          <div class="fund-record-label-right">
            {{ managerData.establishPlace }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t('fundMessage.fundEstablishTime') }}</label>
          <div class="fund-record-label-right">
            {{ managerData.establishTime }}
          </div>
        </div>
        <div class="fund-record-label">
          <label>{{ $t("fundMessage.fundCompany") }}</label>
          <div class="fund-record-label-right">
            {{ fundsNameFun(managerData, 'companyName') }}
          </div>
        </div>
      </ul>
      <ul class="fund-record-list">
        <div class="fund-record-header">
          {{ $t('fundMessage.fundManager') }}
        </div>
        <li class="fund-manager">
          <template v-if="managerData.managers && managerData.managers.length > 0">
            <p class="fund-manager-header">
              <span>{{ $t("fundMessage.username") }}</span>
              <span>{{ $t("fundMessage.tenure") }}</span>
              <span>{{ $t("fundMessage.tenureReportBack") }}</span>
            </p>
            <p
              class="fund-manager-content"
              v-for="(item, index) in managerData.managers"
              :key="index"
            >
              <span>{{ item.managerName }}</span>
              <span>
                <p>{{ item.arriveDate ? item.arriveDate : '-' }}{{item.arriveDate ? $t("fundMessage.sofar") : '-'}}</p>
                <p>{{ item.termDay ? item.termDay : '-' }}{{item.termDay ? $t("fundMessage.day") : '-'}}</p>
              </span>
              <span
                :class="
                  Number(item.yieldRate) > 0
                    ? 'colorUp'
                    : item.yieldRate
                      ? 'colorDown'
                      : 'colorDown1'
                "
              >
                {{
                  Number(item.yieldRate) > 0
                    ? '+' + item.yieldRate + '%'
                    : item.yieldRate
                      ? item.yieldRate + '%'
                      : '-.--'
                }}
              </span>
            </p>
          </template>
          <template v-else>
            <div class="fund-manager-content-empty">
              <!-- {{ $t('fundMessage.notMessage') }} -->
              <img :src="require('@/assets/images/fund_company_' + theme + '.png')" alt="" />
              <div>{{ $t('fundMessage.notMessage') }}</div>
            </div>
          </template>
        </li>
      </ul>
      <!-- <ul class="fund-record-list">
        <div class="fund-record-header">
          {{ $t('fundMessage.invest.idea') }}
        </div>
        <li class="invest-idea">
          {{
            managerData.investmentPhilosophy
              ? managerData.investmentPhilosophy
              : '-'
          }}
        </li>
      </ul>
      <ul class="fund-record-list">
        <div class="fund-record-header">
          {{ $t('fundMessage.invest.strategy') }}
        </div>
        <li class="invest-idea">
          {{
            managerData.investmentStrategy
              ? managerData.investmentStrategy
              : '-'
          }}
        </li>
      </ul> -->
       <ul class="fund-record-list">
        <div class="fund-record-header">
          {{ $t('fundMessage.invest.fundObjective') }}
        </div>
        <li class="invest-idea">
          {{
            managerData.fundObjective
              ? managerData.fundObjective
              : '--'
          }}
        </li>
      </ul>
    </template>
    <template v-else>
      <ul class="fund-file-box">
        <li class="fund-file-header">
          <span>{{ $t('fundMessage.type') }}</span>
          <span>{{ $t('fundMessage.updateTime') }}</span>
        </li>
        <li v-if="managerData.prospectusUrl" class="fund-file-catalog" @click="goPdf(1)">
          <div class="left">
            <img src="../../assets/images/fund-detail-pdf.png" alt="" />
            <span>{{ $t('fundMessage.fundStatement') }}</span>
          </div>
          <div>{{ managerData.prospectusUrlCnModifiedDate }}</div>
        </li>
        <li v-if="managerData.semiAnnualManagerReportUrl" class="fund-file-catalog" @click="goPdf(2)">
          <div class="left">
            <img src="../../assets/images/fund-detail-pdf.png" alt="" />
            <span>{{ $t('fundMessage.semiAnnualManagerReportUrl') }}</span>
          </div>
          <div>{{ managerData.semiAnnualManagerReportUrlModifiedDate }}</div>
        </li>
        <li v-if="managerData.annualManagerReportUrl" class="fund-file-catalog" @click="goPdf(3)">
          <div class="left">
            <img src="../../assets/images/fund-detail-pdf.png" alt="" />
            <span>{{ $t('fundMessage.annualManagerReportUrlEn') }}</span>
          </div>
          <div>{{ managerData.annualManagerReportUrlEnModifiedDate }}</div>
        </li>
        <li v-if="managerData.keyFactStatementUrl" class="fund-file-catalog" @click="goPdf(4)">
          <div class="left">
            <img src="../../assets/images/fund-detail-pdf.png" alt="" />
            <span>{{ $t('fundMessage.keyFactStatementUrlCn') }}</span>
          </div>
          <div>{{ managerData.keyFactStatementUrlCnModifiedDate }}</div>
        </li>
        <li v-if="managerData.providerFactSheetTUrl" class="fund-file-catalog" @click="goPdf(5)">
          <div class="left">
            <img src="../../assets/images/fund-detail-pdf.png" alt="" />
            <span>{{ $t('fundMessage.prospectusUrlCn') }}</span>
          </div>
          <div>{{ managerData.providerFactSheetModifiedDate }}</div>
        </li>
        <template v-if="managerData.eddidBulletinDtoList && managerData.eddidBulletinDtoList.length">
          <li class="fund-file-catalog" v-for="(item, index) in managerData.eddidBulletinDtoList" :key="index" @click="toOpenPdf(item)">
            <div class="left">
              <img src="../../assets/images/fund-detail-pdf.png" alt="" />
              <span>{{item.title  ? item.title[langMap[locale]] || item.title['hk'] || item.title['cn'] : ''}}</span>
            </div>
            <div>{{ moment(item.publishedTime).format('YYYY-MM-DD') }}</div>
          </li>
        </template>
      </ul>
    </template>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import Loading from '@/components/loading/index'
import { fundRecord } from '@/services/fund'
import { Toast } from 'mand-mobile'
import { getPdfDocumentUrl } from "@/utils/util";
export default {
  components: {
    Loading,
  },
  data() {
    return {
      moment,
      fundRecordCurrent: 1,
      managerData: {},
      isLoading: true,
      updateTime: '',
      langMap: {
        'zh-hans':'cn',
        'zh-hant':'hk',
        'en':'us',
      }
    }
  },
  methods: {
    checkType(data) {
      this.fundRecordCurrent = data.name
    },
    fundRecord() {
      this.isLoading = true
      fundRecord(this.$route.query.id, '').then((res) => {
        // console.log('基金档案数据=====', res);
        if (res.code == 200) {
          this.managerData = res.data
          this.managerData.prospectusUrlCnModifiedDate = moment(res.data.prospectusUrlCnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.semiAnnualManagerReportUrlModifiedDate = moment(res.data.semiAnnualManagerReportUrlModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.annualManagerReportUrlEnModifiedDate = moment(res.data.annualManagerReportUrlEnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.keyFactStatementUrlCnModifiedDate = moment(res.data.keyFactStatementUrlCnModifiedDate).format(
            'YYYY-MM-DD'
          )
          this.managerData.providerFactSheetModifiedDate = moment(res.data.providerFactSheetModifiedDate).format(
            'YYYY-MM-DD'
          )
          
        }
        this.isLoading = false
      })
    },
    fundsNameFun(data, type) {
      if (this.locale == 'zh-hans') {
        return data[type] && data[type].cn ? data[type].cn : '-'
      } else if (this.locale == 'zh-hant') {
        return data[type] && data[type].hk ? data[type].hk : '-'
      } else {
        return data[type] && data[type].us ? data[type].us : '-'
      }
    },
    goPdf(num) {
      let url = false;
      let pdfTitle = '';
      let data = getPdfDocumentUrl(this.managerData)
      if (num == 1) {
        // 基金说明书
        pdfTitle = this.$t('fundMessage.fundStatement')
        url = data[this.locale].prospectusUrl
      } else if (num == 2) {
        pdfTitle = this.$t('fundMessage.semiAnnualManagerReportUrl')
        // 基金半年度报告
        url = data[this.locale].semiAnnualManagerReportUrl
      } else if (num == 3) {
        pdfTitle = this.$t('fundMessage.annualManagerReportUrlEn')
        // 基金年度报告
        url = data[this.locale].annualManagerReportUrl
      } else if (num == 4) {
        pdfTitle = this.$t('fundMessage.keyFactStatementUrlCn')
        // 产品资料概况
        url = data[this.locale].keyFactStatementUrl
      } else if (num == 5) {
        // 基金说明书
        pdfTitle = this.$t('fundMessage.prospectusUrlCn')
        url = data[this.locale].providerFactSheetTUrl
      } 
      if(url) {
        this.$jsBridge.run('openPdf', {
          pdfUrl: url,
          pdfTitle: pdfTitle
        })
      }else {
        Toast.info(this.$t('myAccount.noUseDocument'))
      }
    },
    toOpenPdf(item){
      let url = item.bulletinLink  ? item.bulletinLink[this.langMap[this.locale]]  || item.bulletinLink['hk'] || item.bulletinLink['cn']: '';
      let pdfTitle = item.title  ? item.title[this.langMap[this.locale]] : '';
      if(!url){
        Toast.info(this.$t('myAccount.noUseDocument'))
        return
      }
      if(this.$jsBridge.isSupported('openPdf')){
        this.$jsBridge.run('openPdf', {
          pdfUrl: url,
          pdfTitle: pdfTitle
        })
      }else{
        window.open(url)
      }
    },
  },
  
  computed: {
    ...mapState(['theme', 'locale']),
  },
  mounted() {
    this.fundRecord()
  },
}
</script>

<style lang="scss" scoped>
.fund-record-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    background: themed('background-color');
    color: themed('text-color');
  }
  .fund-record-select {
    @include background_color(bg-color);
    @include font_color(second-text);
    .fund-record-tab {
      padding: 22px 30px 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28px;
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
      .bottom-border {
        margin: 8px auto 0;
        width: 16px;
        height: 5px;
        border-radius: 3px;
      }
      .activeType {
        text-align: center;
        @include font_color(text-color);
        .activeBorder {
          @include background_color(fund-bg);
        }
      }
    }
  }
  .fund-record-list {
    padding: 0 30px;
    @include background_color(second-bg);
    @include font_color(text-color);
    margin-top: 20px;
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-top: 0;
    }
    .fund-record-header {
      font-size: 34px;
      font-weight: bold;
      padding: 28px 0;
    }
    .fund-record-label {
      display: flex;
      justify-content: space-between;
      align-items: top;
      padding-top: 34px;
      label {
        font-size: 26px;
        @include font_color(second-text);
      }
      .fund-record-label-right {
        width: 474px;
        text-align: right;
        @include font_color(second-text);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        // text-align: right;
      }
      &:last-child {
        padding-bottom: 30px;
      }
    }
    .fund-manager {
      font-size: 26px;
      @include font_color(text-color);
      .fund-manager-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include font_color(second-text);
        font-size: 24px;
        padding: 18px 0;
        span {
          display: inline-block;
          width: 33%;
          text-align: left;
        }
      }
      .fund-manager-content {
        padding: 28px 0;
        border-bottom: 1px solid;
        @include border_color(line-color);
        &:last-child {
          border: 0;
        }
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          display: inline-block;
          width: 33%;
          text-align: left;
          p {
            text-align: left;
            &:last-child {
              @include font_color(second-text);
            }
          }
          // &:last-child {
          //     @include font_color(error-color);
          // }
        }
        .colorUp {
          @include font_color(k-line-red);
        }
        .colorDown {
          @include font_color(k-line-green);
        }
        .colorDown1 {
          @include font_color(second-text);
        }
      }
      .fund-manager-content-empty {
        text-align: center;
        padding: 28px 0;
        border-top: 1px solid;
        @include border_color(line-color);
        @include font_color(second-text);
        img {
          width: 344px;
          height: 197px;
        }
      }
    }
    .invest-idea {
      padding: 30px 0;
      font-size: 26px;
      @include font_color(text-color);
    }
  }
  .fund-file-box {
    padding: 0 30px;
    @include background_color(second-bg);
    .fund-file-header {
      padding: 18px 0;
      font-size: 24px;
      @include font_color(second-text);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .fund-file-catalog {
      padding: 26px 0;
      @include font_color(text-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid;
      @include border_color(line-color);
      div {
        &:first-child {
          display: flex;
          align-items: center;
          img {
            width: 48px;
            height: 48px;
            margin-right: 20px;
          }
        }
      }
    }
  }
  .left{
    flex: 1;
    padding-right: 10px;
    word-break: break-word;
  }
}
</style>
