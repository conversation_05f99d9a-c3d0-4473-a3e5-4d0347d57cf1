<template>
  <!-- 历史净值 -->
  <div class="history-net-worth-box">
    <div class="history-net-worth-header">
      <span>{{ $t("fundMessage.timeArea") }}</span>
      <span>{{ $t("fundMessage.netValue") }}</span>
      <span>{{ $t("fundMessage.upDown") }}</span>
    </div>
    <ul class="history-net-worth-data">
      <!-- <li class="history-net-worth-li-header">
                <span>{{$t('fundMessage.timeText')}}</span>
                <span>{{$t('fundMessage.netValue')}}</span>
                <span>{{$t('fundMessage.upDown')}}</span>
            </li> -->
      <li
        v-for="(item, index) in fundTimeData"
        :key="index"
        class="history-net-worth-li"
      >
        <span>{{ item.transDate }}</span>
        <span>{{ Number(item.netValue).toFixed(4) }}</span>
        <!-- :class="Number(item.changePercent) < 0 ? 'output' : Number(item.changePercent) ? 'entry' : 'entry1'" -->
        <span
          :class="
            klineTheme === 'redRiseGreenFall'
              ? Number(item.changePercent) < 0
                ? 'output'
                : Number(item.changePercent)
                ? 'entry'
                : 'entry1'
              : Number(item.changePercent) > 0
              ? 'output'
              : Number(item.changePercent)
              ? 'entry'
              : 'entry1'
          "
        >
          {{
            Number(item.changePercent) > 0
              ? "+" + (item.changePercent * 100).toFixed(2)
              : Number(item.changePercent)
              ? (item.changePercent * 100).toFixed(2)
              : "0.00"
          }}%
        </span>
      </li>
      <div
        @click="showAllNetWorth"
        class="fund-earnings-text"
        v-if="fundTimeData.length > 0"
      >
        <span>{{
          dataList
            ? $t("fundMessage.updateMoreMessage")
            : $t("fundMessage.moreMessage")
        }}</span>
      </div>
      <div v-else class="fund-earnings-text">
        {{ $t("fundMessage.notMessage") }}
      </div>
    </ul>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Loading from "@/components/loading/index";
import { performance } from "@/services/fund";
export default {
  components: {
    Loading,
  },
  data() {
    return {
      isLoading: true,
      dataList: false,
      fundTimeData: [],
      pageNum: 0,
      pageSize: 15,
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    showAllNetWorth() {
      // 更多数据
      if (!this.dataList) {
        this.pageNum += 1;
        // this.pageSize += 15;
      }
      this.performance();
    },
    performance() {
      this.isLoading = true;
      if (!this.dataList) {
        performance(
          { page: this.pageNum, size: this.pageSize },
          this.$route.query.id,
          "",
          "netValues",
          "networth"
        ).then((res) => {
          // console.log('历史净值====', res);
          if (res.code == 200) {
            if (this.fundTimeData.length > res.data.totalElements) {
              this.dataList = true;
            } else {
              this.fundTimeData = this.fundTimeData.concat(res.data.content);
            }
          }
          this.isLoading = false;
        });
      }
    },
  },
  mounted() {
    this.performance();
  },
};
</script>

<style lang="scss" scoped>
.history-net-worth-box {
  .history-net-worth-header {
    padding: 18px 29px 18px 31px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    @include font_color(second-text);
  }
  .history-net-worth-data {
    padding: 18px 29px 18px 31px;
    font-size: 26px;
    @include font_color(text-color);
    .history-net-worth-li-header,
    .history-net-worth-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 33.3%;
        &:nth-child(2) {
          text-align: center;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
    .history-net-worth-li {
      padding: 27px 0;
      border-bottom: 1px solid;
      @include border_color(line-color);
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      .entry {
        @include font_color(buy-color);
      }
      .entry1 {
        @include font_color(second-text);
      }
      .output {
        @include font_color(sell-color);
      }
    }
    div {
      padding: 27px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 28px;
      }
    }
    .fund-earnings-text {
      font-size: 24px;
      @include font_color(second-text);
    }
  }
}
</style>
