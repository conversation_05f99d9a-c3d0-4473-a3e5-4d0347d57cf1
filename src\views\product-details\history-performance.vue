<template>
  <!-- 历史业绩 -->
  <div class="history-erformance-box">
    <div class="history-erformance-bg">
      <div class="history-erformance-header">
        <span>{{ $t("fundMessage.timeArea") }}</span>
        <span>{{ $t("fundMessage.upDown") }}</span>
      </div>
      <ul class="history-erformance-data">
        <template v-for="(item, index) in fundTimeData">
          <li class="history-erformance-li" :key="index">
            <span>{{ $t(`fundMessage.dateTime.${index}`) }}</span>
            <!-- :class="item < 0 ? 'output' : item ? Number(item) ? 'entry' : 'second-text' : 'second-text'" -->
            <span
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? item < 0
                    ? 'output'
                    : Number(item)
                    ? 'entry'
                    : 'second-text'
                  : item > 0
                  ? 'output'
                  : Number(item)
                  ? 'entry'
                  : 'second-text'
              "
            >
              {{ performanceNum(item) }}
            </span>
          </li>
        </template>
      </ul>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Loading from "@/components/loading/index";
import { performance } from "@/services/fund";
export default {
  components: {
    Loading,
  },
  data() {
    return {
      isLoading: true,
      fundTimeData: {},
    };
  },
  computed: {
    ...mapState(["klineTheme"]),
  },
  methods: {
    performanceNum(data) {
      if (Number(data) == 0 || !data) {
        return "0.00%";
      }
      if (Number(data) > 0) {
        return "+" + (data * 100).toFixed(2) + "%";
      } else {
        return (data * 100).toFixed(2) + "%";
      }
    },
  },
  mounted() {
    // console.log(this.$route.query);
    performance({}, this.$route.query.id, "", "performance").then((res) => {
      // console.log(res);
      if (res.code == 200) {
        // this.fundTimeData = res.data
        // console.log(this.fundTimeData);
        let newObj = {};
        Object.keys(res.data).forEach((item, i) => {
          if (
            [
              "cumulative1Week",
              "cumulative1M",
              "cumulative3M",
              "cumulative6M",
              "cumulative1Y",
              "cumulative3Y",
              "cumulative5Y",
              "cumulativeMonthly",
              "cumulativeQuarterly",
              "cumulativeYearly",
              "cumulativeSinceLaunch",
            ].includes(item)
          ) {
            newObj[item] = Object.values(res.data)[i];
          }
        });
        this.fundTimeData = newObj;
      }
      this.isLoading = false;
    });
  },
};
</script>

<style lang="scss" scoped>
.history-erformance-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    background: themed("background-color");
    color: themed("text-color");
  }
  .history-erformance-bg {
    padding: 0 30px;
    @include background_color(second-bg);
  }
  .history-erformance-header {
    padding: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    @include font_color(second-text);
  }
  .history-erformance-data {
    font-size: 26px;
    @include font_color(text-color);
    .history-erformance-li {
      padding: 27px 0;
      border-bottom: 1px solid;
      @include border_color(line-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .entry {
        @include font_color(buy-color);
      }
      .second-text {
        @include font_color(second-text);
      }
      .output {
        @include font_color(sell-color);
      }
      &:last-child {
        border-bottom: 0;
      }
    }
    // div {
    //     padding: 27px 0;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     img {
    //         width: 28px;
    //     }
    // }
  }
}
</style>
