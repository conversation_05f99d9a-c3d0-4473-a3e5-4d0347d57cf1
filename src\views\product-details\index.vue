<template>
  <div :class="['product-detail-box', isApp ? 'show-download' : '']">
    <download-app type="fundDetail"></download-app>
    <!-- 产品基本信息 -->
    <ul class="product-detail-header">
      <!-- <li class="product-detail-title">{{ fundsNameFun(productDetails) }}</li> -->
      <li class="product-detail-title">
        {{ (productDetails.name && productDetails.name[langType]) || "-" }}
      </li>
      <li class="product-detail-number">
        <span>ISIN:&nbsp;</span>
        <span>{{ productDetails.fundIsin }}</span>
      </li>
      <li class="product-detail-type">
        <span>{{ productDetails.currencyType }}</span>
        <span>{{
          bondType(productDetails.assetClassType, $t("fundMessage.bondType"))
        }}</span>
        <span>
          {{
            isFirst
              ? tradeRulesData.minFirstInvestment
              : tradeRulesData.minNextInvestmentAmount
          }}&nbsp;{{
            productDetails.currencyType
              ? $t(`fundMessage.currencyType.${productDetails.currencyType}`)
              : ""
          }}&nbsp;{{
            isFirst
              ? $t("myAccount.buyPlaceholder")
              : $t("myAccount.additional")
          }}
        </span>
        <span class="last-span-box" @click="handleGoRiskInfo">
          {{
            riskLevel(productDetails.riskLevel, $t("fundMessage.fundRiskLevel"))
          }}
          <img src="@/assets/images/international/arrow.png" alt="">
        </span>
      </li>
      <li class="product-detail-ratio-netValue">
        <div class="product-detail-ratio">
          <!-- 其他---七日年化 -->
          <!-- <template v-if="this.urlDetail.currentType == 'MONEY_MARKET'">
            <p
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? Number(productDetails.annualised7Day) > 0
                    ? 'fund-up-value'
                    : Number(productDetails.annualised7Day)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                  : Number(productDetails.annualised7Day) < 0
                  ? 'fund-up-value'
                  : Number(productDetails.annualised7Day)
                  ? 'fund-drop-value'
                  : 'fund-drop-value1'
              "
            >
              {{
                Number(productDetails.annualised7Day) > 0
                  ? "+" + (productDetails.annualised7Day * 100).toFixed(4)
                  : Number(productDetails.annualised7Day)
                  ? (productDetails.annualised7Day * 100).toFixed(4)
                  : "0.0000"
              }}%
            </p>
            <p>
              {{ $t("fundMessage.annualYield") }}&nbsp;
              <img
                src="../../assets/images/fund-detail-question.png"
                @click="showTermDescribe('seven')"
                alt="pic"
              />
            </p>
          </template> -->
          <!-- 货币--近一年涨幅 -->
          <template>
            <!-- :class="Number(productDetails.cumulative1Y) > 0 ? 'fund-up-value' : Number(productDetails.cumulative1Y) ? 'fund-drop-value' : 'fund-drop-value1'" -->
            <p
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? Number(productDetails.cumulativeSinceLaunch) > 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulativeSinceLaunch)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                  : Number(productDetails.cumulativeSinceLaunch) < 0
                  ? 'fund-up-value'
                  : Number(productDetails.cumulativeSinceLaunch)
                  ? 'fund-drop-value'
                  : 'fund-drop-value1'
              "
            >
              {{
                Number(productDetails.cumulativeSinceLaunch) > 0
                  ? "+" + (productDetails.cumulativeSinceLaunch * 100).toFixed(2)
                  : Number(productDetails.cumulativeSinceLaunch)
                  ? (productDetails.cumulativeSinceLaunch * 100).toFixed(2)
                  : "0.00"
              }}%
            </p>
            <p>
              {{ $t("fundMessage.timeTile") + $t("fundMessage.amountText") }}
            </p>
          </template>
        </div>
        <div class="product-detail-netValue">
          <!-- 其他--万元收益  -->
          <template v-if="this.urlDetail.currentType == 'MONEY_MARKET'">
            <!-- :class="Number(productDetails.cumulative10Thousand) > 0 ? 'fund-up-value' : Number(productDetails.cumulative10Thousand) ? 'fund-drop-value' : 'fund-drop-value1'" -->
            <p
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? Number(productDetails.cumulative10Thousand) > 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulative10Thousand)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                  : Number(productDetails.cumulative10Thousand) < 0
                  ? 'fund-up-value'
                  : Number(productDetails.cumulative10Thousand)
                  ? 'fund-drop-value'
                  : 'fund-drop-value1'
              "
            >
              {{
                Number(productDetails.cumulative10Thousand) > 0
                  ? "+" + Number(productDetails.cumulative10Thousand).toFixed(4)
                  : Number(productDetails.cumulative10Thousand)
                  ? Number(productDetails.cumulative10Thousand).toFixed(4)
                  : "0.0000"
              }}
            </p>
            <p>
              {{ $t("fundMessage.earnings") }}({{
                stringToArr(productDetails.profitDate)
              }})&nbsp;
              <img
                src="../../assets/images/fund-detail-question.png"
                @click="showTermDescribe('tenThousandYuan')"
                alt=""
              />
            </p>
          </template>
          <!-- 货币-- 日涨跌幅 -->
          <template v-else>
            <!-- :class="Number(productDetails.cumulative1Day) > 0 ? 'fund-up-value' : Number(productDetails.cumulative1Day) ? 'fund-drop-value' : 'fund-drop-value1'" -->
            <p
              :class="
                klineTheme === 'redRiseGreenFall'
                  ? Number(productDetails.cumulative1Day) > 0
                    ? 'fund-up-value'
                    : Number(productDetails.cumulative1Day)
                    ? 'fund-drop-value'
                    : 'fund-drop-value1'
                  : Number(productDetails.cumulative1Day) < 0
                  ? 'fund-up-value'
                  : Number(productDetails.cumulative1Day)
                  ? 'fund-drop-value'
                  : 'fund-drop-value1'
              "
            >
              {{
                Number(productDetails.cumulative1Day) > 0
                  ? "+" + (productDetails.cumulative1Day * 100).toFixed(2)
                  : Number(productDetails.cumulative1Day)
                  ? (productDetails.cumulative1Day * 100).toFixed(2)
                  : "0.00"
              }}%
            </p>
            <p>{{ $t("fundMessage.cumulative1Day") }}</p>
          </template>
        </div>
        <!-- 单位净值 -->
        <div
          class="product-detail-netValue"
          v-if="this.urlDetail.currentType != 'MONEY_MARKET'"
        >
          <p class="last-net-value">
            {{ Number(productDetails.netValue || 0).toFixed(4) }}
          </p>
          <p>
            {{ $t("fundMessage.netValue") }}({{
              stringToArr(productDetails.transDate)
            }})
          </p>
        </div>
      </li>
    </ul>
    <div class="dialog-main" v-show="isTermDescribeShow">
      <div class="dialog">
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow1") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow2") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow3") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow4") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow5") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow6") }}
        </p>
        <p v-show="isSevenDays" class="content-describe">
          {{ $t("myAccount.sevenDescribeRow7") }}
        </p>

        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow1") }}
        </p>
        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow2") }}
        </p>
        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow3") }}
        </p>
        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow4") }}
        </p>
        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow5") }}
        </p>
        <p v-show="!isSevenDays" class="content-describe">
          {{ $t("myAccount.thousandDescribeRow6") }}
        </p>
        <div class="button-bottom" @click="isTermDescribeShow = false">
          {{ $t("myAccount.confim") }}
        </div>
      </div>
    </div>
    <!-- 产品数据 -->
    <ul class="product-type-select">
      <li class="product-type-tab">
        <div
          v-for="(item, index) in typeTabs"
          :key="index"
          @click="checkType(item)"
          class="item"
          :class="{ activeType: productCurrent == item.name }"
        >
          <div>{{ item.label }}</div>
          <div
            :key="index"
            class="bottom-border"
            :class="{ activeBorder: productCurrent == item.name }"
          ></div>
        </div>
      </li>
      <!-- 图表数据 -->
      <div class="product-components">
        <component
          :is="componentName"
          :fundIsin="urlDetail.fundIsin"
          :type="urlDetail.currentType"
        ></component>
      </div>
    </ul>
    <!-- 基金持仓 -->
    <ul class="fund-public-box">
      <div class="fund-public-header">
        <span>{{ $t("fundMessage.fundPulic") }}</span>
        <span @click="possessFund">
          {{ $t("fundMessage.checkDetails") }}
          <img src="../../assets/images/fund-detail-more.png" alt="" />
        </span>
      </div>
      <!-- 投资分布 -->
      <div class="invest-distribution">
        <div class="invest-title">{{ $t("fundMessage.investDistribute") }}</div>
        <div
          v-if="
            compositionsPie &&
            compositionsPie.assets &&
            compositionsPie.assets.length
          "
          class="invest-chart"
        >
          <investPie :compositionsPie="compositionsPie"></investPie>
        </div>
        <div v-else class="invest-chart-height1">
          <img
            class="pic-empty"
            :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
            alt="pic"
          />
          <div>{{ $t("fundMessage.notMessage") }}</div>
        </div>
      </div>
      <!-- 十大持仓 -->
      <div class="invest-distribution">
        <div class="invest-title">{{ $t("fundMessage.entrepotNumber") }}</div>
        <!-- class="invest-chart" -->
        <div
          v-if="
            compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length
          "
          :class="!investDetails ? 'invest-chart-height2' : 'invest-chart-open'"
        >
          <holdPosition
            :chartTitle="false"
            :investDetails="investDetails"
            :compositionsPie="compositionsPie"
          ></holdPosition>
        </div>
        <div v-else class="invest-chart-height1">
          <img
            class="pic-empty"
            :src="require('@/assets/images/subordinate_fund_' + theme + '.png')"
            alt="pic"
          />
          <div>{{ $t("fundMessage.notMessage") }}</div>
        </div>
        <div
          class="unflod-chart"
          v-if="
            compositionsPie &&
            compositionsPie.top10Holdings &&
            compositionsPie.top10Holdings.length > 5
          "
        >
          <span @click="investDetails = !investDetails">{{
            !investDetails
              ? $t("fundMessage.checkText")
              : $t("fundMessage.checkClose")
          }}</span>
          <img
            v-if="!investDetails"
            src="../../assets/images/icon_expand.png"
            alt=""
          />
          <img v-else src="../../assets/images/icon_collapse.png" alt="" />
        </div>
      </div>
    </ul>
    <!-- 交易规则 -->
    <ul class="fund-public-box">
      <div class="fund-public-header">
        <span>{{ $t("fundMessage.dealRule") }}</span>
        <span @click="showDealRule">
          {{ $t("fundMessage.dealFlow") }}
          <img src="../../assets/images/fund-detail-more.png" alt="" />
        </span>
      </div>
      <div class="fund-order-box">
        <div class="fund-order-state">
          <ul>
            <li>
              {{ $t("fundMessage.subscribe")
              }}{{ $t("fundMessage.submitText") }}
            </li>
            <li>{{ $t("fundMessage.affirmShare") }}</li>
            <li>{{ $t("fundMessage.earningsArrive") }}</li>
          </ul>
          <ul>
            <li class="fund-img-li">
              <img src="../../assets/images/fund_detail_process.png" alt="" />
            </li>
          </ul>
          <ul class="last-ul">
            <li>{{ $t("fundMessage.TDayBeforeTen", { time: tradeRulesData.endOfTradingDay }) }}</li>
            <li>T+{{ tradeRulesData.confirmationPlusDay }}{{ $t("fundMessage.TDay") }}</li>
            <li>T+{{ tradeRulesData.moneyConfirmedPlusDay }}{{ $t("fundMessage.tradingDay") }}</li>
          </ul>
        </div>
        <div class="fund-order-rate">
          <!-- 申购费率 -->
          <div>
            <p>{{ $t("fundMessage.subscribe") }}{{ $t("fundMessage.rate") }}</p>
            <p v-if="showLodder()">
              <template v-if="tradeRulesData.subscriptionRatePreferential.length > 1 && tradeRulesData.subscriptionRatePreferential[0].rate">
                <div class="ladder-charge">
                  <span>{{ $t("fundMessage.ladderCharge") }}</span>
                  <img :src="require(`@/assets/images/unfold_${showLadderCharge}.png`)" alt="" @click="showLadderCharge = !showLadderCharge">
                </div>
              </template>
              <template v-else>
                <span class="span1">
                  {{
                    tradeRulesData && Number(tradeRulesData.subscriptionRatePreferential[0].rate)
                      ? (
                          tradeRulesData.subscriptionRatePreferential[0].rate * 100
                        ).toFixed(2)
                      : "0.00"
                  }}%
                </span>
                <span class="span2">
                  {{
                    tradeRulesData && Number(tradeRulesData.subscriptionRate[0].rate)
                      ? (tradeRulesData.subscriptionRate[0].rate * 100).toFixed(2)
                      : "0.00"
                  }}%
                </span>
              </template>
            </p>
            <p v-else>
              <template v-if="tradeRulesData.subscriptionRate && tradeRulesData.subscriptionRate.length > 1 && tradeRulesData.subscriptionRate[0].rate">
                <div class="ladder-charge">
                  <span>{{ $t("fundMessage.ladderCharge") }}</span>
                  <img :src="require(`@/assets/images/unfold_${showLadderCharge}.png`)" alt="" @click="showLadderCharge = !showLadderCharge">
                </div>
              </template>
              <template v-else>
                <span class="span1">
                  {{
                   tradeRulesData && tradeRulesData.subscriptionRate && Number(tradeRulesData.subscriptionRate[0].rate)
                      ? (tradeRulesData.subscriptionRate[0].rate * 100).toFixed(2)
                      : "0.00"
                  }}%
                </span>
                <span class="span2"></span>
              </template>
            </p>
          </div>
          <!-- 赎回费率 -->
          <div>
            <p>
              {{ $t("fundMessage.redemption") }}{{ $t("fundMessage.rate") }}
            </p>
            <p v-if="tradeRulesData && tradeRulesData.redemptionRatePreferential">
              <span class="span1">
                {{
                  tradeRulesData && Number(tradeRulesData.redemptionRatePreferential)
                    ? (tradeRulesData.redemptionRatePreferential * 100).toFixed(
                        2
                      )
                    : "0.00"
                }}%
              </span>
              <span class="span2">
                {{
                  tradeRulesData && Number(tradeRulesData.redemptionRate)
                    ? (tradeRulesData.redemptionRate * 100).toFixed(2)
                    : "0.00"
                }}%
              </span>
            </p>
            <p v-else>
              <span class="span1">
                {{
                  tradeRulesData && Number(tradeRulesData.redemptionRate)
                    ? (tradeRulesData.redemptionRate * 100).toFixed(2)
                    : "0.00"
                }}%
              </span>
              <span class="span1"></span>
            </p>
          </div>
        </div>
        <!-- 阶梯式收费 -->
        <!-- v-if="showLadderCharge" -->
        <div class="ladder-charge-table" v-if="showLadderCharge">
          <ul>
            <li>
              <div>{{ $t("myAccount.money") }}{{ $t("myAccount.section") }}&nbsp;({{tradeRulesData.currency}})</div>
              <div>{{ $t("myAccount.purchaseRates") }}</div>
            </li>
            <template v-if="showLodder()">
              <li v-for="(item, index) in tradeRulesData.subscriptionRatePreferential" :key="index">
                <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                <div>{{ (item.rate * 100).toFixed(2) }}%</div>
              </li>
            </template>
            <template v-else>
              <li v-for="(item, index) in tradeRulesData.subscriptionRate" :key="index">
                <div v-if="item.from==0 && item.end!='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                <div v-if="item.from!=0 && item.end !='inf'">{{item.from}}&lt;{{ $t("myAccount.money") }}≤{{item.end}}</div>
                <div v-if="item.end =='inf'">{{ $t("myAccount.money") }}&gt;{{item.from}}</div>
                <div>{{ (item.rate * 100).toFixed(2) }}%</div>
              </li>
            </template>
          </ul>
        </div>
      </div>
    </ul>
    <!-- 基金档案 -->
    <ul class="fund-public-box">
      <div class="fund-public-header">
        <span>{{ $t("fundMessage.fundRecord") }}</span>
        <span @click="showFundRecord">
          {{ $t("fundMessage.fundRecordType") }}
          <img src="../../assets/images/fund-detail-more.png" alt="" />
        </span>
      </div>
      <div class="fund-public-label">
        <div>
          <label>{{ $t("fundMessage.fundAssetSize") }}</label>
          <span v-if="locale === 'en'">{{ (productDetails.assetSize / *********0).toFixed(2) }}{{ $t("fundMessage.aHundredMillion") }}({{
              productDetails.fundCurrency
            }})</span>
          <span v-else>{{ (productDetails.assetSize / *********).toFixed(2) }}{{ $t("fundMessage.aHundredMillion") }}({{
              productDetails.fundCurrency
            }})</span>
        </div>
        <div>
          <label>{{ $t("fundMessage.fundEstablishTime") }}</label>
          <span>{{ productDetails.establishTime }}</span>
        </div>
        <div>
          <label>{{ $t("fundMessage.fundCompany") }}</label>
          <!-- <span>{{ fundsNameFun(productDetails) }}</span> -->
          <!-- <span>{{ fundsNameFun(productDetails) }}</span> -->
          <span>{{
            (productDetails.companyName &&
              productDetails.companyName[langType]) ||
            "-"
          }}</span>
        </div>
        <div>
          <label>ISIN</label>
          <span>{{ productDetails.fundIsin }}</span>
        </div>
        <div>
          <label>{{ $t("fundMessage.fundStockType") }}</label>
          <span>{{ productDetails.stockType }}</span>
        </div>
        <div>
          <label>{{ $t("fundMessage.fundBondType") }}</label>
          <span>{{
            bondType(productDetails.assetClassType, $t("fundMessage.bondType"))
          }}</span>
        </div>
        <div>
          <label>{{ $t("fundMessage.fundEstablishPlace") }}</label>
          <span>{{ productDetails.establishPlace }}</span>
        </div>
      </div>
    </ul>
    <fundIntroduce class="margin-bottom-69"></fundIntroduce>
    <div class="transaction-button" v-if="!isApp">
      <div class="apply" @click="goTotTansaction(1)">
        {{ $t("myAccount.apply") }}
      </div>
      <div class="sell" @click="goTotTansaction(0)">
        {{ $t("myAccount.redeem") }}
      </div>
      <div class="optional">
        <div @click="focus" class="img">
          <img
            :src="
              !isFocusOn
                ? require('@/assets/images/icon_optional_default.png')
                : require('@/assets/images/icon_optional_selected.png')
            "
            alt="pic"
          />
        </div>
        <div class="font">
          <span>{{ $t("myAccount.optional") }}</span>
        </div>
      </div>
      <div @click="share" class="optional">
        <div class="img">
          <img src="../../assets/images/icon_share.png" alt="pic" />
        </div>
        <div class="font">
          <span>{{ $t("myAccount.fundShare") }}</span>
        </div>
      </div>
    </div>
    <div class="dialog-main" v-show="isRisklevel">
      <div class="dialog">
        <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
        <div class="button-bottom-risk">
          <div class="first-class" @click="isRisklevel = false">
            {{ $t("common.btns.cancel") }}
          </div>
          <div @click="toMeasure">{{ $t("myAccount.reEvaluation") }}</div>
        </div>
      </div>
    </div>
    <div class="dialog-main" v-show="isComplexProduct">
      <div class="dialog">
        <p class="content-describe">{{ $t("myAccount.riskDescription1") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription2") }}</p>
        <p class="content-describe">{{ $t("myAccount.riskDescription3") }}</p>
        <div class="button-bottom-risk">
          <div class="first-class" @click="isComplexProduct = false">
            {{ $t("common.btns.cancel") }}
          </div>
          <div @click="toSuitabilityMatchingRecord">
            {{ $t("common.btns.examinecause") }}
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-main" v-show="isComplexProductQualified">
      <div class="dialog">
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct1") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct2") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct3") }}
        </p>
        <p class="content-describe">
          {{ $t("myAccount.riskDescriptionComplexProduct4") }}
        </p>
        <div class="button-bottom-risk">
          <div class="first-class" @click="isComplexProductQualified = false">
            {{ $t("common.btns.cancel") }}
          </div>
          <div @click="toBuy">{{ $t("myAccount.goOn") }}</div>
        </div>
      </div>
    </div>
    <!-- 风险测评 dialog -->
    <div class="dialog-main" v-if="ExpiredShow">
      <div class="dialog">
        <p class="content-describe">{{  expired_ExpiringSoon }}</p>
        <div class="button-bottom-risk">
          <div class="first-class" @click="onBasicCancel">{{ ExpiredBtn === 'later' ? this.$t('common.Later') : $t('common.btns.cancel') }}</div>
          <div @click="onBasicConfirm">{{ $t('common.SubmitQuestionnaire') }}</div>
        </div>
      </div>
    </div>
    <Loading v-if="isLoading"></Loading>
  </div>
</template>

<script>
import { mapState } from "vuex";
import fundIntroduce from "@/components/footer/fund-introduce.vue";
import annual from "@/components/fundComponent/annual";
import historyPerformance from "@/components/fundComponent/history-performance";
import fundEarnings from "@/components/fundComponent/fund-earnings";
import performanceTrend from "@/components/fundComponent/performance-trend";
import historyNetworth from "@/components/fundComponent/history-networth";
import investPie from "@/components/fundChart/invest-pie.vue";
import holdPosition from "@/components/fundComponent/hold-position.vue";
import Loading from "@/components/loading/index";
import downloadApp from "@/components/downloadApp/index";
import { riskLevel, bondType } from "@/utils/fund-type";
import { fundProductFun, fundCompositions, tradeRules } from "@/services/fund";
import moment from "moment";
import {
  getAccountPosition,
  fundFocus,
  getAccountRiskLevel,
  cancelFundFocus,
  getAccountNumber,
  getOptionalList,
  getFundInformation,
  fundCashRiskAssessment
} from "@/services/account";
import { Toast } from "mand-mobile";
import { refreshAppToken } from "@/utils/auth";
import { delParam } from '@/utils/urlParams';
import { compareVersion } from '@/utils/util';

export default {
  components: {
    fundIntroduce,
    annual,
    historyPerformance,
    fundEarnings,
    performanceTrend,
    historyNetworth,
    investPie,
    holdPosition,
    Loading,
    downloadApp,
  },
  data() {
    return {
      isApp: false,
      isComplexProductQualified: false,
      isComplexProduct: false,
      isFirst: false,
      isRisklevel: false,
      tradeAccountStatus: "",
      isSevenDays: false,
      optionalList: [],
      isFocusOn: false,
      urlDetail: {}, // 查询初始数据--传递过来的参数
      productCurrent: 1,
      typeTabs: [],
      componentName: "",
      investDetails: false,
      isLoading: true,
      productDetails: {},
      tradeRulesData: {},
      compositionsPie: {},
      isTermDescribeShow: false,
      langType: "cn",
      isProfessionalInvestor: "", // 增加专业投资者校验 Y("Y", "是专业投资者"), N("N", "非专业投资者");
      showLadderCharge: false, // 阶梯式收费
      AccountNumber: {},
      isOldShare: true,
      ExpiredBtn: '',
      ExpiredShow: false,
      riskFundIsin: "",
      expired_ExpiringSoon: '',
      riskData: null,
      riskResData: null
    };
  },
  computed: {
    ...mapState(["locale", "theme", "accountNumber", "klineTheme"]),
  },
  created() {
    // 初始化查询登录状态  如果登陆了 查询自选状态   没有没在自选
    // 首次投资额后台给
    this.urlDetail = this.$route.query;
    fundCompositions(this.$route.query.fundIsin, "").then((res) => {
      // 基金持仓
      if (res.code == 200) {
        this.compositionsPie = res.data;
      }
    });
    this.$jsBridge.run("getLoginState", {
      callback: ({ loginStatus }) => {
        if (loginStatus) {
          this.getFundOptionalList();
        } else {
          this.isFocusOn = false;
        }
      },
    });
    tradeRules(this.$route.query.fundIsin).then((res) => {
      // 交易规则
      if (res.code == 200) {
        let timeData = res.data.endOfTradingDay.split(':');
        res.data.endOfTradingDay = timeData[0] + ':' + timeData[1]
        this.tradeRulesData = res.data;
        if (res.data.firstInvestment === false) {
          this.isFirst = false;
        } else {
          this.isFirst = true;
        }
      }
    });
  },
  beforeMount() {
    this.$jsBridge.run('getAppInfo', {
      callback: ({ appVersion }) => {
        this.isOldShare = appVersion && !compareVersion(appVersion, '3.18.0');
      }
    })
  },
  mounted() {
    this.langType =
      this.locale === "zh-hans"
        ? "cn"
        : this.locale === "zh-hant"
        ? "hk"
        : "us";
    if (this.$jsBridge.isSupported("startEdWeb")) {
      this.isApp = false;
    } else {
      this.isApp = true;
    }

    // this.componentName = "annual";
    // this.productDetailsFun()

    if (this.urlDetail.currentType == "MONEY_MARKET") {
      this.typeTabs = this.$t("fundMessage.typeTabs");
    } else {
      this.typeTabs = this.$t("fundMessage.typeTabs1");
    }

    // 默认组件
    this.$nextTick(() => {
      this.componentName = "annual";
      this.urlDetail = this.$route.query;
      this.productDetailsFun();
    });
    setTimeout(() => {
      this.isLoading = false;
      this.onH5Share();
      this.onBuried();
    }, 500);
  },

  methods: {
    handleGoRiskInfo() {
      this.$router.push({
        path: "/international/riskLevelInfo",
      });
    },
    toBuy() {
      this.isComplexProductQualified = false;
      this.routerRun(1);
    },
    toSuitabilityMatchingRecord() {
      this.isComplexProduct = false;
      this.$router.push({
        path: "/suitabilityMatchingRecord",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    toMeasure() {
      this.isRisklevel = false;
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/risk/tips?nav=0`,
        callback: () => {},
      });
    },
    // isLogin() {
    //   this.$jsBridge.run('getLoginState', {
    //     callback: ({ loginStatus }) => {
    //       if (loginStatus) {
    //         this.getPositionInfo();
    //         this.getFundOptionalList();
    //       } else {
    //         this.isFocusOn = false;
    //         this.loginStatus = false;
    //       }
    //     }
    //   })
    // },
    getPositionInfo() {
      // 交易规则
      tradeRules(this.$route.query.fundIsin).then((res) => {
        if (res.code == 200) {
          this.tradeRulesData = res.data;
          if (res.data.firstInvestment === false) {
            this.isFirst = false;
          } else {
            this.isFirst = true;
          }
        }
      });
    },
    showLodder () {
      if (this.tradeRulesData.subscriptionRatePreferential && this.tradeRulesData.subscriptionRatePreferential.length > 0 && this.tradeRulesData.subscriptionRatePreferential[0].rate) {
        return true
      }else {
        return false
      }
    },
    share() {
      const { href, origin } = window.location;
      const { name } = this.productDetails;
      let url = delParam({ paramKey: ['access_token', 'source', 'pageType', 'shareId', 'shareChannel', 'shareType'] });
      url += `${url.includes('?') ? '&' : '?'}pageType=fundDetail`;

      !this.isOldShare
        ? this.$jsBridge.run('startShareUrlAndPicture', {
            type: 'C',
            title: this.$t('fundMessage.shareTitle'),
            content: name && name[this.langType] ? name[this.langType] : '-',
            imageUrl: origin + '/logo-share.png',
            url,
            contentType: '基金详情页',
            pageType: 'fundDetail',
            callback: body => {
              this.$sensor.common.contentShare({
                content_type: '基金详情页',
                content_title: this.$route.query.fundIsin || this.$t('router.productDetails'),
                activity_url: href,
                share_channel: body.channel
              })
            },
          })
        : this.$jsBridge.run("startShareUrl", {
            title: this.$t('fundMessage.shareTitle'),
            content: name && name[this.langType] ? name[this.langType] : '-',
            imageUrl: origin + '/logo-share.png',
            url,
            contentType: '基金详情页',
            pageType: 'fundDetail',
            callback: body => {
              this.$sensor.common.contentShare({
                content_type: '基金详情页',
                content_title: this.$route.query.fundIsin || this.$t('router.productDetails'),
                activity_url: href,
                share_channel: body.channel
              })
            },
          });
    },
    // 站外打开分享页面埋点
    onBuried() {
      const { pageType, shareId, shareChannel, shareType, fundIsin } = this.$route.query;

      pageType && !this.isInApp && this.$sensor.common.$WebPageLeave({
        from_pagetype: pageType,
        share_id: shareId || '',
        share_channel: shareChannel || '',
        share_type: shareType || '',
        share_content_title: fundIsin || this.$t('router.productDetails'),
      })
    },
    onH5Share() {
      const { href, origin } = window.location;
      const { name } = this.productDetails;

      this.$h5Share(href, this.locale, {
        link: href,
        title: this.$t('fundMessage.shareTitle'),
        desc: name && name[this.langType] ? name[this.langType] : '',
        imgUrl: origin + '/logo-share.png'
      });
    },

    showTermDescribe(flag) {
      if (flag === "seven") {
        this.isSevenDays = true;
      } else {
        this.isSevenDays = false;
      }
      this.isTermDescribeShow = true;
    },
    getFundOptionalList() {
      let object = {
        marketTab: "FUND",
      };
      getOptionalList(object, this).then((res) => {
        this.optionalList = res.data.datas;
        if (!this.optionalList.length) {
          this.isFocusOn = false;
        } else {
          this.isFocusOn = false;
          this.optionalList.forEach((item) => {
            if (item.code === this.$route.query.fundIsin) {
              this.isFocusOn = true;
            }
          });
        }
      });
    },
    focus() {
      this.$jsBridge.run("getLoginState", {
        callback: ({ loginStatus }) => {
          if (loginStatus) {
            this.focusReset();
          } else {
            this.loginFund();
          }
        },
      });
    },
    focusReset() {
      if (!this.isFocusOn) {
        let parameter = {
          code: this.$route.query.fundIsin,
          exchangeType: this.productDetails.assetClassType,
          marketTab: "FUND",
          marketType: "FUND",
          name: this.productDetails.name.cn
            ? this.productDetails.name.cn
            : this.productDetails.name.hk,
        };
        fundFocus(parameter).then((res) => {
          if (res.code == "200") {
            setTimeout(() => {
              Toast.info(this.$t("myAccount.focus"));
            }, 500);
            this.getFundOptionalList();
          }
        });
      } else {
        let object = this.optionalList.find((item) => {
          return item.code === this.$route.query.fundIsin;
        });
        let parameter = {
          no: object.no,
        };
        cancelFundFocus(parameter, this).then((res) => {
          this.isFocusOn = false;
          if (res.code == "200") {
            Toast.info(this.$t("myAccount.nofocus"));
            this.getFundOptionalList();
          }
        });
      }
    },
    checkType(data) {
      this.productCurrent = data.name;
      switch (
        this.productCurrent // 更新组件
      ) {
        case 1:
          this.componentName = "annual";
          break;
        case 2:
          this.componentName = "historyPerformance";
          break;
        case 3:
          this.componentName =
            this.urlDetail.currentType == "MONEY_MARKET"
              ? "fundEarnings"
              : "historyNetworth";
          break;
        default:
          break;
      }
    },
    productDetailsFun() {
      // 产品详情
      fundProductFun(this.urlDetail.fundIsin, "").then((res) => {
        if (res.code == 200) {
          this.productDetails = res.data;
        }
      });
    },
    stringToArr(data) {
      if (data) {
        let createArr = data.split("-");
        createArr.shift();
        return createArr.join("-");
      }
    },
    bondType(type, data) {
      // 基金类型
      let newText = "";
      newText = bondType(type, data);
      return newText;
    },
    riskLevel(type, data) {
      // 基金风险等级
      let newText = "";
      newText = riskLevel(type, data);
      return newText;
    },
    fundsNameFun(data) {
      if (this.locale == "zh-hans") {
        return data.name && data.name.cn ? data.name.cn : "-";
      } else if (this.locale == "zh-hant") {
        return data.name && data.name.hk ? data.name.hk : "-";
      } else {
        return data.name && data.name.us ? data.name.us : "-";
      }
    },
    closeDetails(e) {
      this.investDetails = e;
    },
    possessFund() {
      this.$router.push({
        path: "/fundPossess",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    showDealRule() {
      // 进入交易规则页面
      this.$router.push({
        path: "/dealRule",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    showFundRecord() {
      // 进入基金档案
      this.$router.push({
        path: "/fundRecord",
        query: { id: this.urlDetail.fundIsin },
      });
    },
    // 申购赎回前判定当前基金是否支持交易 判断登录是否  先判断是否登录  在判断当前基金是否支持交易
    goTotTansaction(flag) {
      // 判断登陆状态
      this.$jsBridge.run("getLoginState", {
        callback: ({ loginStatus }) => {
          if (loginStatus) {
            // 判断开户状态
            getAccountNumber(this).then((res) => {
              this.AccountNumber = res.data.user;
              this.isProfessionalInvestor = res.data.user.isProfessionalInvestor;
              if (res.data.tradingAccountList && res.data.tradingAccountList.length) {
                let object = res.data.tradingAccountList.find((item) => {
                  return item.type === "FUND";
                });
                if (object && object.tradeAccountNumber) {
                  /*
                    风险测评日期有效状态 --> 风险测评结果已过期，则不允许用户申购
                    （1）如有登录校验、基金开户校验，则放在登录校验、基金开户校验后面。
                    （2）如没有登录校验、基金开户校验，则放在第一步校验
                  */
                  fundCashRiskAssessment(object.tradeAccountNumber).then(riskStatus => {
                    this.tradeAccountStatus = object.status;
                    this.$store.commit("setAccountNumber", object.tradeAccountNumber);
                    if (riskStatus.data.assessmentStatus === 'Expired') {
                      this.ExpiredShow = true
                      this.ExpiredBtn = 'cancel'
                      this.expired_ExpiringSoon = this.$t('common.Expired')
                      return
                    } else if (riskStatus.data.assessmentStatus === 'ExpiringSoon') {
                      this.ExpiredShow = true
                      this.ExpiredBtn = 'later'
                      this.expired_ExpiringSoon = this.$t('common.ExpiringSoon', { time: moment(riskStatus.data.expiryDate).format("YYYY-MM-DD") })
                      return
                    } else { // 已登录 && 已开户 && 风险测评未过期
                      this.accountAttribute(flag)
                    }
                  })
                  
                } else {
                  // 有开过户 没有开基金账户
                  this.$jsBridge.run("toPage", {
                    jumpType: "NATIVE",
                    loginState: "JUDGMENT",
                    openAccountState: "JUDGMENT_FUND",
                    navigationContentCode: "FUND_ACCOUNT",
                    navigationUri: "FUND_ACCOUNT",
                    titleDisplay: "DISPALY",
                  });
                }
              } else {
                // 什么户都没开
                this.$jsBridge.run("toPage", {
                  jumpType: "NATIVE",
                  loginState: "JUDGMENT",
                  openAccountState: "JUDGMENT",
                  navigationContentCode: "FUND_ACCOUNT",
                  navigationUri: "FUND_ACCOUNT",
                  titleDisplay: "DISPALY",
                });
              }
            });
          } else {
            this.loginFund();
          }
        },
      });
    },
    accountAttribute(flag) {
      // 有开过基金账户
      // this.tradeAccountStatus = object.status;
      // this.$store.commit("setAccountNumber", object.tradeAccountNumber);
      // 判定当前基金账户是否可用
      if (this.tradeAccountStatus != "OPEN") {
        Toast.info(this.$t("myAccount.abnormalAccount"));
        return;
      }
      if (flag === 0) {
        this.routerRun(flag);
      } else {
        // 用户国籍是否在基金可申购地区范围
        if (this.productDetails.supportRegions && this.productDetails.supportRegions.length > 0) {
          if (!this.productDetails.supportRegions.includes(this.AccountNumber.issuingCountry) && flag) {
            Toast.info(this.$t("myAccount.regionIsNotSupported"));
            return;
          }
        }
        /**
         * @params
         * professionalInvestorFlag: true (仅限专业投资者), false (不做专业投资者限制)
         * isProfessionalInvestor: 'Y' (当前账户为专业投资者), 'N' (当前账户为非专业投资者)
         */
        // 该基金是否专业投资者才能申购
        if (this.isProfessionalInvestor !== 'Y' && this.productDetails.professionalInvestorFlag && flag) {
          Toast.info(this.$t("myAccount.specialtyInvestor"));
          return;
        }
        // 判断是否是复杂产品
        getFundInformation(this.urlDetail.fundIsin).then((res) => {
          if (res.data.complexProductFlag) {
            // 复杂产品流程
            this.judgeTheRiskLevelComplex(flag);
          } else {
            this.testRouterRun(flag);
          }
        });
      }
    },
    onBasicConfirm() { // 跳转风险评测
      this.ExpiredShow = false
      // this.$router.push({
      //   path: "/risk/tips"
      // })
      this.$jsBridge.run("startEdWeb", {
        url: window.location.origin + `/risk/tips?nav=0&isExpired=yes`,
        callback: () => {},
      });
    },
    onBasicCancel() { // 跳转基金申购页面
      console.log('取消按钮');
      this.ExpiredShow = false
      if (this.ExpiredBtn === 'later') {
        this.accountAttribute(1)
      }
    },
    loginFund() {
      this.$jsBridge.run("startLogin", {
        type: "1A",
        callback: ({ login_state }) => {
          if (login_state) {
            this.$jsBridge.run("generateNewToken", {
              callback: ({ accessToken }) => {
                sessionStorage.setItem(
                  "access_token",
                  accessToken ? `Bearer ${accessToken}` : ""
                );
                this.getPositionInfo();
                this.getFundOptionalList();
              },
            });
          }
        },
      });
    },
    judgeTheRiskLevelComplex(flag) {
      // 判断当前账号风险等级是否可以购买当前基金
      getAccountRiskLevel(this).then((res) => {
        if (
          this.productDetails.riskLevel <= Number(res.data.result[1]) &&
          res.data.isDerivative
        ) {
          this.isComplexProductQualified = true;
          return;
        } else {
          this.isComplexProduct = true;
        }
      });
    },
    testRouterRun(flag) {
      getAccountRiskLevel(this).then((res) => {
        if (
          Number(res.data.result[1]) < Number(this.productDetails.riskLevel) &&
          flag === 1
        ) {
          this.isRisklevel = true;
          return;
        }
        this.routerRun(flag);
      });
    },
    routerRun(flag) {
      if (flag && !this.tradeRulesData.buyFlag) {
        Toast.info(this.$t("myAccount.subscriptionIsNotSupported"));
        return;
      } else if (!flag && !this.tradeRulesData.sellFlag) {
        Toast.info(this.$t("myAccount.redemptionIsNotSupported"));
        return;
      }
      let parameter = {
        isRouteBuy: false,
        fundIsin: this.urlDetail.fundIsin,
        isProfessionalInvestor: this.isProfessionalInvestor || "N",
        professionalInvestorFlag: this.productDetails.professionalInvestorFlag
      };
      if (flag) {
        parameter.isRouteBuy = true;
        this.$router.push({
          path: "/transactionBuy",
          query: parameter,
        });
      } else {
        getAccountPosition(this.accountNumber, this.urlDetail.fundIsin).then(
          (res) => {
            if (res.code == 200) {
              parameter.quantity = res.data.availableQuantity;
              if (parameter.quantity > 0) {
                this.$router.push({
                  path: "/transactionSell",
                  query: parameter,
                });
              } else {
                Toast.info(this.$t("myAccount.noPsition"));
                return;
              }
            }
          }
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.product-detail-box {
  height: 100%;
  overflow: auto;
  @include themeify {
    background: themed("bg-color-base");
    color: themed("text-color");
  }
  // 禁止页面文本可以选中
  moz-user-select: -moz-none;
  -moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  .product-detail-header {
    padding: 40px 30px;
    @include background_color(second-bg);
    @include font_color(text-color);
    .product-detail-title {
      font-size: 34px;
      font-weight: 500;
      word-wrap: break-word; // 数字或英文字母换行
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .product-detail-number {
      font-size: 22px;
      margin: 20px 0 17px;
      @include font_color(second-text);
    }
    .product-detail-type {
      @include font_color(btn-color);
      font-size: 22px;
      display: flex;
      justify-content: start;
      align-items: center;
      span {
        @include background_color(lam-bg);
        margin-right: 20px;
        padding: 9px 14px;
        border-radius: 2px;
      }
      .last-span-box {
        display: flex;
        align-items: center;
        margin-right: 0;
        @include font_color(tag-text-yellow);
        @include background_color(warn-color);
        img {
          width: 14px;
          height: 16px;
        }
      }
    }
    .product-detail-ratio-netValue {
      margin-top: 33px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .product-detail-ratio,
      .product-detail-netValue {
        height: 98px;
        // margin-right: 49px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        @include font_color(second-text);
        &:last-child {
          font-size: 24px;
        }
        p {
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
        img {
          width: 24px;
        }
        .fund-up-value {
          @include font_color(k-line-red);
        }
        .fund-drop-value {
          @include font_color(k-line-green);
        }
        .fund-drop-value1 {
          @include font_color(second-text);
        }
      }
      .product-detail-ratio {
        p {
          &:first-child {
            // @include font_color(k-line-red);
            font-size: 48px;
          }
        }
      }
      .product-detail-netValue {
        margin-left: 49px;
        p {
          &:first-child {
            font-size: 40px;
          }
        }
        .last-net-value {
          @include font_color(text-color);
        }
      }
    }
  }
  .dialog-main {
    background-color: rgba(0, 0, 0, 0.28);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog {
      width: 70%;
      @include themeify {
        background: themed("bg-color");
        color: themed("text-color");
      }
      padding: 50px 30px 140px 30px;
      border-radius: 4px;
      font-size: 28px;
      position: relative;
      .content-describe {
        text-align: center;
        line-height: 45px;
      }
      .button-bottom {
        position: absolute;
        bottom: 0px;
        left: 0px;
        text-align: center;
        border-radius: 4px 0px 0px 0px;
        @include themeify {
          background: themed("primary-color");
          color: themed("white-text");
        }
        width: 100%;
        line-height: 90px;
      }
      .button-bottom-risk {
        position: absolute;
        bottom: 0px;
        left: 0px;
        display: flex;

        div {
          border-radius: 4px 0px 0px 0px;
          @include themeify {
            background: themed("primary-color");
            color: themed("white-text");
          }
          width: 50%;
          text-align: center;
        }

        .first-class {
          border-radius: 0px 0px 0px 4px;
          @include themeify {
            background: themed("bg-btn-default");
            color: themed("second-text");
          }
        }
        width: 100%;
        line-height: 90px;
      }
    }
  }
  .product-type-select {
    margin-top: 20px;
    @include background_color(second-bg);
    @include font_color(second-text);
    .product-type-tab {
      padding: 22px 30px 8px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28px;
      .item {
        flex: 1;
        text-align: center;
      }
      li {
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
      .bottom-border {
        margin: 8px auto 0;
        width: 16px;
        height: 5px;
        border-radius: 3px;
      }
      .activeType {
        text-align: center;
        @include font_color(text-color);
        .activeBorder {
          @include background_color(fund-bg);
        }
      }
    }
    .product-components {
      padding: 20px 30px 34px;
      // height: 620px;
    }
  }
  .fund-public-box {
    margin-top: 20px;
    padding: 0 30px;
    @include background_color(second-bg);
    @include font_color(text-color);
    .fund-public-header {
      padding: 28px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        &:first-child {
          font-size: 34px;
          font-weight: bold;
        }
        &:last-child {
          @include font_color(second-text);
          display: flex;
          align-items: center;
          img {
            width: 24px;
            margin-top: 5px;
            margin-left: 10px;
          }
        }
      }
    }
    .invest-distribution {
      padding: 20px 0;
      // height: 398px;
      border-bottom: 1px solid;
      &:last-child {
        border-bottom: 0;
      }
      @include border_color(line-color);
      .invest-title {
        font-size: 34px;
        font-weight: bold;
        @include font_color(text-color);
      }
      .invest-chart {
        overflow: hidden;
      }
      .invest-chart-height1 {
        padding: 44px 0px 40px 13px;
        width: 100%;
        text-align: center;
        .pic-empty {
          width: 331px;
          height: 187px;
        }
        div {
          @include font_color(second-text);
          font-size: 28px;
          height: 72px;
          line-height: 72px;
          text-align: center;
        }
      }
      .invest-chart-height {
        height: 280px;
      }
      .invest-chart-height2 {
        // height: 350px;
        overflow: hidden;
        height: 540px;
      }
      // .invest-chart-open {
      //   height: 780px;
      // }
      .unflod-chart {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        color: #868e9e;
        img {
          width: 27px;
          height: 27px;
          margin-left: 9px;
        }
      }
    }
    .fund-order-box {
      .fund-order-state {
        padding: 40px 0 39px;
        border-bottom: 1px solid;
        @include border_color(line-color);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        ul {
          display: flex;
          justify-content: space-around;
          align-items: center;
          margin-bottom: 20px;
          text-align: center;
          .fund-img-li {
            width: 100%;
            display: flex;
            justify-content: center;
            img {
              width: 510px;
              height: 40px;
            }
          }
        }
        .last-ul {
          @include font_color(btn-color);
          font-size: 24px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          li {
            margin: 0px;
            width: 33%;
            text-align: center;
          }
        }
      }
      .fund-order-rate {
        padding: 40px 0 43px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        @include font_color(second-text);
        font-size: 24px;
        div {
          width: 50%;
          &:last-child {
            margin-left: 20px;
            // p {
            //   &:last-child {
            //     align-items: center;
            //   }
            // }
          }
          p {
            &:last-child {
              margin-top: 8px;
              display: flex;
              align-items: flex-start;
              .span1 {
                font-size: 40px;
                font-weight: normal;
                @include font_color(buy-color);
                margin-right: 20px;
                word-break: break-all; // 解决数字英文字母不换行问题
              }
              .span2 {
                @include font_color(text-color);
                text-decoration: line-through;
              }
            }
          }
          .ladder-charge {
            width: 100%;
            margin: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            span {
              // color: #FD2E1C;
              @include font_color(text-red);
              font-size: 26px;
            }
            img {
              width: 24px;
              height: 24px;
              margin-left: 15px;
            }
          }
        }
      }
      // 阶梯式收费
      .ladder-charge-table {
        padding-bottom: 30px;
        ul {
          // margin: 10px 0;
          border: 2px solid;
          // border-top: 0;
          // border-bottom: 0;
          @include border_color(bg-color-base1);
          @include background_color(bg-color-bg);
          border-radius: 10px;
          li {
            font-size: 24px;
            font-weight: 400;
            @include font_color(text-color);
            // height: 80px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            &:first-child {
              font-weight: 500;
              @include background_color(bg-color-base1);
            }
            div {
              width: 70%;
              padding: 28px 20px;
              display: flex;
              justify-content: center;
              align-items: center;
              text-align: center;
              &:last-child {
                width: 30%;
                border-left: 2px solid;
                @include border_color(bg-color-base1);
              }
            }
          }
        }
      }
    }
    .fund-public-label {
      padding: 40px 0;
      @include font_color(text-color);
      div {
        padding-top: 34px;
        display: flex;
        &:first-child {
          padding-top: 0;
        }
        label {
          min-width: 150px;
        }
        span {
          flex: 1;
          text-align: right;
          overflow: hidden;
          word-wrap: break-word;
        }
      }
    }
  }
  .margin-bottom-69 {
    margin-bottom: 140px;
  }
  .transaction-button {
    position: fixed;
    bottom: 0px;
    left: 0px;
    @include themeify {
      background: themed("bg-color");
      border-top-color: themed("line-color");
    }
    width: 100%;
    border-top: 1px solid;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 40px;
    .apply,
    .sell {
      width: 225px;
      line-height: 100px;
      font-size: 28px;
      color: #fff;
      text-align: center;
    }
    .apply {
      background-color: #2d60e0;
    }
    .sell {
      background-color: #ec8a01;
    }
    .optional {
      @include themeify {
        color: themed("second-text");
      }
      width: 150px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .img {
        padding: 3px 0px;
        img {
          width: 36px;
          height: 36px;
        }
      }
      .font {
        span {
          line-height: 28px;
          font-size: 22px;
        }
      }
    }
  }
}

.show-download {
  padding-top: 100px;
}
</style>
