<template>
  <div class="result-page">
    <img class="status-img" :src="require('@/assets/images/verify.png')" />
    <div class="title">{{title}}</div>
    <div class="tip">{{tip}}</div>
    <md-button type="primary">{{btnText}}</md-button>
  </div>  
</template>
<script>
export default {
  data() {
    return {
      
    }
  },
  props: {
    pageTitle: {
      default: 'ssdsf',
      type: String
    },
    title: {
      default: 'sfsf',
      type: String
    },
    tip: {
      default: 'dfsfdsffsfsfs',
      type: String
    },
    btnText: {
      default: '返回',
      type: String
    }
  }
}
</script>
<style lang="scss" scoped>
  .result-page{
    padding: 0 30px;
    text-align: center;
  }
  .status-img{
    width: 143px;
    height: 143px;
    margin: 118px auto 32px;
  }
  .title{
    font-size: 38px;
    @include font_color(text-color)
  }
  .tip{
    margin-top: 56px;
    margin-bottom: 140px;
    @include font_color(second-text)
  }
</style>
