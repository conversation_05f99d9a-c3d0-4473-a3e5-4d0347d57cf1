<template>
  <div class="md-field-item md-input-item">
    <div class="md-field-item-content">
      <div class="md-field-item-control">
        <input
          v-model="inputValue"
          :maxlength="maxlength"
          :placeholder="placeholder"
          readonly
          type="text"
          autocomplete="off"
          :disabled="disabled"
          class="md-input-item-input"
          @blur="onBlur"
          @input="onInput"
        />
      </div>
    </div>
    <div v-show="$attrs.errors.has($attrs['data-vv-name'])" class="md-field-item-children">
      <div class="md-input-item-msg">{{ $attrs.errors.first($attrs['data-vv-name']) }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EddidInput',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    maxlength: {
      type: Number,
      default: 0,
    },
    readonly: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: '',
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        if(newVal === oldVal) return;

        this.inputValue = newVal;
      },
    }
  },
  methods: {
    onInput(e) {
      this.$emit('input', e.target.value);
    },
    onBlur() {
      let tempVal = this.inputValue ? this.inputValue.trim() : '';
      this.inputValue = tempVal;
      this.$emit('input', tempVal);
    }
  },
}
</script>
