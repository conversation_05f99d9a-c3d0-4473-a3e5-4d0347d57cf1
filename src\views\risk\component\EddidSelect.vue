<template>
  <div class="eddid-select" :class="[showSelect ? 'expend' : 'collapse']">
    <div @click.stop="showSelect = !showSelect" class="select-input">
      <div>{{ innerValue ? innerValue.label : '' }}</div>
      <div class="icon-wrap">
        <img :src="require(`@/assets/images${theme === 'dark' ? '/dark' : '' }/com_ico_arrow2.png`)" class="icon-arrow" />
      </div>
    </div>
    <ul class="select-list">
      <li
        v-for="item in selectList"
        :key="item.value"
        @click.stop="onChange(item)"
        :class="[innerValue && innerValue.value === item.value ? 'selected' : '']"
      >{{ item.label }}</li>
    </ul>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'EddidSelect',
  props: {
    value: {
      type: Object,
      default: () => null,
    },
    selectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      innerValue: null,
      showSelect: false,
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        if(newVal === oldVal) return;

        this.innerValue = newVal;
      },
    }
  },
  computed: {
    ...mapState(['theme']),
  },
  methods: {
    onChange (row) {
      this.$emit('change', row);
      this.showSelect = false;
    },
  },
}
</script>

<style lang="scss" scoped>
.eddid-select {
  position: relative;
}
.select-input {
  @include background_color(bg-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80px;
  margin-top: 20px;
  padding-left: 20px;
  border-radius: 2px;
  @include thin_border($color: input-line, $radius: 2px, $position: before);

  .icon-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    transform-origin: center;
    transition: all 0.2s ease;
  }
  .icon-arrow {
    width: 18px;
    height: 10px;
    object-fit: contain;
  }
}
.select-list {
  overflow: hidden;
  @include background_color(bg-color);
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  display: none;
  width: 100%;
  border-radius: 4px;
  @include box_shadow(shadow-color);

  li {
    padding: 25px 20px;
    @include font_color(text-color);
    font-size: 28px;
    line-height: 28px;
    font-weight: 400;
    transition: all 0.2s ease;
  }

  li.selected {
    @include background_color(bg-color-base);
    color: $primary-color !important;
  }
}

.eddid-select.expend {
  .select-input .icon-wrap {
    transform: rotate(180deg);
  }

  .select-list {
    display: block;
  }
}
</style>
