<template>
  <div class="risk-footer">
    <slot></slot>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.risk-footer {
  @include background_color(bg-color);
  position: fixed !important;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 30px 30px 40px;
  @include thin_border($directions: top, $position: before);
}
::v-deep.md-button.default::after,
::v-deep.md-button.plain.primary:after {
  width: 100%;
  height: 100%;
  border-radius: 2px;
  transform: scale(1);

  @media screen and(-webkit-min-device-pixel-ratio: 2) {
    width: 200%;
    height: 200%;
    border-radius: 4px;
    transform: scale(0.5);
  }
  @media screen and(-webkit-min-device-pixel-ratio: 3) {
    width: 300%;
    height: 300%;
    border-radius: 6px;
    transform: scale(0.3333);
  }
}
::v-deep.md-button.primary:not(.plain) {
  border-radius: 2px;
}
</style>