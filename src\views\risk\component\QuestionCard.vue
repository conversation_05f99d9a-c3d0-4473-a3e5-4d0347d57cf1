<template>
  <div class="question-card">
    <p class="question">{{ data.question }}</p>
    <ul @click="onClick">
      <li
        v-for="option in data.options || []"
        :key="`${data.questionNumber}-${option.value}`"
        :data-value="option.value"
        :data-single="option.single"
        :class="['option-item', isSelected(option) ? 'selected' : '']"
      >
        <div class="select-icon">
          <img
            :src="getImgStr(option)"
            :class="data.type === 'more' ? 'checkbox-icon' : 'radio-icon'"
          />
        </div>
        <div class="option-content">{{ option.value }}. {{ option.label }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'QuestionCard',
  props: {
    data: {
      type: Object,
    },
    value: {
      type: [String, Array],
    },
    allowChange: {
      type: Boolean,
      default: true
    },
    allowAge: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      innerVal: this.value,
      imageCache: {
        multi_select_default: null,
        multi_select_selected: null,
        multi_select_disable2: null,
        single_select_default: null,
        single_select_selected: null,
        single_select_disabled: null
      }
    }
  },
  computed:{
    isDisabled(){
      return  this.data.disabled
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal, oldVal) {
        if(newVal !== oldVal) {
          this.innerVal = newVal;
        }
      },
      immediate:true
    }
  },
  created() {
    // 预加载图片
    this.preloadImages();
  },
  methods: {
    getSelectedClass(option) {
      return this.isSelected(option) ? 'selected' : '';
    },
    preloadImages() {
      const imageNames = Object.keys(this.imageCache);
      imageNames.forEach(name => {
        this.imageCache[name] = require(`@/assets/images/${name}.png`);
      });
    },
    getImgStr(option) {
      if (this.data.type === 'more') {
        if (this.isDisabled && !this.isSelected(option)) {
          return this.imageCache.multi_select_disable2;
        }
        return this.imageCache[!this.isSelected(option) ? 'multi_select_default' : 'multi_select_selected'];
      }
      
      if (this.isDisabled && !this.isSelected(option)) {
        return this.imageCache.single_select_disabled;
      }
      return this.imageCache[!this.isSelected(option) ? 'single_select_default' : 'single_select_selected'];
    },
   onClick(e) {
      // 使用closest找到最近的li元素
      const target = e.target.closest('.option-item');
      if (!target) return;
      
      const current = target.getAttribute('data-value');
      
      const { type, questionNumber, options } = this.data;
      
      // 检查是否允许更改
      if (this.isDisabled) {
        return false;
      }
      
      // 优化值处理逻辑
      let tempVal;
      if (type === 'more') {
        const single = target.getAttribute('data-single') || false;
        if (single) {
          tempVal = [current];
        } else {
          tempVal = Array.isArray(this.value) ? [...this.value] : 
                (this.value ? [this.value] : []);
        
          const index = tempVal.indexOf(current);
          const selectedOptions = options.find((option)=>{
            return tempVal.includes(option.value) && option.single
          });
          if(selectedOptions){
            tempVal = [current];
          }else{
            if (index === -1) {
              tempVal.push(current);
            } else {
              tempVal.splice(index, 1);
            }
          }
        }
        // tempVal = Array.isArray(this.value) ? [...this.value] : 
        //         (this.value ? [this.value] : []);
        
        // const index = tempVal.indexOf(current);
        // if (index === -1) {
        //   tempVal.push(current);
        // } else {
        //   tempVal.splice(index, 1);
        // }
      } else {
        tempVal = this.value === current ? '' : current;
      }
      this.$emit('change', tempVal, questionNumber);
    },
    isSelected({ value }) {
      if (!this.innerVal || (this.data.type === 'more' && !this.innerVal.length)) return false;
      return this.data.type === 'more'
        ? (Array.isArray(this.innerVal) ? this.innerVal : [this.innerVal]).includes(value)
        : value === this.innerVal;
    },
  },
}
</script>

<style lang="scss" scoped>
.question-card {
  width: 100%;
}
.question {
  margin-bottom: 40px;
  line-height: 44px;
  @include font_color(text-color);
  font-size: 30px;
  font-weight: 500;
}
.option-item {
  display: flex;
  @include background_color(list-bg);
  padding: 36px 30px;
  @include font_color(text-color);

  & + & {
    margin-top: 20px;
  }
}
.selected {
  color: $primary-color !important;
  @include thin_border($color: primary-color, $radius: 4px, $position: before);
}
.option-content {
  flex: 1;
}
.select-icon {
  margin-right: 20px;
  flex-shrink: 0;
  
  .checkbox-icon,
  .radio-icon {
    width: 40px;
    height: 40px;
    vertical-align: middle;
  }
}
</style>