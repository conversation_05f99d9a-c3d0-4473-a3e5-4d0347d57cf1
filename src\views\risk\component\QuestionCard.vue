<template>
  <div class="question-card">
    <p class="question">{{ data.question }}</p>
    <ul @click="onClick">
      <li
        v-for="option in data.options || []"
        :key="`${data.questionNumber}-${option.value}`"
        :data-value="option.value"
        :class="['option-item', isSelected(option) ? 'selected' : '']"
      >
        {{ option.value }}. {{ option.label }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'QuestionCard',
  props: {
    data: {
      type: Object,
    },
    value: {
      type: [String, Array],
    },
    allowChange: {
      type: Boolean,
      default: true
    },
    allowAge: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      innerVal: this.value,
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal, oldVal) {
        if(newVal !== oldVal) {
          this.innerVal = newVal;
        }
      },
      immediate:true
    }
  },
  methods: {
    onClick(e) {
      if (e.target && e.target.className.includes('option-item')) {
        const current = e.target.getAttribute('data-value');
        const { type, questionNumber, answer } = this.data;
        if (!this.allowChange && questionNumber===3) {
          return false
        }
        if (!this.allowAge && questionNumber === 1 && answer) {
          return false
        }
        let tempVal;
        if (type === 'more') {
          tempVal = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
          const index = tempVal.findIndex((item) => item === current);
          index === -1 ? tempVal.push(current) : tempVal.splice(index, 1);
        } else {
          tempVal = this.value === current ? '' : current;
        }

        this.$emit('change', tempVal, questionNumber)
      }
    },
    isSelected({ value }) {
      if (!this.innerVal || (this.data.type === 'more' && !this.innerVal.length)) return false;
      return this.data.type === 'more'
        ? (Array.isArray(this.innerVal) ? this.innerVal : [this.innerVal]).includes(value)
        : value === this.innerVal;
    },
  },
}
</script>

<style lang="scss" scoped>
.question-card {
  width: 100%;
}
.question {
  margin-bottom: 40px;
  line-height: 44px;
  @include font_color(text-color);
  font-size: 30px;
  font-weight: 500;
}
.option-item {
  @include background_color(list-bg);
  padding: 36px 30px;
  @include font_color(text-color);

  & + & {
    margin-top: 20px;
  }
}
.selected {
  color: $primary-color !important;
  @include thin_border($color: primary-color, $radius: 4px, $position: before);
}
</style>