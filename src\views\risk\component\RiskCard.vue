<template>
  <div class="tip-card">
    <div class="title">{{ title }}</div>
    <div class="description">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.tip-card {
  & + & {
    margin-top: 50px;
  }
}
.title {
  line-height: 30px;
  margin-bottom: 20px;
  @include font_color(text-color);
  font-size: 30px;
  font-weight: 500;
}
.description {
  @include background_color(list-bg);
  padding: 20px;
  line-height: 38px;
  @include font_color(second-text);
  font-size: 26px;
  font-weight: 400;
}
</style>