<template>
  <div :class="['container-level', $store.state.locale === 'en' ? 'lang-en': '']">
    <nav-header :back="onBack" />
    <div :class="['bg-level']">
      <div :class="[`bg-type${formValue.type || 'R1'}`, 'type-container']">
      <p class="name">{{ $t('risk.evaluationResult') }}</p>
      <div class="type">
        <div class="left-content">
          <span>{{ formValue.type ? $t(`risk.typeText${formValue.type}`) : '' }}</span>
          <span class="img-quotation" />
        </div>
        <div class="right-content">
            <img :src="require(`@/assets/images/risklevel${formValue.type || 'R1'}.png`)" alt="">
          </div>
      </div>
      <p class="description">{{ formValue.type ? $t(`risk.description${formValue.type}`) : '' }}</p>
    </div>
    </div>
    <ul class="content-info">
      <!-- <li class="row-title">
        <span class="text">{{ $t('risk.columnType') }}</span>
        <span class="text">{{ $t('risk.columnDesc') }}</span>
      </li> -->
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName1') }}</label>
        <span class="text">{{ formValue.isProfessionalInvestor || '' }}</span>
      </li>
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName2') }}</label>
        <span class="text">{{ formValue.level }} ({{ formValue.type ? $t(`risk.riskText${formValue.type}`) : '' }})</span>
      </li>
      <!-- <li class="row-item">
        <label class="label">{{ $t('risk.rowName3') }}</label>
        <span class="text">{{ formValue.type ? $t(`risk.typeText${formValue.type}`) : '' }}</span>
      </li> -->
      <li class="row-item">
        <label class="label">{{ $t('risk.rowName4') }}</label>
        <span class="text">{{ formValue.timeHorizon }}</span>
      </li>
      <!-- <li class="row-item">
        <label class="label">{{ $t('risk.rowName5') }}</label>
        <span class="text">{{ formValue.declaredNetAssets }}</span>
      </li> -->
      <!-- <li class="row-item">
        <label class="label">{{ $t('risk.rowName6') }}</label>
        <span class="text">{{ formValue.derivativesKnowledge }}</span>
      </li> -->
    </ul>
    <eddid-agree v-if="!isConfirm" v-model="isHadAgree" class="agree">
			<span>{{ $t('risk.readDeclaration') }}</span>
		</eddid-agree>
    <eddid-agree v-if="!isConfirm && showCheckSelect" v-model="ishadRead" class="agree" :notNeedChangStatus="!ishadRead" @input="toStatement">
			<div>
        <span>{{ $t('risk.hadAgreeCustomerDeclaration') }}</span>
        <span class="link-text" @click="toStatement">{{ $t('risk.customerDeclaration') }}</span>
      </div>
		</eddid-agree>
    <risk-footer v-if="showFooter">
      <!-- 2个 按钮 -->
      <md-button v-if="!isConfirm" @click="onOpenAccount" :type="isDisabled ? 'disabled' : 'primary'">{{ $t('risk.btnOpenAccount') }}</md-button>
      <div v-if="!isConfirm" @click="onRestart" class="text-btn">{{ $t('risk.btnReEvaluate') }}</div>
      <!-- 只有 1 个按钮 -->
      <md-button v-else @click="onRestart" type="primary">{{ $t('risk.btnReEvaluate') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/NavHeader.vue";
import RiskFooter from "./component/Footer.vue";
import { getAccountNumber } from "@/services/account";
import { getRiskLevel, getOpenAccountStatus, confirmQuestions, professionalInvestorAgreement, getQuestionList } from '@/services/risk';
import { queryLink } from '@/services/common'
import EddidAgree from '@/components/EddidAgree.vue';
import { Dialog, Toast } from 'mand-mobile';
import { mapState } from 'vuex';
export default {
  components: { NavHeader, RiskFooter, EddidAgree },
  data() {
    return {
      showFooter: false,
      formValue: {},
      isConfirm: false, // 是否确认结果
      accountStatus: '',
      isComplexProduct: 0,
      isHadAgree: false,
      ishadRead: false,
      pilink:'',
      vaObj: {},
      questionList: [],
      showCheckSelect: false,
    }
  },
  computed: {
    ...mapState(['signBase64']),
    isDisabled() {
      return !this.isHadAgree || (this.showCheckSelect && !this.ishadRead)
    }
  },
  beforeMount() {
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }
    this.getRiskLevel();
  },
  mounted() {
    this.checkSignatureCompletion()
    this.getQuestionList()
    this.getAccountNumber()
    this.$jsBridge.run('navBackByAppSelf', { isClosePage: true })
  },
  methods: {
    // 添加检查签名完成的方法
    checkSignatureCompletion() {
      const signatureTimestamp = sessionStorage.getItem('signatureTimestamp');
      
      // 检查是否有签名完成标记，且时间戳在最近 5 秒内
      if (signatureTimestamp) {
        const now = Date.now();
        const timestamp = parseInt(signatureTimestamp, 10);
        if (now - timestamp < 5000) {
          sessionStorage.removeItem('signatureTimestamp');
          this.isHadAgree = sessionStorage.getItem('isHadAgree') === 'true'
          sessionStorage.removeItem('isHadAgree');
          if (this.signBase64) {
            this.ishadRead = true;
          }
        }
      }else{
        this.$store.commit('setSignBase64', '')
      }
    },
    getQuestionList(){
      getQuestionList().then(res => {
        if(res.code === 200){
          this.questionList = res.data?.jsonText || [];
          this.showCheck()
        }
      })
    },
    showCheck(){
      this.questionList.forEach(item => {
        if((item.questionNumber === 1 && item.answer === 'E') || (item.questionNumber === 2 && item.answer === 'A')){
          this.showCheckSelect = true
        }
      })
    },
    toStatement(val, status){
      console.log(status, 'status')
      if(status && status === 'needChangStatus'){
        return
      }
      sessionStorage.setItem('isHadAgree', this.isHadAgree)
      this.$router.push('/risk/statement')
    },
    async onOpenAccount() {
      if (!this.isHadAgree) return Toast.info(`${this.$t('risk.pleaseChoose')}「${this.$t('risk.readDeclaration')}。」`);
      if (!this.ishadRead && this.showCheckSelect) return Toast.info(`${this.$t('risk.pleaseChoose')}「${this.$t('risk.hadAgreeCustomerDeclaration')}${this.$t('risk.customerDeclaration')}」`);
      // VA交易账户的客户，若该客户在进行基金的风险测评时，其基金风险等级的测评的分值<45分，则不允许客户进行【确认结果无误】的确认提交
      // if(this.vaObj.type && this.vaObj.type === 'VIRTUAL_ASSET' && this.formValue.level !== '5'){
      //   const dialog = Dialog.confirm({
      //     // title: this.$t('risk.confirmExitPage'),
      //     content: this.$t('risk.hadVaAcount'),
      //     cancelText: this.$t('common.btns.cancel'),
      //     confirmText: this.$t('risk.btnReEvaluate'),
      //     onConfirm: () => {
      //       this.onRestart()
      //     },
      //   })
      //   // const el = dialog.$el;
      //   // el && el.classList.add('inter-dialog-confirm');
      //   return
      // }

      if (sessionStorage.getItem('ispi')==='yes') {
        let res = await professionalInvestorAgreement()
        if (res?.code===200) {
          sessionStorage.removeItem('ispi')
          this.getPiLink()
        }
      }else{
        confirmQuestions({signBase64: this.showCheckSelect ? this.signBase64 : ''}).then(res => {
        if(res.code !== 200){
          return
        }
        if(this.accountStatus && this.accountStatus !== 'REJECT') {
          this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/');
        } else {
          if (sessionStorage.getItem('isExpired') === 'yes') {
            this.$jsBridge.run("startEdWeb", {
              url: window.location.origin + `/fundKind`,
              callback: () => {},
            });
          } else {
            this.$router.push('/risk/disclosure');
          }
        }
      })
      }
    },
    onRestart() {
      const dialog = Dialog.confirm({
        title: this.$t('risk.btnReEvaluate'),
        content: this.$t('risk.confirmReEvaluation'),
        cancelText: this.$t('common.btns.cancel'),
        confirmText: this.$t('risk.btnToEvaluate'),
        onConfirm: () => {
          this.$router.push(`/risk/tips`);
        },
      })
      const el = dialog.$el;
      el && el.classList.add('dialog-confirm');
    },
    async getRiskLevel () {
      this.$loading.show();
      try {
        const statusRes = await getOpenAccountStatus({ispi:sessionStorage.getItem('ispi') || 'no'})
        const levelRes = await getRiskLevel({
          ispi:sessionStorage.getItem('ispi') || 'no',
          queryLatestSubmitResult: !!this.$route.query.isLatest
        })
        if(statusRes.code === 200 && statusRes.data){
          this.accountStatus = statusRes.data.status;
        }
        if(levelRes.code === 200){
          this.formValue = {
            isProfessionalInvestor: levelRes.data.isProfessionalInvestor,
            type: levelRes.data.result,
            level: levelRes.data.result ? levelRes.data.result.split('R')[1] : 0,
            timeHorizon: levelRes.data.timeHorizon,
            declaredNetAssets: levelRes.data.declaredNetAssets,
            derivativesKnowledge: levelRes.data.derivativesKnowledge,
          }
          this.isConfirm = levelRes.data.agreement;
          this.showFooter = true;
        }
      } catch (e) {
        console.log('error', e)
      } finally {
        this.$loading.hide();
      }
    },
    onBack () {
      // if(this.isComplexProduct === '1') {
      //   this.$jsBridge.run('toPage', {
      //     jumpType: 'NATIVE',
      //     loginState: 'JUDGMENT',
      //     openAccountState: 'JUDGMENT_FUND',
      //     navigationContentCode: 'FUND_ACCOUNT',
      //     navigationUri : 'FUND_ACCOUNT',
      //     titleDisplay: 'DISPALY'
      //   })
      //   return
      // }
      this.$jsBridge.isSupported('navBack') ? this.$jsBridge.run('navBack', { isClosePage: true }) : this.$router.push('/');
    },
    async getPiLink(){
      let params = {
        key: "/H5_URLs",
      };
      let res = await queryLink(params);
      if (res && res.code === 200) {
        let pilinkArr =
          res.data.filter((item) => {
            return item.keyText === "pi_submit";
          }) || [];
        this.pilink = pilinkArr[0] && pilinkArr[0].valueText;
        this.$jsBridge.isSupported('navBack')?this.$jsBridge.run('navBack', { isClosePage: true }):''
        this.$jsBridge.isSupported('navBack')?
        this.$jsBridge.run('startEdWeb', {
        url:this.pilink,
        callback: () => {},
      }):window.open(this.pilink)
      }
    },
    getAccountNumber(){
      getAccountNumber().then(res => {
        let vaObj = res.data.tradingAccountList.find((item) => {
          return item.type === "VIRTUAL_ASSET";
        });
        this.vaObj = vaObj ? vaObj : {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container-level {
  padding: 108px 0 30px;
}
.bg-level {
  background-size: 100%;
  width: 100vw;
  padding: 32px;
  color: #ffffff;
  .type-container {
    padding: 32px 32px 52px;
    border-radius: 15px;
  }
  .name {
    opacity: 0.8;
    height: 24px;
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 24px;
    line-height: 1;
  }
  .type {
    position: relative;
    margin-bottom: 30px;
    font-weight: 500;
    font-size: 56px;
    line-height: 1;
    vertical-align: text-top;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .right-content {
    width: 160px;
    height: 160px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-quotation {
    background-image: url(~@/assets/images/icon_quotation.png);
    background-size: 41px 32px;
    background-repeat: no-repeat;
    position: absolute;
    top: 50px;
    width: 41px;
    height: 32px;
    margin-left: 20px;
  }
  .description {
    font-size: 26px;
  }
}
.bg-typeR1 {
  background: linear-gradient( 135deg, #08A7E3 0%, #0076C1 100%);
}
.bg-typeR2 {
  background: linear-gradient( 135deg, #07C9D0 0%, #00AAB1 100%);
}
.bg-typeR3 {
  background: linear-gradient( 315deg, #1A51CE 0%, #3679FA 100%);
}
.bg-typeR4 {
  background: linear-gradient( 135deg, #FFA62B 0%, #F47305 100%);
}
.bg-typeR5 {
  background: linear-gradient( 315deg, #F64B41 0%, #FE9366 100%);
}

.content-info {
  @include background_color(bg-color);
  margin: 50px 30px 70px;
  padding-bottom: 30px;
  @include thin_border($color: line-list);

  .row-title {
    @include background_color(list-bg);
    @extend .x-between-y-center;
    padding: 30px;
  }
  .row-item {
    @extend .x-between-y-center;
    padding: 30px 30px 0;
  }
  .label {
    @include font_color(second-text);
    font: 400 28px/28px PingFangSC-Regular, PingFang SC;
  }
  .text {
    @include font_color(text-color);
    font: 400 28px/28px DINPro-Regular, DINPro;
    text-align: right;
  }
}
.agree {
	padding: 0 30px;
}
::v-deep.risk-footer {
  position: static !important;
  text-align: center;

  &::before {
    content: none;
  }

  .md-button {
    line-height: 30px;
    font-size: 30px;
  }

  .text-btn {
    margin: 20px auto;
    padding: 30px;
    color: #2D60E0;
    font-size: 30px;
    line-height: 30px;
    font-weight: 400;
  }
}

.lang-en {
  .bg-level .description {
    font-size: 22px;
  }
}
.link-text{
  @include font_color(primary-color);
}
</style>