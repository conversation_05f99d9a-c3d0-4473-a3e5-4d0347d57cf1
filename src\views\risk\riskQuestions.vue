<template>
	<div :class="['container', isLastQuestion ? 'container-last-question' : '', theme]" ref="container">
		<nav-header :close="onClose" />
		<div v-if="questionList && !!questionList.length" ref="question">
			<p v-if="isFirstQuestion" class="title">{{ $t('risk.choose') }}</p>
			<p class="mgb70">
				<span class="current">{{ currentQuestion ? currentQuestion.questionNumber : '' }}</span>
				<span class="total">/{{ total }}</span>
			</p>

			<question-card
        v-if="!!currentQuestion"
        :value="selectedValue"
        :data="currentQuestion"
        @change="onSelectChange"
        :allowChange="allowChange"
        :allowAge="allowAge"
        data-vv-name="riskQuestion"
        v-validate="'required'"
        data-vv-validate-on="change"
        :errors="this.$validator.errors"
      />
		</div>

		<div v-if="showSourceCheckbox" class="card-employ">
			<div class="font-bold">{{ $t('risk.employment') }}</div>
      <eddid-select :value="employment" :selectList="employmentList" @change="onChangeEmployment" />
		</div>

    <!-- 第4题 -->
		<div v-if="showSourceCheckbox" ref="riskQuestionCard" class="container-source">
			<div class="font-bold">{{ $t('risk.pleaseSource') }}</div>
			<ul>
				<li
          @click="onChangeSource(item)"
          v-for="item in currentQuestion.sourceOfFunds || []"
          :key="item.value"
          :class="[employment && employment.disabled.includes(item.value) ? 'disabled' : '']"
        >
          <img
            v-if="employment && employment.disabled.includes(item.value)"
            :src="require(`@/assets/images${theme === 'dark' ? '/dark' : ''}/checkbox-disabled.png`)"
            class="icon-checkbox"
          />
					<img
            v-else
            :src="require(`@/assets/images${!sourceOfFound.includes(item.value) && theme === 'dark' ? '/dark' : ''}/checkbox${sourceOfFound.includes(item.value) ? '-checked' : ''}.png`)"
            class="icon-checkbox"
          />
					<span>{{ item.label }}</span>
				</li>
			</ul>
			<textarea v-if="sourceOfFound.includes('G')" v-model.trim="sourceOfOther" maxlength="50" :placeholder="$t('risk.pleaseTextarea')" ref="sourceOfFoundInput" :class="['source-textarea', error ? 'error-textarea' : '']" />
		</div>
    <!-- 第3题 -->
		<div v-if="showThridSourceCheckbox" ref="riskThridQuestionCard" class="container-source">
			<div class="font-bold">{{ $t('risk.pleaseSource') }}</div>
			<ul>
				<li @click="onChangeThridSource(item)" v-for="item in currentQuestion.sourceOfFunds || []" :key="item.value">
					<img :src="require(`@/assets/images${!sourceOfThridFound.includes(item.value) && theme === 'dark' ? '/dark' : ''}/checkbox${sourceOfThridFound.includes(item.value) ? '-checked' : ''}.png`)" class="icon-checkbox" />
					<span>{{ item.label }}</span>
				</li>
			</ul>
			<textarea v-if="sourceOfThridFound.includes('C')" v-model.trim="sourceOfThridOther" maxlength="50" :placeholder="$t('risk.pleaseTextarea')" ref="sourceOfThridFoundInput" :class="['source-textarea', thriderror ? 'error-textarea' : '']" @blur="onBlurThrid" @focus="onFocusThrid" />
		</div>

		<risk-footer>
			<md-button @click="onPre" plain class="btn-pre">
				{{ isFirstQuestion ? $t('risk.onBack') : $t('risk.btnPre') }}
			</md-button>
			<md-button v-if="!isLastQuestion" @click="onNext" type="primary">
				{{ $t('risk.btnNext') }}
			</md-button>
			<md-button v-else @click="onSubmit" type="primary">{{ $t('fundMessage.submitText') }}</md-button>
		</risk-footer>

		<risk-card v-if="isLastQuestion" :title="$t('risk.title3')">
			<p>{{ $t('risk.description4') }}</p>
			<p class="mgt20">{{ $t('risk.description5') }}</p>
			<p class="mgt20">{{ $t('risk.description6') }}</p>
		</risk-card>
	</div>
</template>

<script>
import NavHeader from '@/components/NavHeader.vue';
import QuestionCard from './component/QuestionCard.vue';
import EddidSelect from './component/EddidSelect.vue';
import RiskFooter from './component/Footer.vue';
import RiskCard from './component/RiskCard.vue';
import { mapState } from 'vuex';
import { Toast, Dialog } from 'mand-mobile';
import { getQuestionList, submitQuestions ,getPIQuestionList, submitPIQuestions } from '@/services/risk';

export default {
  components: { NavHeader, QuestionCard, EddidSelect, RiskFooter, RiskCard,  },
  data() {
    this.ageGroup = {
      A: [18, 34],
      B: [35, 49],
      C: [50, 64],
      D: [65, 74],
      E: [75, Infinity],
    };

    // 就业情况：
    // employed("employed", "就业"),
    // self("self", "自雇"),
    // retired("retired", "退休"),
    // unemployed("unemployed", "无业"),
    // other("other", "其他"),
    // 来源：
    // { "label": "薪金", "value": "A" },
    // { "label": "投资回报",  "value": "B" },
    // { "label": "租金",  "value": "C" },
    // { "label": "佣金",  "value": "D" },
    // { "label": "退休金", "value": "E" },
    // { "label": "自营业务收益", "value": "F" },
    // { "label": "其他", "value": "G" }
    this.employmentList = [
      { value: 'employed', label: this.$t('risk.employmed'), optional: ['A', 'B', 'C', 'D', 'G'],  required: ['A'], disabled: ['E', 'F'] },
      { value: 'self', label: this.$t('risk.selfCare'), optional: ['A', 'B', 'C', 'F', 'G'],  required: ['F'], disabled: ['D', 'E'] },
      { value: 'retired', label: this.$t('risk.retirement'), optional: ['B', 'C', 'E', 'G'],  required: [], disabled: ['A', 'D', 'F'] },
      { value: 'unemployed', label: this.$t('risk.unEmployed'), optional: ['B', 'C', 'G'],  required: [], disabled: ['A', 'D', 'E', 'F'] },
    ];
    return {
      isQuestion: 1,
      questionList: [],
      total: 0,
      currentQuestion: null,
      name: '',
      idCard: '',
      age: 0,
      selectedInfo: {},
      sourceOfFound: [],
      sourceOfOther: '',
      error: false,
      thriderror:false,
      isComplexProduct: 0,
      sourceOfThridOther:'',
      sourceOfThridFound:[],
      allowChange: true,
      allowAge: true,
      employment: null,
    }
  },
  watch: {
    '$route': {
      deep: true,
      handler(newVal) {
        const { name, params: { id }, } = newVal;
        if (name === 'riskQuestions') {
          Toast.hide();
          id && this.getQuestionByNo(id);
          this.$nextTick(() => {
            if(this.$validator && this.$validator.errors) {
              const { items } = this.$validator.errors;
              items.length && this.$validator.errors.clear();
            }
          })
        }
      }
    },
    'sourceOfOther'(newVal) {
      this.error = this.sourceOfFound.includes('G') && !newVal;
    },
    'sourceOfThridOther'(newVal){
      this.thriderror = this.sourceOfThridFound.includes('C') && !newVal;
    }
  },
  computed: {
    showSourceCheckbox() {
      return this.currentQuestion && `${this.currentQuestion.questionNumber}` === '4' && ['C', 'D', 'E'].includes(this.selectedInfo[4])
    },
    showThridSourceCheckbox() {
        return sessionStorage.getItem('ispi') === 'yes' && this.currentQuestion && this.currentQuestion.questionNumber === 3
    },
    selectedValue() {
      if (!this.currentQuestion) return '';
      const { questionNumber, type, answer } = this.currentQuestion;
      return this.selectedInfo[questionNumber] || (type === 'more' ? [] : '');
    },
    isFirstQuestion() {
      return (
        !this.currentQuestion ||
        !this.currentQuestion.questionNumber ||
        this.currentQuestion.questionNumber === this.questionList[0].questionNumber
      )
    },
    isLastQuestion() {
      if (!this.currentQuestion) return false;
      return (
        this.currentQuestion.questionNumber &&
        this.currentQuestion.questionNumber === this.questionList[this.total - 1].questionNumber
      )
    },
    ...mapState(['theme']),
  },
  beforeMount() {
    this.getQuestionList();
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }
  },
  methods: {
    async onSubmit() {
      const success = await this.$validator.validate()
      if (!success) {
        Toast.info(this.$t('risk.choose'), 2000);
        return false;
      }
      const answerList = [];
      Object.keys(this.selectedInfo).map((key) => {
        const temp = {
          questionNumber: key,
          answer: Array.isArray(this.selectedInfo[key]) ? this.selectedInfo[key].join(',') : this.selectedInfo[key],
        }
        if(`${key}` === '4' && this.sourceOfFound.length) {
          temp.sourceOfFunds = this.sourceOfFound;
          this.sourceOfOther && (temp.sourceOfFundsOther = this.sourceOfOther);
        }
        if(`${key}` === '3' && this.sourceOfThridFound.length) {
          temp.sourceOfFunds = this.sourceOfThridFound;
          this.sourceOfThridOther && (temp.sourceOfFundsOther = this.sourceOfThridOther);
        }
        answerList.push(temp);
      })
      let params={
        name: this.name,
        idNumber: this.idCard,
        answerList,
        submitSource: 'CP_H5'
      }
      // 第4题新增逻辑
      this.employment && (params.employmentStatus = this.employment.value);

      let res={}
      res = sessionStorage.getItem('ispi') === 'yes' ? await submitPIQuestions(params) : await submitQuestions(params);
      if (res?.code === 200) {
        if(this.isComplexProduct === '1') {
          this.$router.push({path:'/risk/level',query: { isComplexProduct: this.isComplexProduct }})
          return
        }
        this.$router.push({
          path: '/risk/level',
          query: {
            isLatest: true
          }
        });
      }
    },
    onNext() {
      this.$validator.validate().then(success => {
        if (!success) {
          Toast.info(this.$t('risk.choose'), 2000);
          return false;
        }

        if((this.showSourceCheckbox && !this.sourceOfFound.length) || (this.showThridSourceCheckbox && !this.sourceOfThridFound.length)) {
          Toast.info(this.$t('risk.pleaseChoseTextarea'), 2000);
          return false;
        }
        if((this.showSourceCheckbox && this.sourceOfFound.includes('G') && !this.sourceOfOther)) {
          this.error = true;
          Toast.info(this.$t('risk.pleaseTextarea'), 2000);
          return false;
        }
        // 第4题新增就业情况与资金来源提示逻辑
        if(this.showSourceCheckbox && this.employment && this.employment.required.some(item => !this.sourceOfFound.includes(item))) {
          this.error = true;
          Toast.info(this.$t('risk.employmentMismatch'), 2000);
          return false;
        }

        if (this.showThridSourceCheckbox && this.sourceOfThridFound.includes('C') && !this.sourceOfThridOther) {
          this.thriderror = true;
          Toast.info(this.$t('risk.pleaseTextarea'), 2000);
          return false;
        }

        const { questionNumber } = this.currentQuestion;
        if(questionNumber <= 1) {
          const ageGroup = this.ageGroup[this.selectedInfo[questionNumber]];
          if(this.age < ageGroup[0] ||  this.age > ageGroup[1]) {
            return Toast.info(this.$t('risk.ageGroupMismatch'), 2000);
          }
        }

        // 过滤 资金来源 属于 就业情况中disabled中的数据
        if(questionNumber === 4 && this.employment) {
          const arr = this.sourceOfFound.filter(item => !this.employment.disabled.includes(item));
          this.sourceOfFound = arr;
        }

        const nextNo = this.questionList[questionNumber].questionNumber;

        if(this.$refs.sourceOfFoundInput) {
          this.$refs.sourceOfFoundInput.blur();
          this.$refs.riskQuestionCard && this.$refs.riskQuestionCard.scrollTo(0, 0);
          setTimeout(() => {
            this.$router.push(`/risk/questions/${nextNo}`);
          }, 50)
        }else if (this.$refs.sourceOfThridFoundInput) {
          this.$refs.container.setAttribute('style','height:100%')
          this.$refs.sourceOfThridFoundInput.blur();
          this.$refs.riskThridQuestionCard && this.$refs.riskThridQuestionCard.scrollTo(0, 0);
          setTimeout(() => {
            this.$router.push(`/risk/questions/${nextNo}`);
          }, 50)
        } else {
          this.$router.push(`/risk/questions/${nextNo}`);
        }
      });
    },
    onClose() {
      if(!Object.values(this.selectedInfo).filter(i => !!i).length) {
        this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/');
        return;
      }

      const dialog = Dialog.confirm({
        title: this.$t('risk.confirmExitPage'),
        content: this.$t('risk.exitPageTip'),
        cancelText: this.$t('common.btns.cancel'),
        confirmText: this.$t('common.btns.confirm'),
        onConfirm: () => {
          this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/');
        },
      })
      const el = dialog.$el;
      el && el.classList.add('dialog-confirm');
    },
    onPre() {
      if(this.isFirstQuestion) {
        const riskModelData = {
          employment: this.employment,
          sourceOfFound: this.sourceOfFound,
          sourceOfOther: this.sourceOfOther,
          sourceOfThridOther: this.sourceOfThridOther,
          sourceOfThridFound: this.sourceOfThridFound,
        }
        if(Object.values(this.selectedInfo).filter(i => !!i).length) {
          riskModelData.selectedInfo = this.selectedInfo;
        }
        sessionStorage.setItem('riskModel', JSON.stringify(riskModelData));
        this.$router.push('/risk/assessment');
        return;
      }

      const no = Number(this.currentQuestion.questionNumber) - 2;
      const preNo = this.questionList[no].questionNumber;
      this.$router.push(`/risk/questions/${preNo}`);
    },
    onChangeEmployment(info) {
      this.employment = info;
      this.sourceOfFound = [...info.required];
    },
    onChangeSource({ value }) {
      if(this.employment && this.employment.disabled.includes(value)) return;

      if(this.sourceOfFound.includes(value)) {
        this.sourceOfFound  = this.sourceOfFound.filter(i => i !== value);
      } else {
        this.sourceOfFound.push(value);
      }
      this.$nextTick(() => {
        this.onClearSourceInfo('2')
      })
    },
    onChangeThridSource({ value }){
      if(this.sourceOfThridFound.includes(value)) {
        this.sourceOfThridFound  = this.sourceOfThridFound.filter(i => i !== value);
      } else {
        this.sourceOfThridFound.push(value);
      }
    },
    onSelectChange(value, key) {
      this.$set(this.selectedInfo, `${key}`, value);
      `${key}` === '4' && this.$nextTick(() => {
        this.onClearSourceInfo('1')
      })
    },
    onClearSourceInfo(type) {
      switch(type) {
        case '1': {
          if(!['C', 'D', 'E'].includes(this.selectedInfo[4])) {
            this.sourceOfFound = [];
            this.sourceOfOther = '';
          }
          break;
        }
        case '2': {
          if(!this.sourceOfFound.includes('G')) {
            this.sourceOfOther = '';
          }
          break;
        }
      }
    },
    async getQuestionList() {
      try {
        this.$loading.show();
        let res = {}
        sessionStorage.getItem('ispi') === 'yes'? res = await getPIQuestionList():res = await getQuestionList()
        if (res?.code === 200) {
          const { jsonText = [], user, finishedList = [], isFinished } = res.data;
          const { name, idNumber, age, employmentStatus } = user || {};
          this.questionList = jsonText;
          this.total = jsonText.length;
          this.name = name || '';
          this.idCard = idNumber || '';
          this.age = age || 0;
          const defaultEmploy = employmentStatus ? this.employmentList.find(item => item.value === employmentStatus) : null;

          const first = this.questionList[0];
          this.currentQuestion = first;
          // 取 storage 赋值
          let riskModelData = sessionStorage.getItem('riskModel');
          const { selectedInfo, sourceOfThridFound, sourceOfThridOther, employment, sourceOfFound, sourceOfOther, } = riskModelData ? JSON.parse(riskModelData) : {};
          this.selectedInfo = selectedInfo || { [`${first.questionNumber}`]: first.type === 'more' ? [] : '', };
          this.sourceOfThridFound = sourceOfThridFound || [];
          this.sourceOfThridOther = sourceOfThridOther || '';
          this.employment = employment || defaultEmploy || null;
          this.sourceOfFound = sourceOfFound || (this.employment ? [...this.employment.required] : []) || [];
          this.sourceOfOther = sourceOfOther || '';

          if (isFinished) {
            // PI风险测评 存在有效的风险测评结果 其他默认 第三题留空 第七题需要转换一下格式
            finishedList.map(item=>{
              this.$set(this.selectedInfo,`${item.questionNumber}`,item.answer)
              if (item.questionNumber===7) {
                let anser7=item.answer.split(',')
                this.$set(this.selectedInfo,7,anser7)
              }
              if (item.questionNumber===4) {
                this.sourceOfFound = item.sourceOfFunds ? item.sourceOfFunds : [];
                this.sourceOfOther = item.sourceOfFundsOther ? item.sourceOfFundsOther : '';
              }
            })
            this.$set(this.selectedInfo,'3','')
          }
          // 基金风险评测- 根据用户信息自动选择, 第一题-不允许修改
          if (this.currentQuestion && this.currentQuestion.answer) {
            this.$set(this.selectedInfo, '1', this.currentQuestion.answer)
            this.allowAge = false
          }
          // 基金风险测评 客户是“专业投资者”，第3题默认选中“E”，不允许修改
          if (sessionStorage.getItem('ispi') !== 'yes' && user.isProfessionalInvestor === 'Y') {
            this.$set(this.selectedInfo,'3','E')
            this.allowChange = false
          }
          sessionStorage.removeItem('riskModel');
        }
      } catch (error) {
        console.log(error);
      } finally{
        this.$loading.hide();
      }
    },
    getQuestionByNo(questionNumber) {
      const currentQuestion = this.questionList.find((item) => `${item.questionNumber}` === `${questionNumber}`);
      this.currentQuestion = currentQuestion || null;
    },
    onBlurThrid(){
      this.$refs.container.setAttribute('style','height:100vh')
    },
    onFocusThrid(){
      this.$refs.container.setAttribute('style','height:100%')
    }
  },
}
</script>

<style lang="scss" scoped>
.font-bold {
  font-weight: 500;
}
.container {
	padding-top: 118px;
	padding-bottom: 230px;
}
.title {
	margin-bottom: 40px;
	@include font_color(text-color);
	line-height: 34px;
	font-size: 34px;
	font-weight: bold;
}
.current {
	@include font_color(primary-color);
	line-height: 50px;
	font-size: 50px;
	font-weight: 500;
}
.card-employ {
  margin-top: 70px;
  @include font_color(text-color);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;
}
.container-source {
	margin-top: 70px;
	@include font_color(text-color);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;

	li {
		display: inline-flex;
		align-items: center;
		margin-top: 40px;
		margin-right: 70px;
	}
  li.disabled {
    cursor: not-allowed;
  }
	.icon-checkbox {
		background-size: 100%;
		background-repeat: no-repeat;
		width: 28px;
		height: 28px;
		margin-right: 14px;
	}
	.source-textarea {
		@include background_color(list-bg);
		width: 100%;
		min-height: 162px;
		padding: 24px 30px;
		margin-top: 40px;
		@include font_color(text-color);
		line-height: 1.3;
		font-size: 28px;
		font-weight: 400;
		border: none;
		outline: none;

		&.error-textarea {
			border: 1px solid $error-color;
		}
	}
}
.total {
	@include font_color(btn-second);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;
}
::v-deep.risk-footer {
	display: flex;

	.md-button {
		line-height: 30px;
		font-size: 30px;

		&.default:not(:disabled).btn-pre {
			background-color: #f9fbff;
			color: $primary-color;
			&::after {
				border-color: $primary-color;
			}
		}
	}

	.md-button + .md-button {
		margin-left: 20px;
	}
}
.container-last-question {
	padding-bottom: 50px;

	::v-deep.tip-card .description {
		padding-bottom: 54px;
	}

	::v-deep.risk-footer {
		position: relative !important;
		padding: 50px 0 60px;

		&::before {
			content: none;
		}
	}
}

.dark {
	.total {
		opacity: 70%;
	}
	.btn-pre::v-deep.md-button.default:not(:disabled) {
		background-color: #21232d;
		color: #868e9e;

		&::after {
			border-color: #000000;
		}
	}
}
</style>
