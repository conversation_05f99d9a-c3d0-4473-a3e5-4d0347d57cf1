<template>
	<div :class="['container', isLastQuestion ? 'container-last-question' : '', theme]" ref="container">
		<nav-header :close="onClose" />
		<div v-if="questionList && !!questionList.length" ref="question">
			<p v-if="isFirstQuestion" class="title">{{ $t('risk.choose') }}</p>
			<p class="mgb70">
				<span class="current">{{ currentQuestion ? currentQuestion.questionNumber : '' }}</span>
				<span class="total">/{{ total }}</span>
			</p>

			<question-card
        v-if="!!currentQuestion"
        :value="selectedValue"
        :data="currentQuestion"
        @change="onSelectChange"
        :allowChange="allowChange"
        :allowAge="allowAge"
        data-vv-name="riskQuestion"
        v-validate="'required'"
        data-vv-validate-on="change"
        :errors="this.$validator.errors"
      />
		</div>
		<risk-footer>
			<md-button @click="onPre" plain class="btn-pre">
				{{ isFirstQuestion ? $t('risk.onBack') : $t('risk.btnPre') }}
			</md-button>
			<md-button v-if="!isLastQuestion" @click="onNext" :type="selectedValue && selectedValue.length ? 'primary' : 'disabled'">
				{{ $t('risk.btnNext') }}
			</md-button>
			<md-button v-else @click="onSubmit" :type="selectedValue && selectedValue.length ? 'primary' : 'disabled'">{{ $t('fundMessage.submitText') }}</md-button>
		</risk-footer>

		<risk-card v-if="isLastQuestion" :title="$t('risk.title3')">
			<p>{{ $t('risk.description4') }}</p>
			<p class="mgt20">{{ $t('risk.description5') }}</p>
			<!-- <p class="mgt20">{{ $t('risk.description6') }}</p> -->
		</risk-card>
	</div>
</template>

<script>
import NavHeader from '@/components/NavHeader.vue';
import QuestionCard from './component/QuestionCard.vue';
import RiskFooter from './component/Footer.vue';
import RiskCard from './component/RiskCard.vue';
import { mapState } from 'vuex';
import { Toast, Dialog } from 'mand-mobile';
import { getQuestionList, submitQuestions ,getPIQuestionList, submitPIQuestions } from '@/services/risk';

export default {
  components: { NavHeader, QuestionCard, RiskFooter, RiskCard,  },
  data() {
    // this.ageGroup = {
    //   A: [18, 34],
    //   B: [35, 49],
    //   C: [50, 64],
    //   D: [65, 74],
    //   E: [75, Infinity],
    // };

    // 就业情况：
    // employed("employed", "就业"),
    // self("self", "自雇"),
    // retired("retired", "退休"),
    // unemployed("unemployed", "无业"),
    // other("other", "其他"),
    // 来源：
    // { "label": "薪金", "value": "A" },
    // { "label": "投资回报",  "value": "B" },
    // { "label": "租金",  "value": "C" },
    // { "label": "佣金",  "value": "D" },
    // { "label": "退休金", "value": "E" },
    // { "label": "自营业务收益", "value": "F" },
    // { "label": "其他", "value": "G" }
    this.employmentList = [
      { value: 'employed', label: this.$t('risk.employmed'), optional: ['A', 'B', 'C', 'D', 'G'],  required: ['A'], disabled: ['E', 'F'] },
      { value: 'self', label: this.$t('risk.selfCare'), optional: ['A', 'B', 'C', 'F', 'G'],  required: ['F'], disabled: ['D', 'E'] },
      { value: 'retired', label: this.$t('risk.retirement'), optional: ['B', 'C', 'E', 'G'],  required: [], disabled: ['A', 'D', 'F'] },
      { value: 'unemployed', label: this.$t('risk.unEmployed'), optional: ['B', 'C', 'G'],  required: [], disabled: ['A', 'D', 'E', 'F'] },
    ];
    return {
      isQuestion: 1,
      questionList: [],
      total: 0,
      currentQuestion: null,
      name: '',
      idCard: '',
      age: 0,
      selectedInfo: {},
      error: false,
      isComplexProduct: 0,
      allowChange: true,
      allowAge: true,
      employment: null,
    }
  },
  watch: {
    '$route': {
      deep: true,
      handler(newVal) {
        const { name, params: { id }, } = newVal;
        if (name === 'riskQuestions') {
          Toast.hide();
          id && this.getQuestionByNo(id);
          this.$nextTick(() => {
            if(this.$validator && this.$validator.errors) {
              const { items } = this.$validator.errors;
              items.length && this.$validator.errors.clear();
            }
          })
        }
      }
    },
  },
  computed: {
    showSourceCheckbox() {
      return this.currentQuestion && `${this.currentQuestion.questionNumber}` === '4' && ['C', 'D', 'E'].includes(this.selectedInfo[4])
    },
    showThridSourceCheckbox() {
        return sessionStorage.getItem('ispi') === 'yes' && this.currentQuestion && this.currentQuestion.questionNumber === 3
    },
    selectedValue() {
      if (!this.currentQuestion) return '';
      const { questionNumber, type, answer } = this.currentQuestion;
      return this.selectedInfo[questionNumber] || (type === 'more' ? [] : '');
    },
    isFirstQuestion() {
      return (
        !this.currentQuestion ||
        !this.currentQuestion.questionNumber ||
        this.currentQuestion.questionNumber === this.questionList[0].questionNumber
      )
    },
    isLastQuestion() {
      if (!this.currentQuestion) return false;
      return (
        this.currentQuestion.questionNumber &&
        this.currentQuestion.questionNumber === this.questionList[this.total - 1].questionNumber
      )
    },
    ...mapState(['theme']),
  },
  beforeMount() {
    this.getQuestionList();
    if(this.$route.query.isComplexProduct) {
      this.isComplexProduct = this.$route.query.isComplexProduct;
    }
  },
  methods: {
    async onSubmit() {
      const success = await this.$validator.validate()
      if (!success) {
        Toast.info(this.$t('risk.choose'), 2000);
        return false;
      }
      const answerList = [];
      Object.keys(this.selectedInfo).map((key) => {
        const temp = {
          questionNumber: key,
          answer: Array.isArray(this.selectedInfo[key]) ? this.selectedInfo[key].join(',') : this.selectedInfo[key],
        }
        answerList.push(temp);
      })
      let params={
        name: this.name,
        idNumber: this.idCard,
        answerList,
        submitSource: 'CP_H5'
      }
      // 第4题新增逻辑
      this.employment && (params.employmentStatus = this.employment.value);

      let res={}
      res =  await submitQuestions(params);
      sessionStorage.setItem('selectedInfo', '')
      if (res?.code === 200) {
        if(this.isComplexProduct === '1') {
          this.$router.push({path:'/risk/level',query: { isComplexProduct: this.isComplexProduct, isLatest: true }})
          return
        }
        this.$router.push({
          path: '/risk/level',
          query: {
            isLatest: true
          }
        });
      }
    },
    onNext() {
      this.$validator.validate().then(success => {
        if (!success) {
          Toast.info(this.$t('risk.choose'), 2000);
          return false;
        }

        const { questionNumber } = this.currentQuestion;

        const nextNo = this.questionList[questionNumber].questionNumber;

        this.$router.push(`/risk/questions/${nextNo}`);
      });
    },
    onClose() {
      if(!Object.values(this.selectedInfo).filter(i => !!i).length) {
        this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/');
        return;
      }

      const dialog = Dialog.confirm({
        title: this.$t('risk.confirmExitPage'),
        content: this.$t('risk.exitPageTip'),
        cancelText: this.$t('common.btns.cancel'),
        confirmText: this.$t('common.btns.confirm'),
        onConfirm: () => {
          this.$jsBridge.isSupported('navBack')
            ? this.$jsBridge.run('navBack', { isClosePage: true })
            : this.$router.push('/');
        },
      })
      const el = dialog.$el;
      el && el.classList.add('dialog-confirm');
    },
    onPre() {
      if(this.isFirstQuestion) {
       
        this.$router.push('/risk/assessment');
        return;
      }

      const no = Number(this.currentQuestion.questionNumber) - 2;
      const preNo = this.questionList[no].questionNumber;
      this.$router.push(`/risk/questions/${preNo}`);
      this.setFirstStauts();
      
    },
    onSelectChange(value, key) {
      this.$set(this.selectedInfo, `${key}`, value);
    },
    async getQuestionList() {
      try {
        this.$loading.show();
        let res = {}
        res = await getQuestionList()
        if (res?.code === 200) {
          let { jsonText = [], user, finishedList = [], isFinished } = res.data;
          const { name, idNumber, age, employmentStatus } = user || {};
          jsonText = this.setSingleOption(jsonText)
          this.questionList = jsonText;
          this.total = jsonText.length;
          this.name = name || '';
          this.idCard = idNumber || '';
          this.age = age || 0;

          const first = this.$route.params.id ? this.questionList[this.$route.params.id - 1] : this.questionList [0];
          this.currentQuestion = first;
          this.setFirstStauts()
          this.getPreSelected()
        }
      } catch (error) {
        console.log(error);
      } finally{
        this.$loading.hide();
      }
    },
    setSingleOption(jsonText) {
      jsonText.forEach(item => {
        if(item.questionNumber === 6){
          item.options.forEach(option => {
            if(option.value === 'F' || option.value === 'G'){
              option.single = true
            }
          })
        } 
        if(item.questionNumber === 7){
          item.options.forEach(option => {
            if(option.value === 'A'){
              option.single = true
            }
          })
        } 
      })
      return jsonText
    },
    setFirstStauts(){
      // 基金风险评测- 根据用户信息自动选择, 第一题-不允许修改
      if (this.currentQuestion && this.currentQuestion.answer && this.currentQuestion.questionNumber ===1) {
        this.$set(this.selectedInfo, '1', this.currentQuestion.answer)
        this.currentQuestion.disabled = true
      }
    },
    getPreSelected() {
      this.questionList.forEach(item => {
        if(item.answer && !this.selectedInfo[item.questionNumber]) {
          this.$set(this.selectedInfo, item.questionNumber + '', item.answer.includes(',') ? item.answer.split(',') : item.answer)
        }
      })
    },
    getQuestionByNo(questionNumber) {
      const currentQuestion = this.questionList.find((item) => `${item.questionNumber}` === `${questionNumber}`);
      this.currentQuestion = currentQuestion || null;
      this.setFirstStauts();
    }
  },
}
</script>

<style lang="scss" scoped>
.font-bold {
  font-weight: 500;
}
.container {
	padding-top: 118px;
	padding-bottom: 230px;
}
.title {
	margin-bottom: 40px;
	@include font_color(text-color);
	line-height: 34px;
	font-size: 34px;
	font-weight: bold;
}
.current {
	@include font_color(primary-color);
	line-height: 50px;
	font-size: 50px;
	font-weight: 500;
}
.card-employ {
  margin-top: 70px;
  @include font_color(text-color);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;
}
.container-source {
	margin-top: 70px;
	@include font_color(text-color);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;

	li {
		display: inline-flex;
		align-items: center;
		margin-top: 40px;
		margin-right: 70px;
	}
  li.disabled {
    cursor: not-allowed;
  }
	.icon-checkbox {
		background-size: 100%;
		background-repeat: no-repeat;
		width: 28px;
		height: 28px;
		margin-right: 14px;
	}
	.source-textarea {
		@include background_color(list-bg);
		width: 100%;
		min-height: 162px;
		padding: 24px 30px;
		margin-top: 40px;
		@include font_color(text-color);
		line-height: 1.3;
		font-size: 28px;
		font-weight: 400;
		border: none;
		outline: none;

		&.error-textarea {
			border: 1px solid $error-color;
		}
	}
}
.total {
	@include font_color(btn-second);
	line-height: 28px;
	font-size: 28px;
	font-weight: 400;
}
::v-deep.risk-footer {
	display: flex;

	.md-button {
		line-height: 30px;
		font-size: 30px;

		&.default:not(:disabled).btn-pre {
			background-color: #f9fbff;
			color: $primary-color;
			&::after {
				border-color: $primary-color;
			}
		}
	}

	.md-button + .md-button {
		margin-left: 20px;
	}
}
.container-last-question {
	padding-bottom: 50px;

	::v-deep.tip-card .description {
		padding-bottom: 54px;
	}

	::v-deep.risk-footer {
		position: relative !important;
		padding: 50px 0 60px;

		&::before {
			content: none;
		}
	}
}

.dark {
	.total {
		opacity: 70%;
	}
	.btn-pre::v-deep.md-button.default:not(:disabled) {
		background-color: #21232d;
		color: #868e9e;

		&::after {
			border-color: #000000;
		}
	}
}
</style>
