<template>
  <div>
    <nav-header :back="onBack" />
    <div class="content">
      <img class="img-wait" :src="require('@/assets/images/verify.png')" />
      <div class="title">{{$t('risk.underReview')}}</div>
      <div class="tip">{{$t('risk.waitMinutes')}}</div>
      <md-button @click="onBack" type="primary">{{$t('myAccount.return')}}</md-button>
    </div>
  </div>
</template>

<script>
import NavHeader from "@/components/NavHeader.vue";
export default {
  components: { NavHeader, },
  data() {
    return {
    }
  },
  mounted() {
    this.$jsBridge.run('navBackByAppSelf', { isClosePage: true })
  },
  methods: {
    onBack() {
      if(this.$jsBridge.isSupported('navBack')){
        this.$jsBridge.run('navBack', { isClosePage: true });
      }else {
        this.$router.push('/');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 60px 30px 0;
  text-align: center;
}
.img-wait {
  width: 143px;
  height: 143px;
  margin: 118px auto 32px;
}
.title {
  @include font_color(text-color);
  font: 500 38px PingFangSC-Medium, PingFang SC;
  text-align: center;
}
.tip {
  margin: 56px auto 140px;
  @include font_color(second-text);
  font: 400 28px PingFangSC-Medium, PingFang SC;
  text-align: center;
}
</style>
