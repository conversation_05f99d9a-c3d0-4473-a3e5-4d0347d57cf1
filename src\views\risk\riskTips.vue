<template>
  <div class="container">
    <nav-header :back="onBack" />
    <risk-card :title="$t('risk.title1')">
      <p v-for="(item, index) in $t('risk.description1')" :key="index">{{ item }}</p>
    </risk-card>
    <risk-card :title="$t('risk.title2')">
      <p>{{$t('risk.description2')}}</p>
      <p class="mgt20">{{$t('risk.description3')}}</p>
    </risk-card>
    <risk-footer>
      <md-button @click="onToPath" type="primary">{{$t('risk.hadGreen')}}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/NavHeader.vue";
import RiskCard from "./component/RiskCard.vue";
import RiskFooter from "./component/Footer.vue";
import { urlParse } from '@/utils/util';
import {Dialog } from 'mand-mobile';
import { getTotalAttempts } from '@/services/risk';
export default {
  components: { NavHeader, RiskCard, RiskFooter },
  data() {
    return {
      isFinish:false,
      attempts: {}
    };
  },
  created(){
    // 是否是PI认证
    const ispi = urlParse(window.location.search).ispi || 'no'
    sessionStorage.setItem('ispi', ispi)
    const isExpired = urlParse(window.location.search).isExpired || 'no'
    sessionStorage.setItem('isExpired', isExpired)
    this.getTotalAttempts()
  },
  methods: {
    async onToPath() {
      
      if (!this.isFinish) {
        this.$loading.show(); // 显示加载中
        await this.getTotalAttempts();
        this.$loading.hide(); // 隐藏加载中
      }
      if(this.attempts.last30DaysAttemptTotal >= this.attempts.last30DaysMaxAttemptTotal){
        let dialog = Dialog.confirm({
          content: this.$t('risk.hadFiveTimes', {number: this.attempts.last30DaysMaxAttemptTotal}),
          cancelText: this.$t('common.btns.cancel'),
          confirmText: this.$t('risk.continueAssessment'),
          onConfirm: () => {
            this.continueJudge()
          },
        })
        return
      }
      this.continueJudge()
      
    },
    continueJudge() {
      if(this.attempts.currentDaysAttemptTotal >= this.attempts.currentDaysMaxAttemptTotal){
        let dialog = Dialog.alert({
          content: this.$t('risk.useUpChange'),
          confirmText: this.$t('common.btns.gotIt'),
        })
      }else if(this.attempts.currentDaysAttemptTotal < this.attempts.currentDaysMaxAttemptTotal){
        let dialog = Dialog.confirm({
          content: this.$t('risk.haveChanges',{number:this.attempts.currentDaysMaxAttemptTotal - this.attempts.currentDaysAttemptTotal}),
          cancelText: this.$t('common.btns.cancel'),
          confirmText: this.$t('common.btns.goOn'),
          onConfirm: () => {
            this.$router.push('/risk/assessment');
          },
        })
      }
    },
    async getTotalAttempts() {
      try {
        const { data } = await getTotalAttempts();
        this.attempts = data;
        this.isFinish = true;
        return data; 
      } catch (error) {
        this.isFinish = false;
      }
    },
    onBack () {
      this.$jsBridge.isSupported('navBack')
          ? this.$jsBridge.run('navBack', { isClosePage: true })
          : this.$router.push('/')
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-top: 118px;
  padding-bottom: 230px;
}

</style>