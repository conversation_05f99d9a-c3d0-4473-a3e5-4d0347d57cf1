<template>
  <div class="sign-content">
    <div class="wrap">
      <div class="actionsWrap">
        <div class="actions">
          <md-button class="back" inline type="default"  @click="returnPage">{{ $t('common.btns.back')}}</md-button>
          <md-button inline type="default"  class="resign" @click="handleClear">{{ $t('common.btns.resign')}}</md-button>
          <md-button inline type="primary"  @click="handleCommit">{{ $t('common.btns.submit')}}</md-button>
          <!-- <button @click="handleUndo">撤销</button>
          <button @click="handleRedo">重做</button>
          <button @click="handleColor">改变颜色</button>
          <button @click="handleFull">全屏</button> -->
        </div>
      </div>
      <canvas class="canvas" id="signContent" ref="signContent"  :style="{'background':isShowSample?`url(${showSignatureImg}) no-repeat center`:'#fff'}"></canvas>
      <div class="title-wrap">
        <div class="sign-title">{{ $t('risk.signTitle')}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import signature from '@/assets/images/signature_sample.png'
import signatureHant from '@/assets/images/signature_sample_hant.png'
import signatureEn from '@/assets/images/signature_sample_en.png'
import SmoothSignature from "smooth-signature";
import { Toast, Dialog } from 'mand-mobile';
export default {
  name: "sign",
  data() {
    return {
      showFull: true,
      isFirst: true,
      isShowSample: true
    };
  },
  computed: {
    ...mapState(["locale", "theme", "accountNumber", "klineTheme"]),
    showSignatureImg() {
      let langType = this.locale || 'zh-hans';
      const langTypeObj = {
        'zh-hans': signature,
        'zh-hant': signatureHant,
        en: signatureEn,
      };
      return langTypeObj[langType];
    },
  },
  mounted() {
    this.initSignature();
  },
  methods: {
    initSignature() {
      const canvas = this.$refs.signContent;
      const options = {
        minWidth: 2,
        maxWidth: 6,
        onStart: this.onStart,
        // bgColor: '#fff',
      };
      this.signature = new SmoothSignature(canvas, options);
    },
    onStart(){
      this.isShowSample = false
    },
    handleClear() {
      this.isShowSample = true
      this.signature.clear();
    },
    handleUndo() {
      
      this.signature.undo();
      const isEmpty = this.signature.isEmpty();
      if (isEmpty) {
        this.isShowSample = true
      }
    },
    handleRedo() {
      this.signature.redo();
    },
    handleFull() {
      this.showFull = !this.showFull;
    },
    toImg() {
      const isEmpty = this.signature.isEmpty();
      if (isEmpty) {
        return;
      }
      const pngUrl = this.signature.getPNG();
    },

    handleColor() {
      this.signature.color = this.getRandomColor();
    },
    getRandomColor() {
      return '#' + Math.random().toString(16).slice(-6);
    },
    returnPage(){
      this.$router.go(-1)
    },
    handleCommit(){
      const isEmpty = this.signature.isEmpty();
      if (isEmpty) {
        Toast.info(this.$t('risk.pleaseSign'))
        return;
      }
      Dialog.alert({
        content: this.$t('risk.weWillCallyou'),
        confirmText: this.$t('common.btns.gotIt'),
        onConfirm: () => {
          const canvas = this.signature.getRotateCanvas(-90);
          const pngUrl = this.cropAndResizeSignature(canvas, 150);
          this.$store.commit('setSignBase64', pngUrl)
          sessionStorage.setItem('signatureTimestamp', Date.now().toString());
          this.$router.go(-2)
        },
      })
    },
    // 添加裁剪和调整大小的方法
    cropAndResizeSignature(canvas, targetHeight) {
      // 获取签名的边界
      const ctx = canvas.getContext('2d');
      const pixels = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = pixels.data;
      
      let minX = canvas.width;
      let minY = canvas.height;
      let maxX = 0;
      let maxY = 0;
      
      // 查找签名的边界
      for (let y = 0; y < canvas.height; y++) {
        for (let x = 0; x < canvas.width; x++) {
          const i = (y * canvas.width + x) * 4;
          // 检查像素是否不透明（签名部分）
          if (data[i+3] > 0) {
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          }
        }
      }
      
      // 添加一些边距
      const padding = 10;
      minX = Math.max(0, minX - padding);
      minY = Math.max(0, minY - padding);
      maxX = Math.min(canvas.width, maxX + padding);
      maxY = Math.min(canvas.height, maxY + padding);
      
      // 计算裁剪区域的宽度和高度
      const width = maxX - minX;
      const height = maxY - minY;
      
      // 如果没有签名内容，返回原始画布
      if (width <= 0 || height <= 0) {
        return canvas.toDataURL();
      }
      
      // 创建一个新的画布用于裁剪
      const croppedCanvas = document.createElement('canvas');
      const croppedCtx = croppedCanvas.getContext('2d');
      
      // 计算新的宽度，保持宽高比
      const targetWidth = Math.round((width / height) * targetHeight);
      
      // 设置裁剪画布的大小
      croppedCanvas.width = targetWidth;
      croppedCanvas.height = targetHeight;
      
      // 绘制裁剪后的图像
      croppedCtx.drawImage(
        canvas,
        minX, minY, width, height,
        0, 0, targetWidth, targetHeight
      );
      
      // 返回裁剪后的图像的 base64 数据
      return croppedCanvas.toDataURL();
    }
  },
};
</script>

<style lang="scss" scoped>
.sign-content {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: var(--gray_05);
  button {
    font-size: 28px;
    margin-right: 40px;
    width: 240px;
  }
  .back{
    width: 240px;
  }
 .canvas {
    border-radius: 30px;
    width: calc(100% - 242px);
    margin-left: 32px;
    background-size: 100% 100% !important
  }
  .wrap {
    position: absolute;
    left: 40px;
    right: 96px;
    top: 50px;
    bottom: 50px;
    padding: 0;
    display: flex;
    justify-content: center;
    .actionsWrap {
      width: 66px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .canvas {
      flex: 1;
    }
    .actions {
      margin-bottom: 40px;
      white-space: nowrap;
      transform: rotate(90deg);
    }
    .title-wrap{
      width: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .sign-title{
      white-space: nowrap;
      transform: rotate(90deg);
      margin-left: 66px;
      color: var(--text_1st);
      font-size: 32px;
    }
  }
}
.resign.default {
  @include font_color(primary-color);
  @include border_color(primary-color);
  border-width: 1px;
}
.resign.default::after{
  @include border_color(primary-color);
}
</style>