<template>
  <div :class="['risk-container', $store.state.locale === 'en' ? 'lang-en': '']">
    <nav-header />

    <div v-for="(item, index) in $t('risk.statement')" :key="index" :class="['card', index > 0 ? 'mgt-70' : '']">
      <p class="content">{{ !isEnglish ? item.zh : item.en }}</p>
      <p v-if="!isEnglish" class="description">{{ item.en }}</p>
    </div>

    <risk-footer>
      <md-button @click="onSubmit" type="primary">{{ $t('risk.readSign') }}</md-button>
    </risk-footer>
  </div>
</template>

<script>
import NavHeader from "@/components/NavHeader.vue";
import RiskFooter from './component/Footer.vue';
import { mapState } from 'vuex';

export default {
  components: { NavHeader, RiskFooter },
  data() {
    return {
      agreeInfo: {
        fundRiskWarning: null,
        fundSales: null
      },
      isAgree: false,
      isAgree2: false
    }
  },
  computed: {
    ...mapState(['theme', 'locale']),
    isEnglish() {
      return this.locale === 'en';
    }
  },
  beforeMount() {
    // this.getAgreeURL('fundRiskWarning')
    // this.getAgreeURL('fundSales')
  },
  methods: {
    onSubmit() {
      this.$router.push('/risk/sign')
    },
    // getAgreeURL(type) {
    //   agreementContent({ platform: 'H5', type })
    //     .then(res => {
    //       const { path, clauseName } = res.data
    //       path && (this.agreeInfo[type] = { name: clauseName, path })
    //     })
    // }
  },
}
</script>

<style lang="scss" scoped>
.mgt-70 {
  margin-top: 70px;
}
.pdb-30 {
  padding-bottom: 30px;
}
.risk-container {
  padding-top: 118px;
  padding-bottom: 160px;
}
.card {
  position: relative;
  padding: 0 30px;
}
.content {
  margin-bottom: 20px;
  @include font_color(text-color);
  font-size: 28px;
  line-height: 40px;
  font-weight: 400;
}
.description {
  @include background_color(list-bg);
  padding: 20px;
  @include font_color(second-text);
  line-height: 34px;
  font-size: 28px;
  font-family: Helvetica;
}
.agree {
  padding: 30px 30px 0;
}
.agree.pdb-30 {
  padding-bottom: 30px;
}
.link-text {
  color: #2D60E0;
}
::v-deep.risk-footer {
  @include thin_border($directions: top, $color: line-color, $position: before);
}
.lang-en.risk-container {
  ::v-deep.nav-header-container {
    .title {
      padding-left: 208px;

      span {
        max-width: calc(100% - 30px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>