const path = require('path')
const webpack = require('webpack')
// const createThemeColorReplacerPlugin = require('./config/plugin.config')
// const theme = require('./theme.js')
function resolve(dir) {
  return path.join(__dirname, dir)
}

const isProd = process.env.NODE_ENV === 'production'

const assetsCDN = {
  // webpack build externals
  externals: {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    vuex: 'Vuex',
    axios: 'axios'
  },
  css: [],
  // https://unpkg.com/browse/vue@2.6.10/
  js: [
    'https://download.eddidapp.com/js/<EMAIL>',
    'https://download.eddidapp.com/js/vue-router.min.js',
    'https://download.eddidapp.com/js/vuex.min.js',
    'https://download.eddidapp.com/js/axios.min.js'
  ]
}
let timeStamp = new Date().getTime();
// vue.config.js
const vueConfig = {
  filenameHashing: true,

  configureWebpack: {
    // webpack plugins
    plugins: [
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/)
    ],
    // if prod, add externals
    externals: isProd ? assetsCDN.externals : {},
    output: { // 输出重构 打包编译后的js文件名称,添加时间戳.
      filename: `js[name].${timeStamp}.js`,
      chunkFilename: `js[name].${timeStamp}.js`,
    }
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@$', resolve('src'))

    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .oneOf('inline')
      .resourceQuery(/inline/)
      .use('vue-svg-icon-loader')
      .loader('vue-svg-icon-loader')
      .end()
      .end()
      .oneOf('external')
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'assets/[name].[hash:8].[ext]'
      })
      config.plugins.delete('prefetch');
    // if prod is on
    // assets require on cdn
    
    if (isProd) {
      config.plugin('html').tap(args => {
        args[0].cdn = assetsCDN
        return args
      })
    }
  },

  css: {
    loaderOptions: {
      sass: {
        prependData: `@import "./src/css/variable.scss";@import "./src/css/themeify.scss";@import "./src/css/border.scss";@import "./src/css/main.scss";@import "./src/css/international/variable.scss";@import "./src/css/international/main.scss";`,
      },
    },
    extract: { // 打包后css文件名称添加时间戳
      filename: `css/[name].${timeStamp}.css`,
      chunkFilename: `css/[name].${timeStamp}.css`,
    }
  },
  devServer: {
    // development server port 8000
    port: 8081,
    // If you want to turn on the proxy, please remove the mockjs /src/main.jsL11
    // proxy: 'https://operation-center-develop.eddidapp.com:1443',
    hot: true
  },

  // disable source map in production
  productionSourceMap: false,
  lintOnSave: undefined,
  // babel-loader no-ignore node_modules/*
  transpileDependencies: []
}

// preview.pro.loacg.com only do not use in your production;


module.exports = vueConfig
